// 配置管理模块
// 负责应用配置的加载、保存和管理

use crate::error::{AppError, Result};
use directories::ProjectDirs;
use serde::{Deserialize, Serialize};
use std::path::PathBuf;
use tokio::fs;
use tracing::{info, warn, error};

/// 应用配置结构
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AppConfig {
    /// 应用基本配置
    pub app: AppSettings,
    
    /// 聊天配置
    pub chat: ChatConfig,
    
    /// 知识库配置
    pub knowledge: KnowledgeConfig,
    
    /// 模型配置
    pub model: ModelConfig,
    
    /// 网络配置
    pub network: NetworkConfig,
    
    /// 多模态配置
    pub multimodal: MultimodalConfig,
    
    /// 系统配置
    pub system: SystemConfig,
}

/// 应用基本设置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AppSettings {
    /// 应用版本
    pub version: String,
    
    /// 语言设置
    pub language: String,
    
    /// 主题设置
    pub theme: String,
    
    /// 自动启动
    pub auto_start: bool,
    
    /// 最小化到托盘
    pub minimize_to_tray: bool,
    
    /// 启用通知
    pub enable_notifications: bool,
    
    /// 启用声音
    pub enable_sounds: bool,
    
    /// 数据目录
    pub data_dir: PathBuf,
    
    /// 日志级别
    pub log_level: String,
}

/// 聊天配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ChatConfig {
    /// 默认模型
    pub default_model: String,
    
    /// 默认温度
    pub default_temperature: f32,
    
    /// 默认最大令牌数
    pub default_max_tokens: u32,
    
    /// 启用流式响应
    pub enable_streaming: bool,
    
    /// 启用RAG
    pub enable_rag: bool,
    
    /// 默认知识库
    pub default_knowledge_bases: Vec<String>,
    
    /// 会话保存天数
    pub session_retention_days: u32,
    
    /// 自动保存间隔（秒）
    pub auto_save_interval: u32,
}

/// 知识库配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct KnowledgeConfig {
    /// 默认嵌入模型
    pub default_embedding_model: String,
    
    /// 默认分块大小
    pub default_chunk_size: usize,
    
    /// 默认分块重叠
    pub default_chunk_overlap: usize,
    
    /// 向量数据库URL
    pub vector_db_url: String,
    
    /// 支持的文件类型
    pub supported_file_types: Vec<String>,
    
    /// 最大文件大小（MB）
    pub max_file_size_mb: u64,
    
    /// 自动备份间隔（小时）
    pub auto_backup_interval_hours: u32,
}

/// 模型配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ModelConfig {
    /// 模型存储目录
    pub models_dir: PathBuf,
    
    /// 默认量化类型
    pub default_quantization: String,
    
    /// 最大并发加载数
    pub max_concurrent_loads: u32,
    
    /// 模型缓存大小（GB）
    pub model_cache_size_gb: u32,
    
    /// 自动卸载闲置模型
    pub auto_unload_idle_models: bool,
    
    /// 闲置超时时间（分钟）
    pub idle_timeout_minutes: u32,
    
    /// GPU使用优先级
    pub gpu_priority: bool,
}

/// 网络配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct NetworkConfig {
    /// 启用设备发现
    pub enable_discovery: bool,
    
    /// 发现端口
    pub discovery_port: u16,
    
    /// 发现间隔（秒）
    pub discovery_interval_seconds: u32,
    
    /// 最大连接数
    pub max_connections: u32,
    
    /// 连接超时（秒）
    pub connection_timeout_seconds: u32,
    
    /// 传输块大小（KB）
    pub transfer_chunk_size_kb: u32,
    
    /// 启用加密
    pub enable_encryption: bool,
    
    /// 信任的设备列表
    pub trusted_devices: Vec<String>,
}

/// 多模态配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MultimodalConfig {
    /// 启用OCR
    pub enable_ocr: bool,
    
    /// OCR语言
    pub ocr_languages: Vec<String>,
    
    /// 启用TTS
    pub enable_tts: bool,
    
    /// TTS语音
    pub tts_voice: String,
    
    /// 启用STT
    pub enable_stt: bool,
    
    /// STT语言
    pub stt_language: String,
    
    /// 图像处理质量
    pub image_quality: String,
    
    /// 音频采样率
    pub audio_sample_rate: u32,
    
    /// 视频处理分辨率
    pub video_resolution: String,
}

/// 系统配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SystemConfig {
    /// 启用硬件加速
    pub enable_hardware_acceleration: bool,
    
    /// 启用调试模式
    pub enable_debug_mode: bool,
    
    /// 启用性能监控
    pub enable_performance_monitoring: bool,
    
    /// 监控间隔（秒）
    pub monitoring_interval_seconds: u32,
    
    /// 启用自动更新
    pub enable_auto_updates: bool,
    
    /// 更新检查间隔（小时）
    pub update_check_interval_hours: u32,
    
    /// 启用崩溃报告
    pub enable_crash_reports: bool,
    
    /// 启用使用统计
    pub enable_usage_analytics: bool,
}

impl Default for AppConfig {
    fn default() -> Self {
        let project_dirs = ProjectDirs::from("com", "ai-studio", "AI Studio")
            .expect("无法获取项目目录");
        
        let data_dir = project_dirs.data_dir().to_path_buf();
        let models_dir = data_dir.join("models");

        Self {
            app: AppSettings {
                version: env!("CARGO_PKG_VERSION").to_string(),
                language: "zh-CN".to_string(),
                theme: "auto".to_string(),
                auto_start: false,
                minimize_to_tray: true,
                enable_notifications: true,
                enable_sounds: true,
                data_dir: data_dir.clone(),
                log_level: "info".to_string(),
            },
            chat: ChatConfig {
                default_model: "".to_string(),
                default_temperature: 0.7,
                default_max_tokens: 2048,
                enable_streaming: true,
                enable_rag: true,
                default_knowledge_bases: vec![],
                session_retention_days: 30,
                auto_save_interval: 30,
            },
            knowledge: KnowledgeConfig {
                default_embedding_model: "text-embedding-ada-002".to_string(),
                default_chunk_size: 1000,
                default_chunk_overlap: 200,
                vector_db_url: "http://localhost:8000".to_string(),
                supported_file_types: vec![
                    "pdf".to_string(),
                    "docx".to_string(),
                    "txt".to_string(),
                    "md".to_string(),
                    "html".to_string(),
                ],
                max_file_size_mb: 100,
                auto_backup_interval_hours: 24,
            },
            model: ModelConfig {
                models_dir,
                default_quantization: "q4_0".to_string(),
                max_concurrent_loads: 2,
                model_cache_size_gb: 8,
                auto_unload_idle_models: true,
                idle_timeout_minutes: 30,
                gpu_priority: true,
            },
            network: NetworkConfig {
                enable_discovery: true,
                discovery_port: 8080,
                discovery_interval_seconds: 30,
                max_connections: 10,
                connection_timeout_seconds: 30,
                transfer_chunk_size_kb: 1024,
                enable_encryption: true,
                trusted_devices: vec![],
            },
            multimodal: MultimodalConfig {
                enable_ocr: true,
                ocr_languages: vec!["zh".to_string(), "en".to_string()],
                enable_tts: true,
                tts_voice: "zh-CN-XiaoxiaoNeural".to_string(),
                enable_stt: true,
                stt_language: "zh-CN".to_string(),
                image_quality: "high".to_string(),
                audio_sample_rate: 16000,
                video_resolution: "720p".to_string(),
            },
            system: SystemConfig {
                enable_hardware_acceleration: true,
                enable_debug_mode: false,
                enable_performance_monitoring: true,
                monitoring_interval_seconds: 60,
                enable_auto_updates: true,
                update_check_interval_hours: 24,
                enable_crash_reports: true,
                enable_usage_analytics: false,
            },
        }
    }
}

impl AppConfig {
    /// 获取配置文件路径
    pub fn config_path() -> Result<PathBuf> {
        let project_dirs = ProjectDirs::from("com", "ai-studio", "AI Studio")
            .ok_or_else(|| AppError::Config("无法获取项目目录".to_string()))?;
        
        let config_dir = project_dirs.config_dir();
        Ok(config_dir.join("config.toml"))
    }

    /// 加载配置
    pub async fn load() -> Result<Self> {
        let config_path = Self::config_path()?;
        
        if !config_path.exists() {
            info!("配置文件不存在，创建默认配置: {:?}", config_path);
            let default_config = Self::default();
            default_config.save().await?;
            return Ok(default_config);
        }

        match fs::read_to_string(&config_path).await {
            Ok(content) => {
                match toml::from_str::<Self>(&content) {
                    Ok(config) => {
                        info!("配置加载成功: {:?}", config_path);
                        Ok(config)
                    }
                    Err(e) => {
                        warn!("配置文件解析失败，使用默认配置: {}", e);
                        let default_config = Self::default();
                        default_config.save().await?;
                        Ok(default_config)
                    }
                }
            }
            Err(e) => {
                error!("读取配置文件失败: {}", e);
                Err(AppError::Config(format!("读取配置文件失败: {}", e)))
            }
        }
    }

    /// 保存配置
    pub async fn save(&self) -> Result<()> {
        let config_path = Self::config_path()?;
        
        // 确保配置目录存在
        if let Some(parent) = config_path.parent() {
            fs::create_dir_all(parent).await?;
        }

        let content = toml::to_string_pretty(self)
            .map_err(|e| AppError::Config(format!("序列化配置失败: {}", e)))?;

        fs::write(&config_path, content).await?;
        info!("配置保存成功: {:?}", config_path);
        Ok(())
    }

    /// 重置为默认配置
    pub async fn reset() -> Result<Self> {
        let default_config = Self::default();
        default_config.save().await?;
        info!("配置已重置为默认值");
        Ok(default_config)
    }

    /// 验证配置
    pub fn validate(&self) -> Result<()> {
        // 验证数据目录
        if !self.app.data_dir.exists() {
            return Err(AppError::Config(format!(
                "数据目录不存在: {:?}",
                self.app.data_dir
            )));
        }

        // 验证模型目录
        if !self.model.models_dir.exists() {
            return Err(AppError::Config(format!(
                "模型目录不存在: {:?}",
                self.model.models_dir
            )));
        }

        // 验证端口范围
        if self.network.discovery_port == 0 {
            return Err(AppError::Config("发现端口不能为0".to_string()));
        }

        info!("配置验证通过");
        Ok(())
    }

    /// 创建必要的目录
    pub async fn create_directories(&self) -> Result<()> {
        let dirs = vec![
            &self.app.data_dir,
            &self.model.models_dir,
            &self.app.data_dir.join("knowledge"),
            &self.app.data_dir.join("chat"),
            &self.app.data_dir.join("logs"),
            &self.app.data_dir.join("temp"),
        ];

        for dir in dirs {
            if !dir.exists() {
                fs::create_dir_all(dir).await?;
                info!("创建目录: {:?}", dir);
            }
        }

        Ok(())
    }
}
