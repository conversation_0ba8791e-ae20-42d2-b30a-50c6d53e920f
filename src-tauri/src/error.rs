// 错误处理模块
// 定义应用中使用的错误类型和处理逻辑

use serde::{Deserialize, Serialize};
use std::fmt;
use thiserror::Error;

/// 应用错误类型
#[derive(Error, Debug, Clone, Serialize, Deserialize)]
pub enum AppError {
    /// 数据库错误
    #[error("数据库错误: {0}")]
    Database(String),

    /// 网络错误
    #[error("网络错误: {0}")]
    Network(String),

    /// 文件系统错误
    #[error("文件系统错误: {0}")]
    FileSystem(String),

    /// 配置错误
    #[error("配置错误: {0}")]
    Config(String),

    /// 序列化/反序列化错误
    #[error("序列化错误: {0}")]
    Serialization(String),

    /// 验证错误
    #[error("验证错误: {0}")]
    Validation(String),

    /// 权限错误
    #[error("权限错误: {0}")]
    Permission(String),

    /// 资源未找到错误
    #[error("资源未找到: {0}")]
    NotFound(String),

    /// 资源已存在错误
    #[error("资源已存在: {0}")]
    AlreadyExists(String),

    /// 操作超时错误
    #[error("操作超时: {0}")]
    Timeout(String),

    /// 外部服务错误
    #[error("外部服务错误: {0}")]
    ExternalService(String),

    /// 模型相关错误
    #[error("模型错误: {0}")]
    Model(String),

    /// 知识库相关错误
    #[error("知识库错误: {0}")]
    Knowledge(String),

    /// 聊天相关错误
    #[error("聊天错误: {0}")]
    Chat(String),

    /// 多模态处理错误
    #[error("多模态处理错误: {0}")]
    Multimodal(String),

    /// 插件相关错误
    #[error("插件错误: {0}")]
    Plugin(String),

    /// 窗口相关错误
    #[error("窗口错误: {0}")]
    WindowError(String),

    /// 窗口未找到错误
    #[error("窗口未找到: {0}")]
    WindowNotFound(String),

    /// 系统错误
    #[error("系统错误: {0}")]
    System(String),

    /// 内部错误
    #[error("内部错误: {0}")]
    Internal(String),

    /// 未知错误
    #[error("未知错误: {0}")]
    Unknown(String),
}

/// 应用结果类型
pub type Result<T> = std::result::Result<T, AppError>;

/// API响应结构
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ApiResponse<T> {
    pub success: bool,
    pub data: Option<T>,
    pub message: Option<String>,
    pub code: Option<u32>,
    pub timestamp: i64,
}

impl<T> ApiResponse<T> {
    /// 创建成功响应
    pub fn success(data: T) -> Self {
        Self {
            success: true,
            data: Some(data),
            message: None,
            code: None,
            timestamp: chrono::Utc::now().timestamp(),
        }
    }

    /// 创建成功响应（带消息）
    pub fn success_with_message(data: T, message: String) -> Self {
        Self {
            success: true,
            data: Some(data),
            message: Some(message),
            code: None,
            timestamp: chrono::Utc::now().timestamp(),
        }
    }

    /// 创建错误响应
    pub fn error(message: String) -> ApiResponse<()> {
        ApiResponse {
            success: false,
            data: None,
            message: Some(message),
            code: None,
            timestamp: chrono::Utc::now().timestamp(),
        }
    }

    /// 创建错误响应（带错误码）
    pub fn error_with_code(message: String, code: u32) -> ApiResponse<()> {
        ApiResponse {
            success: false,
            data: None,
            message: Some(message),
            code: Some(code),
            timestamp: chrono::Utc::now().timestamp(),
        }
    }
}

/// 从 AppError 转换为 ApiResponse
impl<T> From<AppError> for ApiResponse<T> {
    fn from(error: AppError) -> Self {
        ApiResponse {
            success: false,
            data: None,
            message: Some(error.to_string()),
            code: Some(error.error_code()),
            timestamp: chrono::Utc::now().timestamp(),
        }
    }
}

impl AppError {
    /// 获取错误码
    pub fn error_code(&self) -> u32 {
        match self {
            AppError::Database(_) => 1001,
            AppError::Network(_) => 1002,
            AppError::FileSystem(_) => 1003,
            AppError::Config(_) => 1004,
            AppError::Serialization(_) => 1005,
            AppError::Validation(_) => 1006,
            AppError::Permission(_) => 1007,
            AppError::NotFound(_) => 1008,
            AppError::AlreadyExists(_) => 1009,
            AppError::Timeout(_) => 1010,
            AppError::ExternalService(_) => 1011,
            AppError::Model(_) => 2001,
            AppError::Knowledge(_) => 2002,
            AppError::Chat(_) => 2003,
            AppError::Multimodal(_) => 2004,
            AppError::Plugin(_) => 2005,
            AppError::WindowError(_) => 3001,
            AppError::WindowNotFound(_) => 3002,
            AppError::System(_) => 3003,
            AppError::Internal(_) => 9001,
            AppError::Unknown(_) => 9999,
        }
    }

    /// 检查是否为可重试的错误
    pub fn is_retryable(&self) -> bool {
        matches!(
            self,
            AppError::Network(_) | AppError::Timeout(_) | AppError::ExternalService(_)
        )
    }

    /// 检查是否为用户错误
    pub fn is_user_error(&self) -> bool {
        matches!(
            self,
            AppError::Validation(_) | AppError::Permission(_) | AppError::NotFound(_)
        )
    }
}

/// 从标准库错误转换
impl From<std::io::Error> for AppError {
    fn from(error: std::io::Error) -> Self {
        AppError::FileSystem(error.to_string())
    }
}

impl From<serde_json::Error> for AppError {
    fn from(error: serde_json::Error) -> Self {
        AppError::Serialization(error.to_string())
    }
}

impl From<sqlx::Error> for AppError {
    fn from(error: sqlx::Error) -> Self {
        AppError::Database(error.to_string())
    }
}

impl From<reqwest::Error> for AppError {
    fn from(error: reqwest::Error) -> Self {
        AppError::Network(error.to_string())
    }
}

impl From<config::ConfigError> for AppError {
    fn from(error: config::ConfigError) -> Self {
        AppError::Config(error.to_string())
    }
}

impl From<tokio::time::error::Elapsed> for AppError {
    fn from(error: tokio::time::error::Elapsed) -> Self {
        AppError::Timeout(error.to_string())
    }
}

/// 错误处理工具
pub struct ErrorHandler;

impl ErrorHandler {
    /// 记录错误日志
    pub fn log_error(error: &AppError) {
        match error {
            AppError::Internal(_) | AppError::System(_) => {
                tracing::error!("严重错误: {}", error);
            }
            AppError::Database(_) | AppError::Network(_) => {
                tracing::warn!("服务错误: {}", error);
            }
            _ => {
                tracing::info!("用户错误: {}", error);
            }
        }
    }

    /// 处理错误并返回响应
    pub fn handle_error<T>(error: AppError) -> ApiResponse<T> {
        Self::log_error(&error);
        error.into()
    }

    /// 包装结果
    pub fn wrap_result<T>(result: Result<T>) -> ApiResponse<T> {
        match result {
            Ok(data) => ApiResponse::success(data),
            Err(error) => Self::handle_error(error),
        }
    }
}

/// 错误处理宏
#[macro_export]
macro_rules! handle_error {
    ($result:expr) => {
        match $result {
            Ok(value) => value,
            Err(error) => {
                return $crate::error::ErrorHandler::handle_error(error.into());
            }
        }
    };
}

/// 验证宏
#[macro_export]
macro_rules! validate {
    ($condition:expr, $message:expr) => {
        if !$condition {
            return $crate::error::ErrorHandler::handle_error(
                $crate::error::AppError::Validation($message.to_string())
            );
        }
    };
}

/// 结果包装宏
#[macro_export]
macro_rules! wrap_result {
    ($result:expr) => {
        $crate::error::ErrorHandler::wrap_result($result)
    };
}
