// AI Studio 主程序入口
// 基于 Tauri 2.x 的 Rust 后端应用

#![cfg_attr(not(debug_assertions), windows_subsystem = "windows")]

use std::sync::Arc;
use tauri::{App, Manager, State};
use tokio::sync::Mutex;
use tracing::{info, error};

// 导入模块
mod config;
mod database;
mod services;
mod commands;
mod utils;
mod error;

// 导入服务
use services::{
    chat::ChatService,
    knowledge::KnowledgeService,
    model::ModelService,
    network::NetworkService,
    multimodal::MultimodalService,
    system::SystemService,
};

// 导入配置
use config::AppConfig;

// 导入错误类型
use error::{Result, AppError};

// 应用状态
#[derive(Debug)]
pub struct AppState {
    pub config: Arc<Mutex<AppConfig>>,
    pub chat_service: Arc<ChatService>,
    pub knowledge_service: Arc<KnowledgeService>,
    pub model_service: Arc<ModelService>,
    pub network_service: Arc<NetworkService>,
    pub multimodal_service: Arc<MultimodalService>,
    pub system_service: Arc<SystemService>,
}

impl AppState {
    /// 创建新的应用状态
    pub async fn new() -> Result<Self> {
        info!("初始化应用状态...");

        // 加载配置
        let config = Arc::new(Mutex::new(AppConfig::load().await?));
        
        // 初始化数据库
        let db_pool = database::init_database().await?;
        
        // 初始化服务
        let chat_service = Arc::new(ChatService::new(db_pool.clone()).await?);
        let knowledge_service = Arc::new(KnowledgeService::new(db_pool.clone()).await?);
        let model_service = Arc::new(ModelService::new(db_pool.clone()).await?);
        let network_service = Arc::new(NetworkService::new().await?);
        let multimodal_service = Arc::new(MultimodalService::new().await?);
        let system_service = Arc::new(SystemService::new().await?);

        info!("应用状态初始化完成");

        Ok(Self {
            config,
            chat_service,
            knowledge_service,
            model_service,
            network_service,
            multimodal_service,
            system_service,
        })
    }
}

/// 应用初始化
async fn init_app(app: &App) -> Result<()> {
    info!("开始初始化 AI Studio 应用...");

    // 设置日志
    setup_logging()?;

    // 创建应用状态
    let app_state = AppState::new().await?;
    
    // 将状态注入到 Tauri 应用中
    app.manage(app_state);

    // 设置窗口
    setup_window(app)?;

    // 启动后台服务
    start_background_services(app).await?;

    info!("AI Studio 应用初始化完成");
    Ok(())
}

/// 设置日志系统
fn setup_logging() -> Result<()> {
    use tracing_subscriber::{layer::SubscriberExt, util::SubscriberInitExt};

    tracing_subscriber::registry()
        .with(
            tracing_subscriber::EnvFilter::try_from_default_env()
                .unwrap_or_else(|_| "ai_studio=debug,tower_http=debug".into()),
        )
        .with(tracing_subscriber::fmt::layer())
        .init();

    info!("日志系统初始化完成");
    Ok(())
}

/// 设置窗口
fn setup_window(app: &App) -> Result<()> {
    let window = app.get_webview_window("main")
        .ok_or_else(|| AppError::WindowNotFound("main".to_string()))?;

    // 设置窗口图标
    #[cfg(target_os = "macos")]
    {
        use cocoa::appkit::NSWindow;
        use cocoa::base::{id, nil};
        
        let ns_window = window.ns_window().map_err(|e| AppError::WindowError(e.to_string()))?;
        unsafe {
            let title_bar_height = 28.0;
            ns_window.setTitlebarAppearsTransparent_(cocoa::base::YES);
            ns_window.setTitleVisibility_(cocoa::appkit::NSWindowTitleVisibility::NSWindowTitleHidden);
        }
    }

    // 设置窗口事件监听
    let window_clone = window.clone();
    window.on_window_event(move |event| {
        match event {
            tauri::WindowEvent::CloseRequested { api, .. } => {
                info!("窗口关闭请求");
                // 这里可以添加关闭前的清理逻辑
            }
            tauri::WindowEvent::Resized(size) => {
                info!("窗口大小改变: {:?}", size);
            }
            _ => {}
        }
    });

    info!("窗口设置完成");
    Ok(())
}

/// 启动后台服务
async fn start_background_services(app: &App) -> Result<()> {
    let app_state: State<AppState> = app.state();

    // 启动网络发现服务
    app_state.network_service.start_discovery().await?;

    // 启动系统监控服务
    app_state.system_service.start_monitoring().await?;

    info!("后台服务启动完成");
    Ok(())
}

/// 应用退出清理
async fn cleanup_app(app_state: &AppState) -> Result<()> {
    info!("开始清理应用资源...");

    // 停止网络服务
    app_state.network_service.stop_discovery().await?;

    // 停止系统监控
    app_state.system_service.stop_monitoring().await?;

    // 保存配置
    let config = app_state.config.lock().await;
    config.save().await?;

    info!("应用资源清理完成");
    Ok(())
}

/// 主函数
#[tokio::main]
async fn main() {
    // 构建 Tauri 应用
    let app_result = tauri::Builder::default()
        // 注册插件
        .plugin(tauri_plugin_shell::init())
        .plugin(tauri_plugin_fs::init())
        .plugin(tauri_plugin_http::init())
        .plugin(tauri_plugin_dialog::init())
        .plugin(tauri_plugin_notification::init())
        .plugin(tauri_plugin_window_state::Builder::default().build())
        .plugin(tauri_plugin_updater::Builder::new().build())
        
        // 注册命令
        .invoke_handler(tauri::generate_handler![
            // 系统命令
            commands::system::get_system_info,
            commands::system::get_app_version,
            commands::system::check_for_updates,
            commands::system::restart_app,
            commands::system::exit_app,
            
            // 聊天命令
            commands::chat::get_chat_sessions,
            commands::chat::create_chat_session,
            commands::chat::send_chat_message,
            commands::chat::start_stream_chat,
            commands::chat::stop_stream_chat,
            
            // 知识库命令
            commands::knowledge::get_knowledge_bases,
            commands::knowledge::create_knowledge_base,
            commands::knowledge::upload_document,
            commands::knowledge::search_knowledge,
            
            // 模型命令
            commands::model::get_local_models,
            commands::model::download_model,
            commands::model::load_model,
            commands::model::unload_model,
            
            // 网络命令
            commands::network::get_network_nodes,
            commands::network::connect_to_node,
            commands::network::share_resource,
            commands::network::start_file_transfer,
            
            // 多模态命令
            commands::multimodal::process_image,
            commands::multimodal::process_audio,
            commands::multimodal::process_video,
            commands::multimodal::ocr_image,
        ])
        
        // 设置应用初始化
        .setup(|app| {
            // 使用 tokio 运行异步初始化
            let app_handle = app.handle().clone();
            tauri::async_runtime::spawn(async move {
                if let Err(e) = init_app(&app_handle).await {
                    error!("应用初始化失败: {}", e);
                    std::process::exit(1);
                }
            });
            Ok(())
        })
        
        // 构建应用
        .build(tauri::generate_context!());

    // 运行应用
    match app_result {
        Ok(app) => {
            info!("AI Studio 应用启动成功");
            
            // 运行应用
            app.run(|app_handle, event| {
                match event {
                    tauri::RunEvent::ExitRequested { api, .. } => {
                        info!("应用退出请求");
                        
                        // 异步清理资源
                        let app_state: State<AppState> = app_handle.state();
                        tauri::async_runtime::spawn(async move {
                            if let Err(e) = cleanup_app(&app_state).await {
                                error!("应用清理失败: {}", e);
                            }
                        });
                    }
                    _ => {}
                }
            });
        }
        Err(e) => {
            error!("AI Studio 应用启动失败: {}", e);
            std::process::exit(1);
        }
    }
}
