{"$schema": "https://schema.tauri.app/config/2.0.0", "productName": "AI Studio", "version": "1.0.0", "identifier": "com.ai-studio.app", "build": {"beforeDevCommand": "npm run dev", "beforeBuildCommand": "npm run build", "devUrl": "http://localhost:1420", "frontendDist": "../dist"}, "app": {"windows": [{"title": "AI Studio", "width": 1200, "height": 800, "minWidth": 800, "minHeight": 600, "resizable": true, "maximizable": true, "minimizable": true, "closable": true, "decorations": false, "transparent": false, "alwaysOnTop": false, "fullscreen": false, "skipTaskbar": false, "center": true, "titleBarStyle": "overlay", "hiddenTitle": true, "tabbingIdentifier": "ai-studio"}], "security": {"csp": "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data: https:; connect-src 'self' https: wss: ws:; media-src 'self' https:;"}, "trayIcon": {"iconPath": "icons/icon.png", "iconAsTemplate": true, "menuOnLeftClick": false, "tooltip": "AI Studio"}}, "bundle": {"active": true, "targets": "all", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"], "identifier": "com.ai-studio.app", "publisher": "AI Studio Team", "category": "Productivity", "shortDescription": "AI Studio - 智能AI助手", "longDescription": "AI Studio 是一个基于 Vue3 + TypeScript + Vite + Tauri 2.x 的中文AI助手桌面应用，提供本地大模型部署、知识库管理、局域网共享等企业级功能。", "copyright": "© 2024 AI Studio Team. All rights reserved.", "licenseFile": "../LICENSE", "resources": ["resources/*"], "externalBin": [], "windows": {"certificateThumbprint": null, "digestAlgorithm": "sha256", "timestampUrl": "", "tsp": false, "wix": {"language": ["zh-CN", "en-US"], "template": "wix/main.wxs", "fragmentPaths": ["wix/fragment.wxs"], "componentGroupRefs": ["CustomComponentGroup"], "componentRefs": ["CustomComponent"], "featureGroupRefs": ["CustomFeatureGroup"], "featureRefs": ["CustomFeature"], "mergeRefs": ["CustomMerge"], "skipWebviewInstall": false, "bannerPath": "wix/banner.bmp", "dialogImagePath": "wix/dialog.bmp"}, "nsis": {"template": "nsis/installer.nsi", "languages": ["SimpChinese", "English"], "displayLanguageSelector": true, "installerIcon": "icons/icon.ico", "installMode": "perMachine", "allowDowngrades": false, "deleteAppDataOnUninstall": false}}, "macOS": {"frameworks": [], "minimumSystemVersion": "10.13", "exceptionDomain": "", "signingIdentity": null, "providerShortName": null, "entitlements": "entitlements.plist", "dmg": {"appPosition": {"x": 180, "y": 170}, "applicationFolderPosition": {"x": 480, "y": 170}, "windowSize": {"width": 660, "height": 400}}}, "linux": {"deb": {"depends": ["libwebkit2gtk-4.0-37", "libgtk-3-0", "libayatana-appindicator3-1"], "desktopTemplate": "linux/ai-studio.desktop"}, "appimage": {"bundleMediaFramework": false, "libs": ["libwebkit2gtk-4.0-37", "libgtk-3-0"]}}}, "plugins": {"shell": {"open": true, "scope": [{"name": "open-url", "cmd": "open", "args": ["$URL"]}]}, "fs": {"scope": ["$APPDATA", "$APPDATA/**", "$DOCUMENT", "$DOCUMENT/**", "$DOWNLOAD", "$DOWNLOAD/**", "$DESKTOP", "$DESKTOP/**"]}, "http": {"scope": ["https://**", "http://localhost:**"]}, "dialog": {"open": true, "save": true, "message": true, "ask": true, "confirm": true}, "notification": {"all": true}, "window-state": {"filename": "window-state.json"}, "updater": {"active": true, "endpoints": ["https://api.ai-studio.com/updates/{{target}}/{{current_version}}"], "dialog": true, "pubkey": "dW50cnVzdGVkIGNvbW1lbnQ6IG1pbmlzaWduIHB1YmxpYyBrZXk6IDhEQzY4M0ZGNjI4QjY4NjAKUldSWTRKaFRuVGlHWXpEa1JUeXF5UGFQYUxWdGRaenBuaUNSM2VhT0hGcjNWUjJVOXNlNzlOZgo="}}}