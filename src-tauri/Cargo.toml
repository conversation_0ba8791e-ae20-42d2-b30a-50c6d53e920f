[package]
name = "ai-studio"
version = "1.0.0"
description = "AI Studio - 基于 Vue3 + TypeScript + Vite + Tauri 2.x 的中文AI助手桌面应用"
authors = ["AI Studio Team"]
license = "MIT"
repository = "https://github.com/ai-studio/ai-studio"
edition = "2021"

# 构建配置
[build-dependencies]
tauri-build = { version = "2.0", features = [] }

# 依赖配置
[dependencies]
# Tauri 核心依赖
tauri = { version = "2.0", features = [
    "macos-private-api",
    "unstable"
] }
tauri-plugin-shell = "2.0"
tauri-plugin-fs = "2.0"
tauri-plugin-http = "2.0"
tauri-plugin-dialog = "2.0"
tauri-plugin-notification = "2.0"
tauri-plugin-window-state = "2.0"
tauri-plugin-updater = "2.0"

# 异步运行时
tokio = { version = "1.0", features = ["full"] }
futures = "0.3"

# 序列化和反序列化
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
toml = "0.8"

# 数据库
sqlx = { version = "0.7", features = [
    "runtime-tokio-rustls",
    "sqlite",
    "chrono",
    "uuid"
] }

# 向量数据库 (ChromaDB 客户端)
reqwest = { version = "0.11", features = ["json", "stream"] }
uuid = { version = "1.0", features = ["v4", "serde"] }

# 时间处理
chrono = { version = "0.4", features = ["serde"] }

# 错误处理
anyhow = "1.0"
thiserror = "1.0"

# 日志
log = "0.4"
env_logger = "0.10"
tracing = "0.1"
tracing-subscriber = "0.3"

# 文件系统操作
walkdir = "2.0"
mime_guess = "2.0"

# 网络和HTTP
hyper = { version = "0.14", features = ["full"] }
tower = "0.4"
tower-http = { version = "0.4", features = ["cors", "fs"] }

# 加密和安全
sha2 = "0.10"
aes-gcm = "0.10"
rand = "0.8"

# 系统信息
sysinfo = "0.29"

# 配置管理
config = "0.13"
directories = "5.0"

# 多模态处理
image = "0.24"
opencv = { version = "0.88", optional = true }

# 音频处理
rodio = { version = "0.17", optional = true }
hound = { version = "3.5", optional = true }

# 文档处理
pdf-extract = { version = "0.7", optional = true }
docx-rs = { version = "0.4", optional = true }

# 网络发现 (mDNS)
mdns-sd = "0.7"

# 插件系统 (WASM)
wasmtime = { version = "13.0", optional = true }
wasmtime-wasi = { version = "13.0", optional = true }

# 特性配置
[features]
default = [
    "multimodal",
    "document-processing",
    "audio-processing",
    "plugin-system"
]

# 多模态处理特性
multimodal = ["opencv"]

# 文档处理特性
document-processing = ["pdf-extract", "docx-rs"]

# 音频处理特性
audio-processing = ["rodio", "hound"]

# 插件系统特性
plugin-system = ["wasmtime", "wasmtime-wasi"]

# 目标配置
[target."cfg(not(any(target_os = \"android\", target_os = \"ios\")))".dependencies]
tauri-plugin-updater = "2.0"

# Windows 特定依赖
[target.'cfg(target_os = "windows")'.dependencies]
winapi = { version = "0.3", features = ["winuser", "processthreadsapi"] }

# macOS 特定依赖
[target.'cfg(target_os = "macos")'.dependencies]
cocoa = "0.24"
objc = "0.2"

# Linux 特定依赖
[target.'cfg(target_os = "linux")'.dependencies]
gtk = "0.16"

# 优化配置
[profile.release]
panic = "abort"
codegen-units = 1
lto = true
opt-level = "s"
strip = true
