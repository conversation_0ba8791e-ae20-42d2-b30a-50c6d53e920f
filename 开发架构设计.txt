# AI Studio 开发架构设计文档

## 文档信息
- **项目名称**：AI Studio - 本地AI助手桌面应用
- **文档版本**：v1.0 架构设计版
- **目标平台**：Windows 和 macOS 桌面应用
- **分辨率支持**：最小800×600，默认1200×800
- **核心技术栈**：Vue3 + Vite7 + Tauri2.x + Rust + SQLite + ChromaDB + Candle + LLaMA.cpp + ONNX Runtime
- **样式技术**：Tailwind CSS + SCSS（仅桌面端适配，无移动端）
- **主题系统**：深色/浅色主题切换功能
- **国际化支持**：中文/英文双语切换
- **文档状态**：架构设计完整版
- **创建日期**：2025年1月
- **基于源文档**：开发设计文档.txt (13,315行)

---

## 📋 详细目录

### 1. 项目概述与技术架构
- [1.1 项目背景与目标](#11-项目背景与目标)
- [1.2 技术栈选型与架构](#12-技术栈选型与架构)
- [1.3 系统架构设计](#13-系统架构设计)

### 2. 前端目录结构与组件设计
- [2.1 前端目录结构详解](#21-前端目录结构详解)
- [2.2 Vue3组件设计规范](#22-vue3组件设计规范)
- [2.3 Tailwind CSS + SCSS样式方案](#23-tailwind-css--scss样式方案)

### 3. 后端目录结构与模块设计
- [3.1 Rust后端目录结构](#31-rust后端目录结构)
- [3.2 Tauri集成与命令系统](#32-tauri集成与命令系统)
- [3.3 AI推理引擎模块](#33-ai推理引擎模块)

### 4. 公共模块与工具文件
- [4.1 共享组件库](#41-共享组件库)
- [4.2 工具函数与辅助类](#42-工具函数与辅助类)
- [4.3 配置文件管理](#43-配置文件管理)

### 5. 数据库设计
- [5.1 SQLite关系型数据库](#51-sqlite关系型数据库)
- [5.2 ChromaDB向量数据库](#52-chromadb向量数据库)
- [5.3 数据库关系图](#53-数据库关系图)

### 6. 用户界面设计规范
- [6.1 组件库设计](#61-组件库设计)
- [6.2 样式指南与主题系统](#62-样式指南与主题系统)
- [6.3 国际化设计方案](#63-国际化设计方案)

### 7. 系统流程与业务逻辑
- [7.1 用户操作流程](#71-用户操作流程)
- [7.2 数据处理逻辑](#72-数据处理逻辑)
- [7.3 AI推理流程](#73-ai推理流程)

### 8. API接口设计
- [8.1 Tauri Invoke通信协议](#81-tauri-invoke通信协议)
- [8.2 前后端接口规范](#82-前后端接口规范)
- [8.3 错误处理与状态管理](#83-错误处理与状态管理)

### 9. 错误处理机制
- [9.1 异常捕获策略](#91-异常捕获策略)
- [9.2 用户提示系统](#92-用户提示系统)
- [9.3 日志记录机制](#93-日志记录机制)

### 10. 性能优化策略
- [10.1 内存管理优化](#101-内存管理优化)
- [10.2 数据库性能优化](#102-数据库性能优化)
- [10.3 UI渲染优化](#103-ui渲染优化)

### 11. 整体架构设计
- [11.1 系统架构图](#111-系统架构图)
- [11.2 数据流设计](#112-数据流设计)
- [11.3 模块交互图](#113-模块交互图)

---

## 1. 项目概述与技术架构

### 1.1 项目背景与目标

#### 1.1.1 项目背景

AI Studio 是一个基于 Vue3 + TypeScript + Vite + Tauri 2.x 技术栈开发的中文AI助手桌面应用，专为 Windows/macOS 平台设计。在数据隐私日益重要的今天，用户需要一个既强大又安全的AI工具，能够在不依赖云服务的情况下处理敏感信息。

**市场需求分析：**
随着人工智能技术的快速发展，用户对AI助手的需求日益增长，但现有解决方案存在以下问题：

**隐私安全问题**
- 云端AI服务存在数据泄露风险
- 敏感信息可能被第三方服务提供商访问
- 企业内部数据无法保证完全隔离
- 跨境数据传输的合规风险

**网络依赖问题**
- 需要稳定的网络连接才能使用
- 网络延迟影响用户体验
- 离线环境无法正常工作
- 网络费用和流量限制

**功能局限问题**
- 云端服务功能相对固化，难以定制
- 无法集成企业内部系统和数据
- 缺乏本地化的知识库管理能力
- 多模态处理能力有限

AI Studio 通过本地化部署完美解决了这些痛点，为用户提供安全、可控、高效的AI助手解决方案。

#### 1.1.2 核心目标

**主要目标：**
- **本地化部署**：支持本地大模型推理，无需依赖云端服务
- **知识库管理**：提供文档解析、向量搜索、RAG增强等功能
- **局域网协作**：实现设备间模型、知识库、配置的共享
- **多模态支持**：集成OCR、TTS、语音识别等多媒体处理能力
- **企业级质量**：提供生产环境可用的稳定性和性能
- **插件生态**：支持第三方插件扩展和云端模型API集成

**技术目标：**
- **高性能**：利用Rust的性能优势，实现快速AI推理，优化内存使用
- **安全性**：本地数据处理，数据加密，权限控制，保护用户隐私
- **可扩展性**：模块化设计，插件系统，支持功能扩展和定制
- **易用性**：现代化UI设计，直观操作流程，简化用户学习成本
- **稳定性**：完善的错误处理和恢复机制，生产级质量保证
- **跨平台**：Windows和macOS统一体验，适配不同硬件配置

### 1.2 技术栈选型与架构

#### 1.2.1 技术选型原则

AI Studio 采用现代化的技术栈，确保应用的性能、可维护性和扩展性。技术选型遵循以下原则：
- **成熟稳定**：选择经过生产环境验证的技术
- **性能优先**：优先考虑性能和资源消耗
- **开发效率**：提高开发效率和代码质量
- **社区支持**：选择有活跃社区支持的技术
- **未来兼容**：考虑技术的发展趋势和兼容性

#### 1.2.2 前端技术栈

**核心框架：**

**Vue 3.5+ (Composition API)**
- **选择理由**：
  - Composition API提供更好的逻辑复用
  - 优秀的TypeScript支持
  - 更小的包体积和更好的性能
  - 丰富的生态系统和社区支持
- **替代方案对比**：
  - React：学习曲线较陡，生态复杂
  - Angular：过于重量级，不适合桌面应用
  - Svelte：生态相对较小，企业级支持不足

**TypeScript 5.0+**
- **选择理由**：
  - 提供静态类型检查，减少运行时错误
  - 优秀的IDE支持和代码提示
  - 更好的代码可维护性
  - 与Vue 3的完美集成
- **配置要点**：
  - 严格模式启用
  - 路径映射配置
  - 类型声明文件管理

**Tauri 2.x**
- **选择理由**：
  - Rust后端提供极佳的性能和安全性
  - 更小的应用体积（相比Electron）
  - 更低的内存占用
  - 原生系统集成能力强
  - 跨平台支持完善
- **替代方案对比**：
  - Electron：内存占用大，安全性相对较低
  - Flutter Desktop：生态相对较新
  - .NET MAUI：平台限制较多

**Vite 7.0+**
- **选择理由**：
  - 极快的开发服务器启动速度
  - 基于ESM的热更新
  - 优秀的构建性能
  - 丰富的插件生态
- **替代方案对比**：
  - Webpack：配置复杂，构建速度较慢
  - Rollup：功能相对简单
  - Parcel：生态支持不足

#### 1.2.3 后端技术栈

**核心语言和运行时：**

**Rust 1.75+**
- **选择理由**：
  - 内存安全和线程安全
  - 极佳的性能表现
  - 零成本抽象
  - 丰富的包管理生态
  - 与Tauri的完美集成
- **替代方案对比**：
  - Go：性能略低，GC开销
  - C++：内存安全问题，开发效率低
  - Node.js：性能不足，不适合计算密集型任务

**Tokio**
- **选择理由**：
  - Rust生态的异步运行时标准
  - 高性能的异步I/O
  - 丰富的异步工具集
  - 优秀的错误处理
- **核心功能**：
  - 异步任务调度
  - 网络编程支持
  - 定时器和延迟
  - 并发控制

### 1.3 系统架构设计

#### 1.3.1 整体架构图

```
┌─────────────────────────────────────────────────────────────┐
│                    AI Studio 桌面应用                        │
├─────────────────────────────────────────────────────────────┤
│                      前端层 (Vue3)                          │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │   聊天界面   │ │  知识库管理  │ │  模型管理   │ │  设置   │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │  多模态交互  │ │  远程协作   │ │  插件管理   │ │  监控   │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
├─────────────────────────────────────────────────────────────┤
│                    Tauri Bridge Layer                       │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │              IPC 通信层 (JSON-RPC)                      │ │
│  └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│                     后端层 (Rust)                           │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │  聊天服务   │ │  知识库服务  │ │  模型服务   │ │ 系统服务 │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │  AI推理引擎  │ │  网络服务   │ │  插件引擎   │ │ 安全服务 │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
├─────────────────────────────────────────────────────────────┤
│                      数据层                                 │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │   SQLite    │ │  ChromaDB   │ │  文件系统   │ │  缓存   │ │
│  │  (关系数据)  │ │  (向量数据)  │ │  (模型文件)  │ │ (内存)  │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
└─────────────────────────────────────────────────────────────┘
```

#### 1.3.2 核心模块架构

```
AI Studio Core Architecture:

┌─── 用户界面层 ───┐
│ Vue3 Components │ ← 响应式UI组件
│ Pinia Stores    │ ← 状态管理
│ Router & Guards │ ← 路由控制
└─────────────────┘
         ↕ IPC
┌─── 业务逻辑层 ───┐
│ Chat Service    │ ← 聊天会话管理
│ Knowledge Svc   │ ← 知识库管理
│ Model Service   │ ← 模型生命周期
│ Network Service │ ← P2P网络通信
│ Plugin Engine  │ ← 插件系统
└─────────────────┘
         ↕
┌─── AI推理层 ────┐
│ Inference Mgr   │ ← 推理任务调度
│ Model Cache     │ ← 模型缓存管理
│ Token Manager   │ ← 分词处理
│ Embedding Svc   │ ← 向量化服务
└─────────────────┘
         ↕
┌─── 数据持久层 ───┐
│ SQLite DB       │ ← 结构化数据
│ ChromaDB        │ ← 向量数据库
│ File System     │ ← 文件存储
│ Cache Layer     │ ← 多级缓存
└─────────────────┘
```

---

## 2. 前端目录结构与组件设计

### 2.1 前端目录结构详解

```
src/                                    # 前端源代码根目录
├── main.ts                            # 应用入口文件：初始化Vue应用、注册插件、挂载根组件、配置全局属性
├── App.vue                            # 根组件：定义应用整体布局、路由出口、全局状态监听、主题切换逻辑
├── style.css                          # 全局样式：基础CSS重置、全局变量定义、通用样式类、响应式断点
├── assets/                            # 静态资源目录
│   ├── images/                        # 图片资源
│   │   ├── icons/                     # 图标文件：SVG图标、PNG图标、功能图标、状态图标
│   │   ├── logos/                     # Logo文件：应用Logo、品牌标识、不同尺寸Logo、透明背景版本
│   │   └── backgrounds/               # 背景图片：默认背景、主题背景、装饰图案、渐变纹理
│   ├── fonts/                         # 字体文件：中文字体、英文字体、等宽字体、图标字体
│   └── styles/                        # 样式文件
│       ├── globals.scss               # 全局SCSS变量：颜色变量、尺寸变量、动画变量、媒体查询断点
│       ├── themes.scss                # 主题样式：浅色主题、深色主题、高对比度主题、自定义主题
│       └── components.scss            # 组件样式：组件基础样式、组件变体、组件状态、组件动画
```

#### 2.1.1 核心文件详细说明

**main.ts - 应用入口文件**
```typescript
// 应用初始化逻辑
import { createApp } from 'vue'
import { createPinia } from 'pinia'
import { createI18n } from 'vue-i18n'
import App from './App.vue'
import router from './router'

// 创建Vue应用实例
const app = createApp(App)

// 注册全局插件
app.use(createPinia())
app.use(router)
app.use(createI18n({
  locale: 'zh-CN',
  fallbackLocale: 'en-US',
  messages: {}
}))

// 全局属性配置
app.config.globalProperties.$THEME = 'light'
app.config.globalProperties.$PLATFORM = 'desktop'

// 挂载应用
app.mount('#app')
```

**App.vue - 根组件**
```vue
<template>
  <div id="app" :class="themeClass">
    <!-- 应用整体布局 -->
    <div class="app-container">
      <!-- 标题栏 -->
      <TitleBar />
      
      <!-- 主要内容区域 -->
      <div class="main-content">
        <!-- 侧边栏导航 -->
        <Sidebar />
        
        <!-- 路由视图 -->
        <router-view />
      </div>
      
      <!-- 状态栏 -->
      <StatusBar />
    </div>
    
    <!-- 全局组件 -->
    <GlobalNotifications />
    <GlobalModals />
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted } from 'vue'
import { useThemeStore } from '@/stores/theme'

const themeStore = useThemeStore()

const themeClass = computed(() => ({
  'theme-light': themeStore.currentTheme === 'light',
  'theme-dark': themeStore.currentTheme === 'dark'
}))

onMounted(() => {
  // 初始化主题
  themeStore.initializeTheme()
})
</script>
```

#### 2.1.2 组件目录结构详解

```
├── components/                        # 可复用组件
│   ├── common/                        # 通用组件
│   │   ├── Button.vue                 # 按钮组件：多种样式变体、尺寸规格、状态管理、点击事件处理、加载状态、禁用状态
│   │   ├── Input.vue                  # 输入框组件：文本输入、密码输入、数字输入、验证状态、错误提示、自动完成
│   │   ├── Modal.vue                  # 模态框组件：弹窗显示、遮罩层、关闭逻辑、动画效果、键盘事件、焦点管理
│   │   ├── Loading.vue                # 加载组件：旋转动画、进度条、骨架屏、加载文本、不同尺寸、全屏遮罩
│   │   ├── Toast.vue                  # 提示组件：成功提示、错误提示、警告提示、信息提示、自动消失、手动关闭
│   │   ├── Dropdown.vue               # 下拉菜单：选项列表、搜索过滤、多选支持、键盘导航、位置计算、虚拟滚动
│   │   ├── Tabs.vue                   # 标签页组件：标签切换、内容区域、动态标签、关闭功能、拖拽排序、懒加载
│   │   ├── Pagination.vue             # 分页组件：页码显示、跳转功能、每页条数、总数统计、快速跳转、响应式布局
│   │   └── VirtualList.vue            # 虚拟滚动列表：大数据渲染、动态高度、滚动优化、缓存策略、无限滚动、性能监控
│   ├── layout/                        # 布局组件
│   │   ├── Sidebar.vue                # 侧边栏：导航菜单、折叠展开、菜单项高亮、权限控制、搜索功能、自定义宽度
│   │   ├── Header.vue                 # 顶部栏：标题显示、用户信息、快捷操作、通知中心、搜索框、主题切换
│   │   ├── Footer.vue                 # 底部栏：版权信息、链接导航、状态显示、快捷操作、响应式隐藏、固定定位
│   │   ├── Navigation.vue             # 导航组件：路由导航、面包屑、返回按钮、前进后退、历史记录、快捷键支持
│   │   └── Breadcrumb.vue             # 面包屑导航：路径显示、点击跳转、动态生成、自定义分隔符、溢出处理、无障碍支持
│   ├── chat/                          # 聊天相关组件
│   │   ├── ChatContainer.vue          # 聊天容器：整体布局管理、会话切换、消息流控制、状态同步、快捷键绑定、窗口大小适配
│   │   ├── MessageList.vue            # 消息列表：消息渲染、虚拟滚动、自动滚动、消息分组、时间戳显示、加载更多历史消息
│   │   ├── MessageItem.vue            # 消息项：消息内容显示、用户头像、时间格式化、状态图标、操作菜单、复制分享功能
│   │   ├── MessageInput.vue           # 消息输入框：文本输入、多行支持、附件上传、表情选择、快捷命令、发送按钮状态
│   │   ├── SessionList.vue            # 会话列表：会话显示、搜索过滤、分组管理、拖拽排序、右键菜单、批量操作
│   │   ├── SessionItem.vue            # 会话项：会话信息、最后消息预览、未读计数、置顶标识、删除确认、重命名功能
│   │   ├── AttachmentUpload.vue       # 附件上传：文件选择、拖拽上传、进度显示、格式验证、大小限制、预览功能
│   │   ├── CodeBlock.vue              # 代码块显示：语法高亮、语言识别、复制代码、行号显示、折叠展开、主题适配
│   │   └── MarkdownRenderer.vue       # Markdown渲染：内容解析、样式渲染、链接处理、图片显示、表格支持、数学公式
│   ├── knowledge/                     # 知识库组件
│   │   ├── KnowledgeBaseList.vue      # 知识库列表：知识库展示、创建删除、搜索过滤、状态显示、统计信息、权限管理
│   │   ├── DocumentUpload.vue         # 文档上传：多文件上传、格式检测、进度跟踪、错误处理、批量操作、预处理设置
│   │   ├── DocumentList.vue           # 文档列表：文档展示、分页加载、排序筛选、批量选择、状态监控、操作菜单
│   │   ├── DocumentViewer.vue         # 文档查看器：内容预览、格式渲染、搜索高亮、页面导航、缩放控制、全屏模式
│   │   ├── SearchInterface.vue        # 搜索界面：关键词搜索、语义搜索、高级筛选、结果排序、搜索历史、保存查询
│   │   └── EmbeddingProgress.vue      # 向量化进度：处理状态、进度条、错误信息、取消操作、重试机制、完成通知
│   ├── model/                         # 模型管理组件
│   │   ├── ModelList.vue              # 模型列表：本地模型、远程模型、分类筛选、搜索功能、状态显示、批量操作
│   │   ├── ModelCard.vue              # 模型卡片：模型信息、参数展示、操作按钮、状态指示、评分显示、标签管理
│   │   ├── ModelDownload.vue          # 模型下载：下载管理、镜像选择、断点续传、速度显示、队列管理、完成通知
│   │   ├── ModelConfig.vue            # 模型配置：参数设置、性能调优、设备选择、内存管理、高级选项、配置保存
│   │   ├── DownloadProgress.vue       # 下载进度：进度显示、速度统计、剩余时间、暂停恢复、取消下载、错误重试
│   │   └── ModelMetrics.vue           # 模型性能指标：性能监控、资源使用、响应时间、吞吐量、错误率、历史趋势
│   ├── multimodal/                    # 多模态组件
│   │   ├── ImageUpload.vue            # 图片上传：图片选择、拖拽上传、格式转换、尺寸调整、预览显示、OCR识别、批量处理
│   │   ├── AudioRecorder.vue          # 音频录制：录音控制、音频可视化、格式选择、质量设置、实时转录、噪音抑制、文件保存
│   │   ├── VideoPlayer.vue            # 视频播放：播放控制、进度条、音量调节、全屏模式、字幕显示、倍速播放、截图功能
│   │   ├── FilePreview.vue            # 文件预览：多格式支持、内容渲染、缩放控制、页面导航、搜索功能、下载链接、分享选项
│   │   └── MediaGallery.vue           # 媒体画廊：缩略图展示、大图预览、幻灯片模式、分类筛选、搜索功能、批量操作、元数据显示
│   ├── network/                       # 网络功能组件
│   │   ├── DeviceList.vue             # 设备列表：设备发现、连接状态、设备信息、操作菜单、刷新功能、连接历史、信任管理
│   │   ├── ConnectionStatus.vue       # 连接状态：网络状态、连接质量、延迟显示、带宽监控、错误提示、重连按钮、诊断工具
│   │   ├── ResourceSharing.vue        # 资源共享：共享设置、权限管理、文件列表、访问控制、传输记录、安全设置、同步状态
│   │   ├── TransferProgress.vue       # 传输进度：传输列表、进度显示、速度统计、暂停恢复、取消传输、错误处理、完成通知
│   │   └── NetworkSettings.vue        # 网络设置：连接配置、端口设置、安全选项、代理配置、带宽限制、日志记录、诊断测试
│   ├── plugins/                       # 插件系统组件
│   │   ├── PluginList.vue             # 插件列表：已安装插件、状态显示、启用禁用、更新检查、卸载功能、依赖管理、性能监控
│   │   ├── PluginCard.vue             # 插件卡片：插件信息、版本显示、评分评论、安装按钮、权限说明、截图预览、兼容性检查
│   │   ├── PluginConfig.vue           # 插件配置：参数设置、配置验证、重置选项、导入导出、实时预览、帮助文档、错误提示
│   │   ├── PluginStore.vue            # 插件商店：插件浏览、分类筛选、搜索功能、排序选项、推荐算法、下载统计、用户评价
│   │   └── PluginDeveloper.vue        # 插件开发工具：代码编辑、调试控制台、API文档、测试工具、打包发布、日志查看、性能分析
│   └── settings/                      # 设置组件
│       ├── GeneralSettings.vue        # 通用设置：基础配置、启动选项、自动保存、快捷键设置、界面布局、默认行为、数据目录
│       ├── ThemeSettings.vue          # 主题设置：主题选择、颜色自定义、字体设置、界面缩放、动画效果、对比度调节、夜间模式
│       ├── LanguageSettings.vue       # 语言设置：界面语言、区域设置、日期格式、数字格式、时区配置、输入法设置、翻译选项
│       ├── ModelSettings.vue          # 模型设置：默认模型、推理参数、缓存设置、性能优化、硬件加速、内存限制、并发控制
│       ├── NetworkSettings.vue        # 网络设置：代理配置、连接超时、重试次数、带宽限制、安全证书、防火墙设置、日志级别
│       ├── PrivacySettings.vue        # 隐私设置：数据加密、访问权限、使用统计、错误报告、数据清理、匿名模式、审计日志
│       ├── AdvancedSettings.vue       # 高级设置：实验功能、调试模式、性能调优、内存管理、缓存策略、日志配置、开发者选项
│       └── AboutDialog.vue            # 关于对话框：版本信息、更新检查、许可证、致谢名单、联系方式、反馈渠道、系统信息
```

#### 2.1.3 页面视图结构

```
├── views/                             # 页面视图
│   ├── ChatView.vue                   # 聊天页面：主聊天界面、会话管理、消息流、模型切换、设置面板、快捷操作、状态同步
│   ├── KnowledgeView.vue              # 知识库页面：知识库管理、文档上传、搜索界面、向量化监控、数据统计、批量操作、导入导出
│   ├── ModelView.vue                  # 模型管理页面：模型列表、下载管理、配置界面、性能监控、版本控制、存储管理、兼容性检查
│   ├── MultimodalView.vue             # 多模态页面：多媒体处理、格式转换、预览界面、处理历史、批量操作、设置配置、结果展示
│   ├── NetworkView.vue                # 网络功能页面：设备发现、连接管理、资源共享、传输监控、网络诊断、安全设置、日志查看
│   ├── PluginView.vue                 # 插件管理页面：插件商店、已安装插件、开发工具、配置管理、更新检查、性能监控、安全审计
│   ├── SettingsView.vue               # 设置页面：分类设置、搜索功能、导入导出、重置选项、实时预览、帮助文档、变更记录
│   ├── MonitorView.vue                # 监控页面：系统监控、性能指标、资源使用、错误日志、统计图表、告警设置、历史数据
│   └── WelcomeView.vue                # 欢迎页面：引导流程、功能介绍、快速设置、示例演示、帮助链接、版本更新、用户反馈
```

#### 2.1.4 状态管理结构

```
├── stores/                            # Pinia状态管理
│   ├── index.ts                       # Store入口：Store注册、插件配置、持久化设置、开发工具、类型导出、初始化逻辑
│   ├── chat.ts                        # 聊天状态：会话列表、当前会话、消息历史、输入状态、模型配置、流式响应、错误处理
│   ├── knowledge.ts                   # 知识库状态：知识库列表、文档管理、搜索结果、处理状态、配置信息、统计数据、缓存管理
│   ├── model.ts                       # 模型状态：模型列表、下载队列、加载状态、配置参数、性能指标、错误信息、版本管理
│   ├── multimodal.ts                  # 多模态状态：处理队列、结果缓存、配置设置、历史记录、错误日志、进度跟踪、格式支持
│   ├── network.ts                     # 网络状态：设备列表、连接状态、传输任务、配置信息、安全设置、日志记录、性能统计
│   ├── plugin.ts                      # 插件状态：插件列表、运行状态、配置数据、权限管理、更新信息、错误日志、性能监控
│   ├── settings.ts                    # 设置状态：配置项、用户偏好、默认值、验证规则、变更历史、导入导出、重置功能
│   ├── theme.ts                       # 主题状态：当前主题、主题列表、自定义配置、切换动画、系统检测、用户偏好、缓存管理
│   ├── i18n.ts                        # 国际化状态：当前语言、语言包、翻译缓存、格式化配置、区域设置、动态加载、回退机制
│   └── system.ts                      # 系统状态：应用信息、运行状态、性能数据、错误信息、更新检查、诊断数据、日志管理
```

### 2.2 Vue3组件设计规范

#### 2.2.1 组件设计原则

**单一职责原则**
- 每个组件只负责一个特定的功能
- 组件功能边界清晰，避免功能重叠
- 便于测试和维护

**可复用性原则**
- 组件设计考虑多场景使用
- 通过props和slots提供灵活配置
- 避免硬编码，提供可配置选项

**组合优于继承**
- 使用Composition API进行逻辑复用
- 通过组合多个小组件构建复杂功能
- 避免深层次的组件继承

#### 2.2.2 组件命名规范

**组件文件命名**
```
PascalCase.vue - 使用帕斯卡命名法
例如：
- ChatContainer.vue
- MessageList.vue
- ModelCard.vue
```

**组件注册命名**
```typescript
// 全局组件注册
app.component('ChatContainer', ChatContainer)
app.component('MessageList', MessageList)

// 局部组件注册
import ChatContainer from '@/components/chat/ChatContainer.vue'
import MessageList from '@/components/chat/MessageList.vue'
```

**组件使用命名**
```vue
<template>
  <!-- 使用kebab-case -->
  <chat-container>
    <message-list />
  </chat-container>
</template>
```

#### 2.2.3 组件结构模板

**标准组件结构**
```vue
<template>
  <div class="component-name">
    <!-- 组件内容 -->
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import type { ComponentProps } from '@/types/components'

// Props定义
interface Props {
  title: string
  visible?: boolean
  size?: 'small' | 'medium' | 'large'
}

const props = withDefaults(defineProps<Props>(), {
  visible: true,
  size: 'medium'
})

// Emits定义
interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'confirm', data: any): void
  (e: 'cancel'): void
}

const emit = defineEmits<Emits>()

// 响应式数据
const isLoading = ref(false)
const formData = ref({})

// 计算属性
const componentClass = computed(() => ({
  'component-name': true,
  'component-name--small': props.size === 'small',
  'component-name--medium': props.size === 'medium',
  'component-name--large': props.size === 'large',
  'component-name--visible': props.visible
}))

// 方法
const handleConfirm = () => {
  emit('confirm', formData.value)
}

const handleCancel = () => {
  emit('cancel')
}

// 生命周期
onMounted(() => {
  // 组件挂载后的逻辑
})

// 暴露给父组件的方法
defineExpose({
  handleConfirm,
  handleCancel
})
</script>

<style lang="scss" scoped>
.component-name {
  // 组件样式

  &--small {
    // 小尺寸样式
  }

  &--medium {
    // 中等尺寸样式
  }

  &--large {
    // 大尺寸样式
  }

  &--visible {
    // 可见状态样式
  }
}
</style>
```

#### 2.2.4 路由系统设计

```
├── router/                            # 路由配置
│   ├── index.ts                       # 路由主配置：路由定义、导航守卫、权限控制、动态路由、懒加载、错误处理、历史模式
│   ├── guards.ts                      # 路由守卫：权限验证、登录检查、页面访问控制、数据预加载、标题设置、进度条、埋点统计
│   ├── routes/                        # 路由模块
│   │   ├── chat.ts                    # 聊天路由：聊天页面、会话详情、历史记录、设置页面、权限控制、参数验证、重定向逻辑
│   │   ├── knowledge.ts               # 知识库路由：知识库列表、文档管理、搜索页面、上传界面、统计报告、配置页面、权限检查
│   │   ├── model.ts                   # 模型路由：模型列表、下载管理、配置界面、监控页面、版本管理、性能分析、错误诊断
│   │   ├── multimodal.ts              # 多模态路由：处理界面、历史记录、配置页面、格式转换、批量操作、结果展示、错误处理
│   │   ├── network.ts                 # 网络路由：设备管理、连接配置、传输监控、安全设置、日志查看、诊断工具、性能统计
│   │   ├── plugin.ts                  # 插件路由：插件商店、管理界面、开发工具、配置页面、更新检查、安全审计、性能监控
│   │   └── settings.ts                # 设置路由：通用设置、主题配置、语言设置、高级选项、导入导出、重置功能、帮助文档
│   └── types.ts                       # 路由类型定义：路由元信息、参数类型、守卫类型、权限类型、导航类型、错误类型
```

**路由配置示例**
```typescript
// router/index.ts
import { createRouter, createWebHistory } from 'vue-router'
import { setupRouterGuards } from './guards'
import chatRoutes from './routes/chat'
import knowledgeRoutes from './routes/knowledge'
import modelRoutes from './routes/model'

const routes = [
  {
    path: '/',
    redirect: '/chat'
  },
  {
    path: '/chat',
    component: () => import('@/layouts/MainLayout.vue'),
    children: chatRoutes
  },
  {
    path: '/knowledge',
    component: () => import('@/layouts/MainLayout.vue'),
    children: knowledgeRoutes
  },
  {
    path: '/model',
    component: () => import('@/layouts/MainLayout.vue'),
    children: modelRoutes
  },
  {
    path: '/:pathMatch(.*)*',
    component: () => import('@/views/NotFound.vue')
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    }
    return { top: 0 }
  }
})

// 设置路由守卫
setupRouterGuards(router)

export default router
```

#### 2.2.5 工具函数与辅助类

```
├── utils/                             # 工具函数
│   ├── index.ts                       # 工具函数入口：统一导出、类型定义、常用工具、快捷方法、兼容性处理、性能优化
│   ├── format.ts                      # 格式化工具：日期格式化、数字格式化、文件大小、时间差计算、货币格式、百分比、本地化
│   ├── validation.ts                  # 验证工具：表单验证、数据校验、格式检查、规则引擎、错误消息、自定义验证、异步验证
│   ├── storage.ts                     # 存储工具：本地存储、会话存储、IndexedDB、数据加密、过期管理、容量检查、备份恢复
│   ├── request.ts                     # 请求工具：HTTP客户端、请求拦截、响应处理、错误重试、超时控制、缓存策略、进度跟踪
│   ├── file.ts                        # 文件工具：文件读取、格式检测、大小计算、类型判断、路径处理、下载上传、压缩解压
│   ├── crypto.ts                      # 加密工具：数据加密、哈希计算、签名验证、密钥管理、随机数生成、安全存储、完整性检查
│   ├── performance.ts                 # 性能工具：性能监控、内存使用、执行时间、资源统计、瓶颈分析、优化建议、报告生成
│   ├── dom.ts                         # DOM工具：元素操作、事件处理、样式计算、位置获取、滚动控制、焦点管理、无障碍支持
│   ├── async.ts                       # 异步工具：Promise封装、并发控制、队列管理、重试机制、超时处理、取消操作、进度回调
│   ├── string.ts                      # 字符串工具：字符串处理、模板替换、编码转换、正则匹配、文本分析、格式化、国际化
│   ├── array.ts                       # 数组工具：数组操作、去重排序、分组聚合、查找过滤、分页处理、性能优化、类型安全
│   ├── object.ts                      # 对象工具：深拷贝、对象合并、属性访问、类型转换、序列化、比较判断、代理包装
│   ├── date.ts                        # 日期工具：日期计算、格式转换、时区处理、相对时间、日历功能、假期判断、工作日计算
│   ├── color.ts                       # 颜色工具：颜色转换、主题生成、对比度计算、调色板、渐变生成、无障碍检查、色彩分析
│   ├── animation.ts                   # 动画工具：缓动函数、动画控制、帧率管理、性能优化、手势识别、物理模拟、交互反馈
│   └── debug.ts                       # 调试工具：日志输出、错误追踪、性能分析、状态检查、开发工具、测试辅助、问题诊断
```

**工具函数示例**
```typescript
// utils/format.ts
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B'

  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

export const formatDate = (date: Date | string, format: string = 'YYYY-MM-DD HH:mm:ss'): string => {
  const d = new Date(date)

  const formatMap: Record<string, string> = {
    'YYYY': d.getFullYear().toString(),
    'MM': (d.getMonth() + 1).toString().padStart(2, '0'),
    'DD': d.getDate().toString().padStart(2, '0'),
    'HH': d.getHours().toString().padStart(2, '0'),
    'mm': d.getMinutes().toString().padStart(2, '0'),
    'ss': d.getSeconds().toString().padStart(2, '0')
  }

  return format.replace(/YYYY|MM|DD|HH|mm|ss/g, match => formatMap[match])
}

// utils/validation.ts
export const validateEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

export const validateFileType = (file: File, allowedTypes: string[]): boolean => {
  return allowedTypes.includes(file.type)
}

export const validateFileSize = (file: File, maxSize: number): boolean => {
  return file.size <= maxSize
}
```

#### 2.2.6 类型定义系统

```
├── types/                             # TypeScript类型定义
│   ├── index.ts                       # 类型入口：统一导出、全局类型、基础类型、工具类型、条件类型、映射类型、模板字面量
│   ├── api.ts                         # API类型：请求参数、响应数据、错误类型、状态码、头部信息、分页数据、批量操作
│   ├── chat.ts                        # 聊天类型：消息类型、会话类型、用户类型、模型类型、配置类型、状态类型、事件类型
│   ├── knowledge.ts                   # 知识库类型：文档类型、知识库类型、搜索类型、向量类型、处理状态、统计数据、配置选项
│   ├── model.ts                       # 模型类型：模型信息、配置参数、性能指标、下载状态、版本信息、兼容性、错误类型
│   ├── multimodal.ts                  # 多模态类型：媒体类型、处理结果、配置选项、格式信息、元数据、进度状态、错误信息
│   ├── network.ts                     # 网络类型：设备信息、连接状态、传输数据、配置选项、安全设置、性能统计、错误类型
│   ├── plugin.ts                      # 插件类型：插件信息、配置数据、权限类型、API接口、事件类型、状态管理、错误处理
│   ├── settings.ts                    # 设置类型：配置项、用户偏好、验证规则、默认值、变更记录、导入导出、重置选项
│   ├── theme.ts                       # 主题类型：主题配置、颜色定义、样式变量、动画设置、响应式断点、自定义选项、兼容性
│   ├── i18n.ts                        # 国际化类型：语言配置、翻译键值、格式化选项、区域设置、动态加载、回退机制、验证规则
│   ├── system.ts                      # 系统类型：应用信息、运行状态、性能数据、错误信息、诊断数据、更新信息、日志类型
│   ├── components.ts                  # 组件类型：Props类型、Emits类型、Slots类型、Ref类型、实例类型、事件类型、状态类型
│   └── utils.ts                       # 工具类型：函数类型、返回类型、参数类型、泛型约束、条件类型、工具函数、类型守卫
```

**类型定义示例**
```typescript
// types/chat.ts
export interface Message {
  id: string
  sessionId: string
  content: string
  role: 'user' | 'assistant' | 'system'
  timestamp: number
  status: 'sending' | 'sent' | 'error'
  metadata?: {
    model?: string
    tokens?: number
    duration?: number
    attachments?: Attachment[]
  }
}

export interface Session {
  id: string
  title: string
  createdAt: number
  updatedAt: number
  messageCount: number
  model: string
  settings: SessionSettings
  tags: string[]
  isArchived: boolean
}

export interface SessionSettings {
  model: string
  temperature: number
  maxTokens: number
  topP: number
  frequencyPenalty: number
  presencePenalty: number
  systemPrompt?: string
}

// types/api.ts
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  error?: {
    code: string
    message: string
    details?: any
  }
  timestamp: number
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: {
    page: number
    pageSize: number
    total: number
    totalPages: number
  }
}
```

### 2.3 Tailwind CSS + SCSS样式方案

#### 2.3.1 样式架构设计

AI Studio 采用 Tailwind CSS 作为基础样式框架，结合 SCSS 进行样式扩展和组件样式管理。这种混合方案既保持了 Tailwind 的快速开发优势，又提供了 SCSS 的强大功能。

**样式层次结构：**
```
样式系统架构:

┌─── 基础层 (Base Layer) ───┐
│ • CSS Reset              │ ← 浏览器样式重置
│ • Normalize.css          │ ← 跨浏览器一致性
│ • 全局变量定义            │ ← CSS自定义属性
└─────────────────────────┘
         ↓
┌─── 工具层 (Utility Layer) ───┐
│ • Tailwind Utilities     │ ← 原子化CSS类
│ • 自定义工具类            │ ← 项目特定工具
│ • 响应式断点             │ ← 媒体查询工具
└─────────────────────────┘
         ↓
┌─── 组件层 (Component Layer) ───┐
│ • 组件基础样式            │ ← 组件默认样式
│ • 组件变体样式            │ ← 不同状态样式
│ • 组件动画效果            │ ← 交互动画
└─────────────────────────┘
         ↓
┌─── 主题层 (Theme Layer) ───┐
│ • 浅色主题               │ ← Light Theme
│ • 深色主题               │ ← Dark Theme
│ • 高对比度主题            │ ← High Contrast
│ • 自定义主题             │ ← Custom Themes
└─────────────────────────┘
```

#### 2.3.2 Tailwind CSS配置

**tailwind.config.js 配置文件**
```javascript
/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./index.html",
    "./src/**/*.{vue,js,ts,jsx,tsx}",
  ],
  darkMode: 'class', // 支持类名切换深色模式
  theme: {
    extend: {
      // 自定义颜色系统
      colors: {
        // 主色调
        primary: {
          50: '#f0f9ff',
          100: '#e0f2fe',
          200: '#bae6fd',
          300: '#7dd3fc',
          400: '#38bdf8',
          500: '#0ea5e9',
          600: '#0284c7',
          700: '#0369a1',
          800: '#075985',
          900: '#0c4a6e',
        },
        // 辅助色
        secondary: {
          50: '#f8fafc',
          100: '#f1f5f9',
          200: '#e2e8f0',
          300: '#cbd5e1',
          400: '#94a3b8',
          500: '#64748b',
          600: '#475569',
          700: '#334155',
          800: '#1e293b',
          900: '#0f172a',
        },
        // 成功色
        success: {
          50: '#f0fdf4',
          100: '#dcfce7',
          200: '#bbf7d0',
          300: '#86efac',
          400: '#4ade80',
          500: '#22c55e',
          600: '#16a34a',
          700: '#15803d',
          800: '#166534',
          900: '#14532d',
        },
        // 警告色
        warning: {
          50: '#fffbeb',
          100: '#fef3c7',
          200: '#fde68a',
          300: '#fcd34d',
          400: '#fbbf24',
          500: '#f59e0b',
          600: '#d97706',
          700: '#b45309',
          800: '#92400e',
          900: '#78350f',
        },
        // 错误色
        error: {
          50: '#fef2f2',
          100: '#fee2e2',
          200: '#fecaca',
          300: '#fca5a5',
          400: '#f87171',
          500: '#ef4444',
          600: '#dc2626',
          700: '#b91c1c',
          800: '#991b1b',
          900: '#7f1d1d',
        },
        // 中性色
        neutral: {
          50: '#fafafa',
          100: '#f5f5f5',
          200: '#e5e5e5',
          300: '#d4d4d4',
          400: '#a3a3a3',
          500: '#737373',
          600: '#525252',
          700: '#404040',
          800: '#262626',
          900: '#171717',
        }
      },

      // 自定义字体
      fontFamily: {
        sans: ['Inter', 'PingFang SC', 'Microsoft YaHei', 'sans-serif'],
        mono: ['JetBrains Mono', 'Consolas', 'Monaco', 'monospace'],
        serif: ['Georgia', 'Times New Roman', 'serif'],
      },

      // 自定义间距
      spacing: {
        '18': '4.5rem',
        '88': '22rem',
        '128': '32rem',
      },

      // 自定义断点
      screens: {
        'xs': '475px',
        'sm': '640px',
        'md': '768px',
        'lg': '1024px',
        'xl': '1280px',
        '2xl': '1536px',
        '3xl': '1920px',
      },

      // 自定义阴影
      boxShadow: {
        'soft': '0 2px 15px -3px rgba(0, 0, 0, 0.07), 0 10px 20px -2px rgba(0, 0, 0, 0.04)',
        'medium': '0 4px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
        'strong': '0 10px 40px -10px rgba(0, 0, 0, 0.15), 0 20px 25px -5px rgba(0, 0, 0, 0.1)',
      },

      // 自定义动画
      animation: {
        'fade-in': 'fadeIn 0.3s ease-in-out',
        'slide-up': 'slideUp 0.3s ease-out',
        'slide-down': 'slideDown 0.3s ease-out',
        'scale-in': 'scaleIn 0.2s ease-out',
        'spin-slow': 'spin 3s linear infinite',
      },

      // 自定义关键帧
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        slideUp: {
          '0%': { transform: 'translateY(10px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
        slideDown: {
          '0%': { transform: 'translateY(-10px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
        scaleIn: {
          '0%': { transform: 'scale(0.95)', opacity: '0' },
          '100%': { transform: 'scale(1)', opacity: '1' },
        },
      },
    },
  },
  plugins: [
    require('@tailwindcss/forms'),
    require('@tailwindcss/typography'),
    require('@tailwindcss/aspect-ratio'),
    require('@tailwindcss/line-clamp'),
  ],
}
```

#### 2.3.3 SCSS样式组织

**SCSS文件结构**
```scss
// assets/styles/globals.scss - 全局SCSS变量和混入
// ===================================================

// CSS自定义属性 (CSS Variables)
:root {
  // 颜色系统
  --color-primary: #0ea5e9;
  --color-primary-hover: #0284c7;
  --color-primary-active: #0369a1;

  --color-secondary: #64748b;
  --color-secondary-hover: #475569;
  --color-secondary-active: #334155;

  --color-success: #22c55e;
  --color-warning: #f59e0b;
  --color-error: #ef4444;
  --color-info: #3b82f6;

  // 中性色
  --color-text-primary: #1e293b;
  --color-text-secondary: #64748b;
  --color-text-tertiary: #94a3b8;
  --color-text-inverse: #ffffff;

  --color-bg-primary: #ffffff;
  --color-bg-secondary: #f8fafc;
  --color-bg-tertiary: #f1f5f9;
  --color-bg-overlay: rgba(0, 0, 0, 0.5);

  --color-border-primary: #e2e8f0;
  --color-border-secondary: #cbd5e1;
  --color-border-focus: #0ea5e9;

  // 间距系统
  --spacing-xs: 0.25rem;    // 4px
  --spacing-sm: 0.5rem;     // 8px
  --spacing-md: 1rem;       // 16px
  --spacing-lg: 1.5rem;     // 24px
  --spacing-xl: 2rem;       // 32px
  --spacing-2xl: 3rem;      // 48px
  --spacing-3xl: 4rem;      // 64px

  // 字体系统
  --font-size-xs: 0.75rem;   // 12px
  --font-size-sm: 0.875rem;  // 14px
  --font-size-base: 1rem;    // 16px
  --font-size-lg: 1.125rem;  // 18px
  --font-size-xl: 1.25rem;   // 20px
  --font-size-2xl: 1.5rem;   // 24px
  --font-size-3xl: 1.875rem; // 30px
  --font-size-4xl: 2.25rem;  // 36px

  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;

  --line-height-tight: 1.25;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.75;

  // 圆角系统
  --radius-sm: 0.25rem;   // 4px
  --radius-md: 0.375rem;  // 6px
  --radius-lg: 0.5rem;    // 8px
  --radius-xl: 0.75rem;   // 12px
  --radius-2xl: 1rem;     // 16px
  --radius-full: 9999px;

  // 阴影系统
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

  // 动画系统
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 250ms ease-in-out;
  --transition-slow: 350ms ease-in-out;

  // Z-index系统
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
  --z-toast: 1080;
}

// 深色主题变量
[data-theme="dark"] {
  --color-text-primary: #f8fafc;
  --color-text-secondary: #cbd5e1;
  --color-text-tertiary: #94a3b8;
  --color-text-inverse: #1e293b;

  --color-bg-primary: #0f172a;
  --color-bg-secondary: #1e293b;
  --color-bg-tertiary: #334155;
  --color-bg-overlay: rgba(0, 0, 0, 0.8);

  --color-border-primary: #334155;
  --color-border-secondary: #475569;
}

// SCSS变量
$breakpoints: (
  xs: 475px,
  sm: 640px,
  md: 768px,
  lg: 1024px,
  xl: 1280px,
  2xl: 1536px,
  3xl: 1920px
);

// 混入 (Mixins)
@mixin respond-to($breakpoint) {
  @if map-has-key($breakpoints, $breakpoint) {
    @media (min-width: map-get($breakpoints, $breakpoint)) {
      @content;
    }
  } @else {
    @warn "Unknown breakpoint: #{$breakpoint}";
  }
}

@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

@mixin absolute-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

@mixin truncate-text {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

@mixin visually-hidden {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

// 函数
@function rem($px) {
  @return #{$px / 16}rem;
}

@function em($px, $base: 16) {
  @return #{$px / $base}em;
}
```

#### 2.3.4 主题系统实现

**主题切换机制**
```scss
// assets/styles/themes.scss - 主题样式定义
// ===================================================

// 浅色主题 (默认)
.theme-light {
  --theme-bg-primary: #ffffff;
  --theme-bg-secondary: #f8fafc;
  --theme-bg-tertiary: #f1f5f9;
  --theme-bg-accent: #e0f2fe;

  --theme-text-primary: #1e293b;
  --theme-text-secondary: #64748b;
  --theme-text-tertiary: #94a3b8;
  --theme-text-accent: #0ea5e9;

  --theme-border-primary: #e2e8f0;
  --theme-border-secondary: #cbd5e1;
  --theme-border-accent: #0ea5e9;

  --theme-shadow: rgba(0, 0, 0, 0.1);
  --theme-overlay: rgba(0, 0, 0, 0.5);
}

// 深色主题
.theme-dark {
  --theme-bg-primary: #0f172a;
  --theme-bg-secondary: #1e293b;
  --theme-bg-tertiary: #334155;
  --theme-bg-accent: #1e40af;

  --theme-text-primary: #f8fafc;
  --theme-text-secondary: #cbd5e1;
  --theme-text-tertiary: #94a3b8;
  --theme-text-accent: #60a5fa;

  --theme-border-primary: #334155;
  --theme-border-secondary: #475569;
  --theme-border-accent: #60a5fa;

  --theme-shadow: rgba(0, 0, 0, 0.3);
  --theme-overlay: rgba(0, 0, 0, 0.8);
}

// 高对比度主题
.theme-high-contrast {
  --theme-bg-primary: #ffffff;
  --theme-bg-secondary: #f0f0f0;
  --theme-bg-tertiary: #e0e0e0;
  --theme-bg-accent: #0000ff;

  --theme-text-primary: #000000;
  --theme-text-secondary: #333333;
  --theme-text-tertiary: #666666;
  --theme-text-accent: #0000ff;

  --theme-border-primary: #000000;
  --theme-border-secondary: #333333;
  --theme-border-accent: #0000ff;

  --theme-shadow: rgba(0, 0, 0, 0.5);
  --theme-overlay: rgba(0, 0, 0, 0.9);
}

// 主题过渡动画
.theme-transition {
  transition:
    background-color var(--transition-normal),
    color var(--transition-normal),
    border-color var(--transition-normal),
    box-shadow var(--transition-normal);
}
```

---

## 3. 后端目录结构与模块设计

### 3.1 Rust后端目录结构

```
src-tauri/                             # Tauri后端根目录
├── Cargo.toml                         # 项目配置：依赖管理、构建配置、元数据信息、特性开关、优化设置、目标平台、发布配置
├── tauri.conf.json                    # Tauri配置：窗口设置、权限配置、构建选项、安全策略、更新机制、图标资源、平台特定设置
├── build.rs                           # 构建脚本：编译时代码生成、资源嵌入、条件编译、环境检查、依赖验证、优化配置、平台适配
├── src/                               # 源代码目录
│   ├── main.rs                        # 应用入口：Tauri应用初始化、窗口创建、菜单设置、事件监听、插件注册、错误处理、生命周期管理
│   ├── lib.rs                         # 库入口：模块声明、公共接口、类型导出、特征定义、宏定义、条件编译、文档注释
│   ├── commands/                      # Tauri命令模块
│   │   ├── mod.rs                     # 命令模块入口：命令注册、权限检查、错误处理、日志记录、性能监控、安全验证、类型转换
│   │   ├── chat.rs                    # 聊天命令：会话管理、消息发送、流式响应、历史查询、模型切换、配置更新、状态同步
│   │   ├── knowledge.rs               # 知识库命令：文档上传、向量化处理、搜索查询、知识库管理、统计信息、批量操作、数据导入导出
│   │   ├── model.rs                   # 模型命令：模型下载、加载卸载、配置管理、性能监控、版本控制、兼容性检查、资源管理
│   │   ├── multimodal.rs              # 多模态命令：图像处理、音频转换、视频分析、OCR识别、格式转换、批量处理、结果缓存
│   │   ├── network.rs                 # 网络命令：设备发现、连接管理、数据传输、状态同步、安全验证、性能监控、错误恢复
│   │   ├── plugin.rs                  # 插件命令：插件加载、配置管理、权限控制、生命周期、API调用、事件分发、安全沙箱
│   │   ├── system.rs                  # 系统命令：系统信息、性能监控、日志管理、配置读写、更新检查、诊断工具、资源清理
│   │   └── settings.rs                # 设置命令：配置读写、验证更新、默认值、导入导出、重置功能、变更通知、备份恢复
│   ├── services/                      # 业务服务层
│   │   ├── mod.rs                     # 服务模块入口：服务注册、依赖注入、生命周期管理、错误处理、日志记录、性能监控、资源管理
│   │   ├── chat_service.rs            # 聊天服务：会话管理、消息处理、模型调用、流式响应、历史存储、状态管理、错误恢复
│   │   ├── knowledge_service.rs       # 知识库服务：文档解析、向量化、搜索引擎、索引管理、缓存策略、批量处理、数据同步
│   │   ├── model_service.rs           # 模型服务：模型加载、推理调度、资源管理、性能优化、缓存策略、错误处理、监控统计
│   │   ├── multimodal_service.rs      # 多模态服务：媒体处理、格式转换、特征提取、结果缓存、批量队列、错误重试、进度跟踪
│   │   ├── network_service.rs         # 网络服务：P2P通信、设备发现、数据传输、连接管理、安全加密、状态同步、错误恢复
│   │   ├── plugin_service.rs          # 插件服务：插件管理、沙箱执行、API代理、事件系统、权限控制、生命周期、安全审计
│   │   ├── storage_service.rs         # 存储服务：数据持久化、缓存管理、文件操作、备份恢复、数据迁移、完整性检查、性能优化
│   │   └── system_service.rs          # 系统服务：系统监控、资源管理、日志服务、配置管理、更新服务、诊断工具、性能分析
│   ├── core/                          # 核心功能模块
│   │   ├── mod.rs                     # 核心模块入口：核心组件注册、初始化顺序、依赖关系、错误处理、日志配置、性能监控
│   │   ├── ai/                        # AI推理引擎
│   │   │   ├── mod.rs                 # AI模块入口：推理引擎初始化、模型管理、任务调度、资源分配、性能监控、错误处理
│   │   │   ├── inference.rs           # 推理引擎：模型加载、推理执行、批处理、流式输出、性能优化、内存管理、错误恢复
│   │   │   ├── models.rs              # 模型管理：模型注册、版本控制、兼容性检查、资源管理、缓存策略、热加载、监控统计
│   │   │   ├── tokenizer.rs           # 分词器：文本分词、编码解码、特殊标记、词汇表管理、性能优化、缓存机制、错误处理
│   │   │   ├── embedding.rs           # 向量化：文本向量化、批量处理、缓存管理、性能优化、维度管理、相似度计算、索引构建
│   │   │   └── pipeline.rs            # 推理管道：任务流水线、并发控制、资源调度、性能监控、错误处理、结果缓存、优化策略
│   │   ├── database/                  # 数据库模块
│   │   │   ├── mod.rs                 # 数据库模块入口：连接管理、事务控制、迁移管理、性能监控、错误处理、连接池、备份恢复
│   │   │   ├── sqlite.rs              # SQLite数据库：连接管理、查询执行、事务处理、索引优化、性能调优、备份恢复、数据迁移
│   │   │   ├── chroma.rs              # ChromaDB向量库：向量存储、相似度搜索、索引管理、批量操作、性能优化、数据同步、错误处理
│   │   │   ├── migrations.rs          # 数据库迁移：版本管理、结构变更、数据迁移、回滚机制、完整性检查、性能优化、错误恢复
│   │   │   └── cache.rs               # 缓存层：内存缓存、持久化缓存、缓存策略、过期管理、性能监控、数据一致性、错误处理
│   │   ├── network/                   # 网络通信模块
│   │   │   ├── mod.rs                 # 网络模块入口：网络初始化、协议注册、连接管理、安全配置、性能监控、错误处理、资源清理
│   │   │   ├── p2p.rs                 # P2P网络：节点发现、连接建立、数据传输、路由管理、NAT穿透、安全加密、性能优化
│   │   │   ├── discovery.rs           # 设备发现：广播发现、服务注册、状态同步、网络拓扑、连接质量、安全验证、错误恢复
│   │   │   ├── transfer.rs            # 数据传输：文件传输、断点续传、压缩加密、进度跟踪、错误重试、带宽控制、完整性验证
│   │   │   └── security.rs            # 网络安全：身份验证、数据加密、证书管理、权限控制、安全审计、攻击防护、合规检查
│   │   ├── plugin/                    # 插件系统
│   │   │   ├── mod.rs                 # 插件模块入口：插件框架初始化、API注册、安全沙箱、生命周期管理、事件系统、错误处理
│   │   │   ├── manager.rs             # 插件管理器：插件加载、卸载、更新、依赖管理、版本控制、冲突解决、性能监控、安全审计
│   │   │   ├── runtime.rs             # 插件运行时：沙箱执行、资源限制、API代理、事件分发、错误隔离、性能监控、安全控制
│   │   │   ├── api.rs                 # 插件API：接口定义、权限控制、参数验证、结果处理、错误传播、日志记录、性能统计
│   │   │   └── security.rs            # 插件安全：权限模型、沙箱隔离、代码审计、资源限制、安全策略、威胁检测、合规验证
│   │   └── utils/                     # 工具模块
│   │       ├── mod.rs                 # 工具模块入口：工具函数注册、公共接口、错误处理、性能优化、日志配置、测试辅助
│   │       ├── crypto.rs              # 加密工具：数据加密、哈希计算、数字签名、密钥管理、随机数生成、安全存储、完整性验证
│   │       ├── file.rs                # 文件工具：文件操作、路径处理、权限管理、监控变更、批量处理、压缩解压、安全检查
│   │       ├── config.rs              # 配置工具：配置读写、验证解析、默认值、环境变量、配置合并、热重载、版本管理
│   │       ├── logger.rs              # 日志工具：日志记录、级别控制、格式化、轮转管理、性能监控、错误追踪、审计日志
│   │       ├── metrics.rs             # 性能指标：指标收集、统计分析、性能监控、资源使用、趋势分析、告警机制、报告生成
│   │       └── error.rs               # 错误处理：错误定义、错误传播、错误恢复、日志记录、用户提示、调试信息、错误统计
│   └── types/                         # 类型定义
│       ├── mod.rs                     # 类型模块入口：类型导出、公共接口、序列化配置、验证规则、转换函数、文档注释
│       ├── api.rs                     # API类型：请求响应、错误类型、状态码、参数验证、序列化、版本兼容、文档生成
│       ├── config.rs                  # 配置类型：配置结构、默认值、验证规则、环境变量、类型转换、版本管理、文档说明
│       ├── database.rs                # 数据库类型：实体模型、查询参数、结果集、关系映射、索引定义、迁移脚本、性能优化
│       ├── network.rs                 # 网络类型：协议定义、消息格式、连接状态、传输参数、安全配置、性能指标、错误类型
│       └── plugin.rs                  # 插件类型：插件接口、配置结构、权限模型、事件类型、API定义、安全策略、版本信息
```

### 3.2 Tauri集成与命令系统

#### 3.2.1 Tauri应用初始化

**main.rs - 应用入口配置**
```rust
// src/main.rs
#![cfg_attr(not(debug_assertions), windows_subsystem = "windows")]

use tauri::{
    CustomMenuItem, Manager, Menu, MenuItem, Submenu, SystemTray, SystemTrayEvent,
    SystemTrayMenu, SystemTrayMenuItem, WindowBuilder, WindowUrl,
};
use std::sync::Arc;
use tokio::sync::Mutex;

mod commands;
mod services;
mod core;
mod types;

use commands::*;
use services::*;
use core::*;

// 应用状态管理
#[derive(Default)]
pub struct AppState {
    pub chat_service: Arc<Mutex<ChatService>>,
    pub knowledge_service: Arc<Mutex<KnowledgeService>>,
    pub model_service: Arc<Mutex<ModelService>>,
    pub multimodal_service: Arc<Mutex<MultimodalService>>,
    pub network_service: Arc<Mutex<NetworkService>>,
    pub plugin_service: Arc<Mutex<PluginService>>,
    pub system_service: Arc<Mutex<SystemService>>,
}

#[tokio::main]
async fn main() {
    // 初始化日志系统
    env_logger::Builder::from_env(env_logger::Env::default().default_filter_or("info"))
        .init();

    // 创建应用状态
    let app_state = AppState::default();

    // 创建菜单
    let menu = create_app_menu();

    // 创建系统托盘
    let tray = create_system_tray();

    // 构建Tauri应用
    tauri::Builder::default()
        .manage(app_state)
        .menu(menu)
        .system_tray(tray)
        .on_system_tray_event(handle_system_tray_event)
        .setup(|app| {
            // 应用初始化
            setup_app(app)?;
            Ok(())
        })
        .invoke_handler(tauri::generate_handler![
            // 聊天命令
            chat::create_session,
            chat::send_message,
            chat::get_sessions,
            chat::get_messages,
            chat::delete_session,
            chat::update_session_settings,

            // 知识库命令
            knowledge::create_knowledge_base,
            knowledge::upload_documents,
            knowledge::search_documents,
            knowledge::get_knowledge_bases,
            knowledge::delete_knowledge_base,
            knowledge::get_embedding_progress,

            // 模型命令
            model::get_available_models,
            model::download_model,
            model::load_model,
            model::unload_model,
            model::get_model_status,
            model::get_download_progress,

            // 多模态命令
            multimodal::process_image,
            multimodal::process_audio,
            multimodal::process_video,
            multimodal::get_processing_history,

            // 网络命令
            network::discover_devices,
            network::connect_device,
            network::share_resource,
            network::get_connection_status,

            // 插件命令
            plugin::get_installed_plugins,
            plugin::install_plugin,
            plugin::uninstall_plugin,
            plugin::configure_plugin,
            plugin::get_plugin_store,

            // 系统命令
            system::get_system_info,
            system::get_performance_metrics,
            system::check_for_updates,
            system::export_logs,

            // 设置命令
            settings::get_settings,
            settings::update_settings,
            settings::reset_settings,
            settings::export_settings,
            settings::import_settings,
        ])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}

// 创建应用菜单
fn create_app_menu() -> Menu {
    let quit = CustomMenuItem::new("quit".to_string(), "退出");
    let close = CustomMenuItem::new("close".to_string(), "关闭");
    let minimize = CustomMenuItem::new("minimize".to_string(), "最小化");

    let submenu = Submenu::new("文件", Menu::new().add_item(quit).add_item(close));
    let window_submenu = Submenu::new("窗口", Menu::new().add_item(minimize));

    Menu::new()
        .add_submenu(submenu)
        .add_submenu(window_submenu)
        .add_native_item(MenuItem::Copy)
        .add_native_item(MenuItem::Paste)
        .add_native_item(MenuItem::Cut)
        .add_native_item(MenuItem::SelectAll)
}

// 创建系统托盘
fn create_system_tray() -> SystemTray {
    let quit = CustomMenuItem::new("quit".to_string(), "退出");
    let show = CustomMenuItem::new("show".to_string(), "显示");
    let hide = CustomMenuItem::new("hide".to_string(), "隐藏");

    let tray_menu = SystemTrayMenu::new()
        .add_item(show)
        .add_item(hide)
        .add_native_item(SystemTrayMenuItem::Separator)
        .add_item(quit);

    SystemTray::new().with_menu(tray_menu)
}

// 处理系统托盘事件
fn handle_system_tray_event(app: &tauri::AppHandle, event: SystemTrayEvent) {
    match event {
        SystemTrayEvent::LeftClick {
            position: _,
            size: _,
            ..
        } => {
            let window = app.get_window("main").unwrap();
            window.show().unwrap();
            window.set_focus().unwrap();
        }
        SystemTrayEvent::MenuItemClick { id, .. } => {
            match id.as_str() {
                "quit" => {
                    std::process::exit(0);
                }
                "show" => {
                    let window = app.get_window("main").unwrap();
                    window.show().unwrap();
                }
                "hide" => {
                    let window = app.get_window("main").unwrap();
                    window.hide().unwrap();
                }
                _ => {}
            }
        }
        _ => {}
    }
}

// 应用初始化设置
fn setup_app(app: &mut tauri::App) -> Result<(), Box<dyn std::error::Error>> {
    // 创建主窗口
    let main_window = WindowBuilder::new(
        app,
        "main",
        WindowUrl::App("index.html".into())
    )
    .title("AI Studio")
    .inner_size(1200.0, 800.0)
    .min_inner_size(800.0, 600.0)
    .center()
    .build()?;

    // 初始化服务
    let app_handle = app.handle();
    tauri::async_runtime::spawn(async move {
        if let Err(e) = initialize_services(&app_handle).await {
            eprintln!("Failed to initialize services: {}", e);
        }
    });

    Ok(())
}

// 初始化所有服务
async fn initialize_services(app: &tauri::AppHandle) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
    let state = app.state::<AppState>();

    // 初始化数据库
    let db_service = database::DatabaseService::new().await?;

    // 初始化各个服务
    let mut chat_service = state.chat_service.lock().await;
    *chat_service = ChatService::new(db_service.clone()).await?;
    drop(chat_service);

    let mut knowledge_service = state.knowledge_service.lock().await;
    *knowledge_service = KnowledgeService::new(db_service.clone()).await?;
    drop(knowledge_service);

    let mut model_service = state.model_service.lock().await;
    *model_service = ModelService::new().await?;
    drop(model_service);

    // 启动后台任务
    start_background_tasks(app).await?;

    Ok(())
}

// 启动后台任务
async fn start_background_tasks(app: &tauri::AppHandle) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
    let app_handle = app.clone();

    // 性能监控任务
    tokio::spawn(async move {
        let mut interval = tokio::time::interval(tokio::time::Duration::from_secs(30));
        loop {
            interval.tick().await;
            if let Err(e) = collect_performance_metrics(&app_handle).await {
                eprintln!("Failed to collect performance metrics: {}", e);
            }
        }
    });

    // 自动保存任务
    let app_handle = app.clone();
    tokio::spawn(async move {
        let mut interval = tokio::time::interval(tokio::time::Duration::from_secs(300)); // 5分钟
        loop {
            interval.tick().await;
            if let Err(e) = auto_save_data(&app_handle).await {
                eprintln!("Failed to auto save data: {}", e);
            }
        }
    });

    Ok(())
}

// 性能指标收集
async fn collect_performance_metrics(app: &tauri::AppHandle) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
    let state = app.state::<AppState>();
    let system_service = state.system_service.lock().await;

    let metrics = system_service.get_performance_metrics().await?;

    // 发送性能指标到前端
    app.emit_all("performance-metrics", &metrics)?;

    Ok(())
}

// 自动保存数据
async fn auto_save_data(app: &tauri::AppHandle) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
    let state = app.state::<AppState>();

    // 保存聊天数据
    let chat_service = state.chat_service.lock().await;
    chat_service.auto_save().await?;
    drop(chat_service);

    // 保存知识库数据
    let knowledge_service = state.knowledge_service.lock().await;
    knowledge_service.auto_save().await?;
    drop(knowledge_service);

    Ok(())
}
```

#### 3.2.2 命令系统架构

**commands/mod.rs - 命令模块入口**
```rust
// src/commands/mod.rs
use tauri::{command, State};
use serde::{Deserialize, Serialize};
use std::sync::Arc;
use tokio::sync::Mutex;

use crate::AppState;
use crate::types::api::{ApiResponse, ApiError};

pub mod chat;
pub mod knowledge;
pub mod model;
pub mod multimodal;
pub mod network;
pub mod plugin;
pub mod system;
pub mod settings;

// 命令结果类型
pub type CommandResult<T> = Result<ApiResponse<T>, ApiError>;

// 通用命令特征
#[async_trait::async_trait]
pub trait Command<T, R> {
    async fn execute(&self, params: T, state: &AppState) -> CommandResult<R>;
}

// 命令执行器
pub struct CommandExecutor;

impl CommandExecutor {
    pub async fn execute<T, R, C>(
        command: C,
        params: T,
        state: &AppState,
    ) -> CommandResult<R>
    where
        C: Command<T, R>,
        T: Send + Sync,
        R: Send + Sync,
    {
        // 执行前置检查
        Self::pre_execute_check(&params, state).await?;

        // 执行命令
        let result = command.execute(params, state).await;

        // 执行后置处理
        Self::post_execute_process(&result, state).await?;

        result
    }

    async fn pre_execute_check<T>(
        _params: &T,
        _state: &AppState,
    ) -> Result<(), ApiError> {
        // 权限检查
        // 参数验证
        // 资源检查
        Ok(())
    }

    async fn post_execute_process<R>(
        _result: &CommandResult<R>,
        _state: &AppState,
    ) -> Result<(), ApiError> {
        // 日志记录
        // 性能统计
        // 事件发送
        Ok(())
    }
}

// 命令装饰器宏
#[macro_export]
macro_rules! command_handler {
    ($func:ident, $params:ty, $return:ty) => {
        #[command]
        pub async fn $func(
            params: $params,
            state: State<'_, AppState>,
        ) -> Result<ApiResponse<$return>, ApiError> {
            let command = paste::paste! { [<$func:camel Command>]::new() };
            CommandExecutor::execute(command, params, &*state).await
        }
    };
}

// 错误处理宏
#[macro_export]
macro_rules! handle_service_error {
    ($result:expr) => {
        match $result {
            Ok(data) => Ok(ApiResponse::success(data)),
            Err(e) => {
                log::error!("Service error: {}", e);
                Err(ApiError::internal_error(e.to_string()))
            }
        }
    };
}

// 参数验证宏
#[macro_export]
macro_rules! validate_params {
    ($params:expr, $($field:ident: $validator:expr),*) => {
        $(
            if !$validator(&$params.$field) {
                return Err(ApiError::validation_error(
                    format!("Invalid parameter: {}", stringify!($field))
                ));
            }
        )*
    };
}
```

### 3.3 AI推理引擎模块

#### 3.3.1 推理引擎架构

AI Studio 的推理引擎采用模块化设计，支持多种AI模型和推理后端，提供统一的推理接口和高性能的推理能力。

**推理引擎架构图**
```
AI推理引擎架构:

┌─────────────────────────────────────────────────────────────┐
│                    推理引擎管理层                            │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │  任务调度器  │ │  资源管理器  │ │  缓存管理器  │ │ 监控器  │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
├─────────────────────────────────────────────────────────────┤
│                    模型抽象层                               │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │  模型接口   │ │  分词器接口  │ │  配置接口   │ │ 生命周期 │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
├─────────────────────────────────────────────────────────────┤
│                    推理后端层                               │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │ LLaMA.cpp   │ │   Candle    │ │ ONNX Runtime│ │ 自定义  │ │
│  │   后端      │ │    后端     │ │    后端     │ │  后端   │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
├─────────────────────────────────────────────────────────────┤
│                    硬件抽象层                               │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │    CPU      │ │    GPU      │ │    Metal    │ │  其他   │ │
│  │   计算      │ │   计算      │ │   计算      │ │ 加速器  │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
└─────────────────────────────────────────────────────────────┘
```

#### 3.3.2 推理引擎实现

**core/ai/inference.rs - 推理引擎核心**
```rust
// src/core/ai/inference.rs
use std::collections::HashMap;
use std::sync::Arc;
use tokio::sync::{Mutex, RwLock};
use serde::{Deserialize, Serialize};
use anyhow::{Result, anyhow};

use crate::core::ai::models::{ModelManager, ModelInfo, ModelConfig};
use crate::core::ai::tokenizer::{Tokenizer, TokenizerManager};
use crate::types::api::{InferenceRequest, InferenceResponse, StreamResponse};

// 推理引擎主结构
pub struct InferenceEngine {
    model_manager: Arc<ModelManager>,
    tokenizer_manager: Arc<TokenizerManager>,
    task_scheduler: Arc<TaskScheduler>,
    resource_manager: Arc<ResourceManager>,
    cache_manager: Arc<CacheManager>,
    performance_monitor: Arc<PerformanceMonitor>,
    config: InferenceConfig,
}

// 推理配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct InferenceConfig {
    pub max_concurrent_tasks: usize,
    pub max_sequence_length: usize,
    pub default_temperature: f32,
    pub default_top_p: f32,
    pub default_top_k: u32,
    pub cache_enabled: bool,
    pub cache_size_mb: usize,
    pub performance_monitoring: bool,
    pub gpu_enabled: bool,
    pub gpu_memory_fraction: f32,
}

impl Default for InferenceConfig {
    fn default() -> Self {
        Self {
            max_concurrent_tasks: 4,
            max_sequence_length: 4096,
            default_temperature: 0.7,
            default_top_p: 0.9,
            default_top_k: 40,
            cache_enabled: true,
            cache_size_mb: 512,
            performance_monitoring: true,
            gpu_enabled: true,
            gpu_memory_fraction: 0.8,
        }
    }
}

// 推理任务
#[derive(Debug, Clone)]
pub struct InferenceTask {
    pub id: String,
    pub model_id: String,
    pub request: InferenceRequest,
    pub priority: TaskPriority,
    pub created_at: std::time::Instant,
    pub timeout: Option<std::time::Duration>,
}

#[derive(Debug, Clone, PartialEq, Eq, PartialOrd, Ord)]
pub enum TaskPriority {
    Low = 0,
    Normal = 1,
    High = 2,
    Critical = 3,
}

impl InferenceEngine {
    pub async fn new(config: InferenceConfig) -> Result<Self> {
        let model_manager = Arc::new(ModelManager::new().await?);
        let tokenizer_manager = Arc::new(TokenizerManager::new().await?);
        let task_scheduler = Arc::new(TaskScheduler::new(config.max_concurrent_tasks).await?);
        let resource_manager = Arc::new(ResourceManager::new(&config).await?);
        let cache_manager = Arc::new(CacheManager::new(config.cache_size_mb).await?);
        let performance_monitor = Arc::new(PerformanceMonitor::new().await?);

        Ok(Self {
            model_manager,
            tokenizer_manager,
            task_scheduler,
            resource_manager,
            cache_manager,
            performance_monitor,
            config,
        })
    }

    // 执行推理任务
    pub async fn infer(&self, request: InferenceRequest) -> Result<InferenceResponse> {
        let task_id = uuid::Uuid::new_v4().to_string();
        let task = InferenceTask {
            id: task_id.clone(),
            model_id: request.model_id.clone(),
            request: request.clone(),
            priority: TaskPriority::Normal,
            created_at: std::time::Instant::now(),
            timeout: Some(std::time::Duration::from_secs(300)), // 5分钟超时
        };

        // 检查缓存
        if self.config.cache_enabled {
            if let Some(cached_response) = self.cache_manager.get(&request).await? {
                return Ok(cached_response);
            }
        }

        // 提交任务到调度器
        let response = self.task_scheduler.submit_task(task).await?;

        // 缓存结果
        if self.config.cache_enabled {
            self.cache_manager.put(&request, &response).await?;
        }

        Ok(response)
    }

    // 流式推理
    pub async fn infer_stream(
        &self,
        request: InferenceRequest,
    ) -> Result<tokio::sync::mpsc::Receiver<StreamResponse>> {
        let task_id = uuid::Uuid::new_v4().to_string();
        let task = InferenceTask {
            id: task_id.clone(),
            model_id: request.model_id.clone(),
            request,
            priority: TaskPriority::High, // 流式任务优先级较高
            created_at: std::time::Instant::now(),
            timeout: Some(std::time::Duration::from_secs(600)), // 10分钟超时
        };

        self.task_scheduler.submit_stream_task(task).await
    }

    // 加载模型
    pub async fn load_model(&self, model_info: &ModelInfo) -> Result<()> {
        // 检查资源是否足够
        self.resource_manager.check_resources_for_model(model_info).await?;

        // 加载模型
        self.model_manager.load_model(model_info).await?;

        // 加载对应的分词器
        self.tokenizer_manager.load_tokenizer(&model_info.tokenizer_path).await?;

        log::info!("Model {} loaded successfully", model_info.id);
        Ok(())
    }

    // 卸载模型
    pub async fn unload_model(&self, model_id: &str) -> Result<()> {
        self.model_manager.unload_model(model_id).await?;
        self.tokenizer_manager.unload_tokenizer(model_id).await?;

        log::info!("Model {} unloaded successfully", model_id);
        Ok(())
    }

    // 获取性能指标
    pub async fn get_performance_metrics(&self) -> Result<PerformanceMetrics> {
        self.performance_monitor.get_metrics().await
    }

    // 获取模型状态
    pub async fn get_model_status(&self, model_id: &str) -> Result<ModelStatus> {
        self.model_manager.get_model_status(model_id).await
    }

    // 清理资源
    pub async fn cleanup(&self) -> Result<()> {
        self.task_scheduler.shutdown().await?;
        self.model_manager.cleanup().await?;
        self.cache_manager.clear().await?;
        Ok(())
    }
}

// 任务调度器
pub struct TaskScheduler {
    task_queue: Arc<Mutex<std::collections::BinaryHeap<InferenceTask>>>,
    running_tasks: Arc<RwLock<HashMap<String, tokio::task::JoinHandle<()>>>>,
    max_concurrent: usize,
    shutdown_signal: Arc<tokio::sync::Notify>,
}

impl TaskScheduler {
    pub async fn new(max_concurrent: usize) -> Result<Self> {
        let scheduler = Self {
            task_queue: Arc::new(Mutex::new(std::collections::BinaryHeap::new())),
            running_tasks: Arc::new(RwLock::new(HashMap::new())),
            max_concurrent,
            shutdown_signal: Arc::new(tokio::sync::Notify::new()),
        };

        // 启动调度循环
        scheduler.start_scheduler_loop().await;

        Ok(scheduler)
    }

    pub async fn submit_task(&self, task: InferenceTask) -> Result<InferenceResponse> {
        let (tx, rx) = tokio::sync::oneshot::channel();

        // 将任务添加到队列
        {
            let mut queue = self.task_queue.lock().await;
            queue.push(task);
        }

        // 等待任务完成
        rx.await.map_err(|e| anyhow!("Task execution failed: {}", e))
    }

    pub async fn submit_stream_task(
        &self,
        task: InferenceTask,
    ) -> Result<tokio::sync::mpsc::Receiver<StreamResponse>> {
        let (tx, rx) = tokio::sync::mpsc::channel(100);

        // 创建流式任务处理器
        let task_handle = tokio::spawn(async move {
            // 流式任务处理逻辑
            // ...
        });

        // 记录运行中的任务
        {
            let mut running_tasks = self.running_tasks.write().await;
            running_tasks.insert(task.id.clone(), task_handle);
        }

        Ok(rx)
    }

    async fn start_scheduler_loop(&self) {
        let task_queue = self.task_queue.clone();
        let running_tasks = self.running_tasks.clone();
        let max_concurrent = self.max_concurrent;
        let shutdown_signal = self.shutdown_signal.clone();

        tokio::spawn(async move {
            loop {
                tokio::select! {
                    _ = shutdown_signal.notified() => {
                        log::info!("Task scheduler shutting down");
                        break;
                    }
                    _ = tokio::time::sleep(tokio::time::Duration::from_millis(100)) => {
                        // 检查是否有可用的执行槽位
                        let running_count = {
                            let tasks = running_tasks.read().await;
                            tasks.len()
                        };

                        if running_count < max_concurrent {
                            // 从队列中取出任务
                            let task = {
                                let mut queue = task_queue.lock().await;
                                queue.pop()
                            };

                            if let Some(task) = task {
                                // 执行任务
                                let task_id = task.id.clone();
                                let running_tasks_clone = running_tasks.clone();

                                let handle = tokio::spawn(async move {
                                    // 执行推理任务
                                    if let Err(e) = execute_inference_task(task).await {
                                        log::error!("Task execution failed: {}", e);
                                    }

                                    // 从运行列表中移除
                                    let mut tasks = running_tasks_clone.write().await;
                                    tasks.remove(&task_id);
                                });

                                let mut tasks = running_tasks.write().await;
                                tasks.insert(task_id, handle);
                            }
                        }
                    }
                }
            }
        });
    }

    pub async fn shutdown(&self) -> Result<()> {
        self.shutdown_signal.notify_waiters();

        // 等待所有任务完成
        let handles: Vec<_> = {
            let mut tasks = self.running_tasks.write().await;
            tasks.drain().map(|(_, handle)| handle).collect()
        };

        for handle in handles {
            let _ = handle.await;
        }

        Ok(())
    }
}

// 执行推理任务
async fn execute_inference_task(task: InferenceTask) -> Result<InferenceResponse> {
    let start_time = std::time::Instant::now();

    // 获取模型实例
    // 执行推理
    // 处理结果
    // 记录性能指标

    let duration = start_time.elapsed();
    log::debug!("Task {} completed in {:?}", task.id, duration);

    // 返回模拟结果
    Ok(InferenceResponse {
        id: task.id,
        text: "Generated response".to_string(),
        tokens: vec![],
        finish_reason: "stop".to_string(),
        usage: Default::default(),
        model: task.model_id,
        created: chrono::Utc::now().timestamp(),
    })
}

// 性能指标
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct PerformanceMetrics {
    pub total_requests: u64,
    pub successful_requests: u64,
    pub failed_requests: u64,
    pub average_latency_ms: f64,
    pub tokens_per_second: f64,
    pub memory_usage_mb: f64,
    pub gpu_utilization: f64,
    pub cache_hit_rate: f64,
}

// 模型状态
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ModelStatus {
    pub id: String,
    pub loaded: bool,
    pub memory_usage_mb: f64,
    pub last_used: Option<chrono::DateTime<chrono::Utc>>,
    pub total_requests: u64,
    pub average_latency_ms: f64,
}

// 资源管理器
pub struct ResourceManager {
    config: InferenceConfig,
    system_info: SystemInfo,
}

impl ResourceManager {
    pub async fn new(config: &InferenceConfig) -> Result<Self> {
        let system_info = SystemInfo::collect().await?;

        Ok(Self {
            config: config.clone(),
            system_info,
        })
    }

    pub async fn check_resources_for_model(&self, model_info: &ModelInfo) -> Result<()> {
        // 检查内存是否足够
        let required_memory = model_info.memory_requirements_mb;
        let available_memory = self.system_info.available_memory_mb;

        if required_memory > available_memory {
            return Err(anyhow!(
                "Insufficient memory: required {}MB, available {}MB",
                required_memory,
                available_memory
            ));
        }

        // 检查GPU资源
        if self.config.gpu_enabled && model_info.requires_gpu {
            if !self.system_info.gpu_available {
                return Err(anyhow!("GPU required but not available"));
            }
        }

        Ok(())
    }
}

// 系统信息
#[derive(Debug, Clone)]
pub struct SystemInfo {
    pub total_memory_mb: f64,
    pub available_memory_mb: f64,
    pub cpu_cores: usize,
    pub gpu_available: bool,
    pub gpu_memory_mb: Option<f64>,
}

impl SystemInfo {
    pub async fn collect() -> Result<Self> {
        // 收集系统信息的实现
        Ok(Self {
            total_memory_mb: 16384.0, // 示例值
            available_memory_mb: 8192.0,
            cpu_cores: 8,
            gpu_available: true,
            gpu_memory_mb: Some(8192.0),
        })
    }
}

// 缓存管理器
pub struct CacheManager {
    cache: Arc<RwLock<lru::LruCache<String, InferenceResponse>>>,
    max_size_mb: usize,
}

impl CacheManager {
    pub async fn new(max_size_mb: usize) -> Result<Self> {
        let cache_capacity = (max_size_mb * 1024 * 1024) / 1024; // 估算条目数
        let cache = Arc::new(RwLock::new(lru::LruCache::new(cache_capacity)));

        Ok(Self {
            cache,
            max_size_mb,
        })
    }

    pub async fn get(&self, request: &InferenceRequest) -> Result<Option<InferenceResponse>> {
        let cache_key = self.generate_cache_key(request);
        let cache = self.cache.read().await;
        Ok(cache.peek(&cache_key).cloned())
    }

    pub async fn put(&self, request: &InferenceRequest, response: &InferenceResponse) -> Result<()> {
        let cache_key = self.generate_cache_key(request);
        let mut cache = self.cache.write().await;
        cache.put(cache_key, response.clone());
        Ok(())
    }

    pub async fn clear(&self) -> Result<()> {
        let mut cache = self.cache.write().await;
        cache.clear();
        Ok(())
    }

    fn generate_cache_key(&self, request: &InferenceRequest) -> String {
        use std::collections::hash_map::DefaultHasher;
        use std::hash::{Hash, Hasher};

        let mut hasher = DefaultHasher::new();
        request.hash(&mut hasher);
        format!("{:x}", hasher.finish())
    }
}

// 性能监控器
pub struct PerformanceMonitor {
    metrics: Arc<RwLock<PerformanceMetrics>>,
    start_time: std::time::Instant,
}

impl PerformanceMonitor {
    pub async fn new() -> Result<Self> {
        Ok(Self {
            metrics: Arc::new(RwLock::new(PerformanceMetrics {
                total_requests: 0,
                successful_requests: 0,
                failed_requests: 0,
                average_latency_ms: 0.0,
                tokens_per_second: 0.0,
                memory_usage_mb: 0.0,
                gpu_utilization: 0.0,
                cache_hit_rate: 0.0,
            })),
            start_time: std::time::Instant::now(),
        })
    }

    pub async fn get_metrics(&self) -> Result<PerformanceMetrics> {
        let metrics = self.metrics.read().await;
        Ok(metrics.clone())
    }

    pub async fn record_request(&self, duration: std::time::Duration, success: bool) -> Result<()> {
        let mut metrics = self.metrics.write().await;

        metrics.total_requests += 1;
        if success {
            metrics.successful_requests += 1;
        } else {
            metrics.failed_requests += 1;
        }

        // 更新平均延迟
        let latency_ms = duration.as_millis() as f64;
        metrics.average_latency_ms =
            (metrics.average_latency_ms * (metrics.total_requests - 1) as f64 + latency_ms)
            / metrics.total_requests as f64;

        Ok(())
    }
}
```

---

## 4. 公共模块与工具文件

### 4.1 共享组件库

#### 4.1.1 基础组件设计

AI Studio 的共享组件库采用原子设计理念，将UI组件分为原子组件、分子组件、有机体组件和模板组件四个层次，确保组件的可复用性和一致性。

**组件层次结构**
```
组件设计层次:

┌─── 原子组件 (Atoms) ───┐
│ • Button              │ ← 按钮、输入框、标签等基础元素
│ • Input               │
│ • Label               │
│ • Icon                │
│ • Avatar              │
└───────────────────────┘
         ↓ 组合
┌─── 分子组件 (Molecules) ───┐
│ • SearchBox           │ ← 由原子组件组合而成的功能单元
│ • FormField           │
│ • MessageBubble       │
│ • ProgressBar         │
│ • Notification        │
└───────────────────────┘
         ↓ 组合
┌─── 有机体组件 (Organisms) ───┐
│ • Header              │ ← 由分子组件组合的复杂功能区域
│ • Sidebar             │
│ • ChatPanel           │
│ • ModelList           │
│ • SettingsPanel       │
└───────────────────────┘
         ↓ 组合
┌─── 模板组件 (Templates) ───┐
│ • ChatLayout          │ ← 完整的页面布局模板
│ • SettingsLayout      │
│ • DashboardLayout     │
│ • WelcomeLayout       │
└───────────────────────┘
```

#### 4.1.2 核心共享组件

**Button组件 - 按钮组件**
```vue
<!-- components/common/Button.vue -->
<template>
  <button
    :class="buttonClasses"
    :disabled="disabled || loading"
    :type="type"
    @click="handleClick"
  >
    <Icon
      v-if="loading"
      name="spinner"
      class="animate-spin mr-2"
      :size="iconSize"
    />
    <Icon
      v-else-if="icon && iconPosition === 'left'"
      :name="icon"
      class="mr-2"
      :size="iconSize"
    />

    <span v-if="$slots.default" class="button-text">
      <slot />
    </span>

    <Icon
      v-if="icon && iconPosition === 'right'"
      :name="icon"
      class="ml-2"
      :size="iconSize"
    />
  </button>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import Icon from './Icon.vue'

interface Props {
  variant?: 'primary' | 'secondary' | 'success' | 'warning' | 'error' | 'ghost' | 'link'
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl'
  disabled?: boolean
  loading?: boolean
  icon?: string
  iconPosition?: 'left' | 'right'
  type?: 'button' | 'submit' | 'reset'
  block?: boolean
  rounded?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  variant: 'primary',
  size: 'md',
  disabled: false,
  loading: false,
  iconPosition: 'left',
  type: 'button',
  block: false,
  rounded: false
})

interface Emits {
  (e: 'click', event: MouseEvent): void
}

const emit = defineEmits<Emits>()

const buttonClasses = computed(() => {
  const baseClasses = [
    'inline-flex',
    'items-center',
    'justify-center',
    'font-medium',
    'transition-all',
    'duration-200',
    'focus:outline-none',
    'focus:ring-2',
    'focus:ring-offset-2',
    'disabled:opacity-50',
    'disabled:cursor-not-allowed'
  ]

  // 尺寸样式
  const sizeClasses = {
    xs: ['px-2', 'py-1', 'text-xs', 'rounded'],
    sm: ['px-3', 'py-1.5', 'text-sm', 'rounded'],
    md: ['px-4', 'py-2', 'text-sm', 'rounded-md'],
    lg: ['px-6', 'py-3', 'text-base', 'rounded-md'],
    xl: ['px-8', 'py-4', 'text-lg', 'rounded-lg']
  }

  // 变体样式
  const variantClasses = {
    primary: [
      'bg-primary-600',
      'text-white',
      'hover:bg-primary-700',
      'focus:ring-primary-500',
      'active:bg-primary-800'
    ],
    secondary: [
      'bg-secondary-100',
      'text-secondary-900',
      'hover:bg-secondary-200',
      'focus:ring-secondary-500',
      'active:bg-secondary-300'
    ],
    success: [
      'bg-success-600',
      'text-white',
      'hover:bg-success-700',
      'focus:ring-success-500',
      'active:bg-success-800'
    ],
    warning: [
      'bg-warning-600',
      'text-white',
      'hover:bg-warning-700',
      'focus:ring-warning-500',
      'active:bg-warning-800'
    ],
    error: [
      'bg-error-600',
      'text-white',
      'hover:bg-error-700',
      'focus:ring-error-500',
      'active:bg-error-800'
    ],
    ghost: [
      'bg-transparent',
      'text-secondary-700',
      'hover:bg-secondary-100',
      'focus:ring-secondary-500',
      'active:bg-secondary-200'
    ],
    link: [
      'bg-transparent',
      'text-primary-600',
      'hover:text-primary-700',
      'focus:ring-primary-500',
      'active:text-primary-800',
      'underline-offset-4',
      'hover:underline'
    ]
  }

  const classes = [
    ...baseClasses,
    ...sizeClasses[props.size],
    ...variantClasses[props.variant]
  ]

  if (props.block) {
    classes.push('w-full')
  }

  if (props.rounded) {
    classes.push('rounded-full')
  }

  return classes
})

const iconSize = computed(() => {
  const sizeMap = {
    xs: 'xs',
    sm: 'sm',
    md: 'sm',
    lg: 'md',
    xl: 'lg'
  }
  return sizeMap[props.size]
})

const handleClick = (event: MouseEvent) => {
  if (!props.disabled && !props.loading) {
    emit('click', event)
  }
}
</script>

<style lang="scss" scoped>
.button-text {
  @apply truncate;
}

// 深色主题适配
.theme-dark {
  .bg-secondary-100 {
    @apply bg-secondary-800;
  }

  .text-secondary-900 {
    @apply text-secondary-100;
  }

  .hover\:bg-secondary-200:hover {
    @apply bg-secondary-700;
  }
}
</style>
```

**Modal组件 - 模态框组件**
```vue
<!-- components/common/Modal.vue -->
<template>
  <Teleport to="body">
    <Transition
      name="modal"
      @enter="onEnter"
      @after-enter="onAfterEnter"
      @leave="onLeave"
      @after-leave="onAfterLeave"
    >
      <div
        v-if="visible"
        class="modal-overlay"
        @click="handleOverlayClick"
      >
        <div
          ref="modalRef"
          :class="modalClasses"
          role="dialog"
          aria-modal="true"
          :aria-labelledby="titleId"
          @click.stop
        >
          <!-- 模态框头部 -->
          <div v-if="showHeader" class="modal-header">
            <h3 :id="titleId" class="modal-title">
              <slot name="title">{{ title }}</slot>
            </h3>

            <Button
              v-if="closable"
              variant="ghost"
              size="sm"
              icon="x"
              class="modal-close"
              @click="handleClose"
            />
          </div>

          <!-- 模态框内容 -->
          <div class="modal-body">
            <slot />
          </div>

          <!-- 模态框底部 -->
          <div v-if="showFooter" class="modal-footer">
            <slot name="footer">
              <div class="flex justify-end space-x-3">
                <Button
                  v-if="showCancel"
                  variant="secondary"
                  @click="handleCancel"
                >
                  {{ cancelText }}
                </Button>
                <Button
                  v-if="showConfirm"
                  :variant="confirmVariant"
                  :loading="confirmLoading"
                  @click="handleConfirm"
                >
                  {{ confirmText }}
                </Button>
              </div>
            </slot>
          </div>
        </div>
      </div>
    </Transition>
  </Teleport>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue'
import { onKeyStroke } from '@vueuse/core'
import Button from './Button.vue'

interface Props {
  visible?: boolean
  title?: string
  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full'
  closable?: boolean
  maskClosable?: boolean
  showHeader?: boolean
  showFooter?: boolean
  showCancel?: boolean
  showConfirm?: boolean
  cancelText?: string
  confirmText?: string
  confirmVariant?: 'primary' | 'success' | 'warning' | 'error'
  confirmLoading?: boolean
  destroyOnClose?: boolean
  zIndex?: number
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  title: '',
  size: 'md',
  closable: true,
  maskClosable: true,
  showHeader: true,
  showFooter: true,
  showCancel: true,
  showConfirm: true,
  cancelText: '取消',
  confirmText: '确定',
  confirmVariant: 'primary',
  confirmLoading: false,
  destroyOnClose: false,
  zIndex: 1000
})

interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'confirm'): void
  (e: 'cancel'): void
  (e: 'close'): void
  (e: 'opened'): void
  (e: 'closed'): void
}

const emit = defineEmits<Emits>()

const modalRef = ref<HTMLElement>()
const titleId = `modal-title-${Math.random().toString(36).substr(2, 9)}`

const modalClasses = computed(() => {
  const baseClasses = [
    'modal-container',
    'bg-white',
    'rounded-lg',
    'shadow-xl',
    'transform',
    'transition-all',
    'duration-300',
    'max-h-[90vh]',
    'overflow-hidden',
    'flex',
    'flex-col'
  ]

  const sizeClasses = {
    sm: ['w-96', 'max-w-sm'],
    md: ['w-[32rem]', 'max-w-md'],
    lg: ['w-[48rem]', 'max-w-2xl'],
    xl: ['w-[64rem]', 'max-w-4xl'],
    full: ['w-[95vw]', 'h-[95vh]', 'max-w-none']
  }

  return [
    ...baseClasses,
    ...sizeClasses[props.size]
  ]
})

// 键盘事件处理
onKeyStroke('Escape', () => {
  if (props.visible && props.closable) {
    handleClose()
  }
})

// 监听visible变化
watch(() => props.visible, (newVal) => {
  if (newVal) {
    document.body.style.overflow = 'hidden'
    nextTick(() => {
      modalRef.value?.focus()
    })
  } else {
    document.body.style.overflow = ''
  }
})

const handleOverlayClick = () => {
  if (props.maskClosable) {
    handleClose()
  }
}

const handleClose = () => {
  emit('update:visible', false)
  emit('close')
}

const handleConfirm = () => {
  emit('confirm')
}

const handleCancel = () => {
  emit('cancel')
  handleClose()
}

// 动画事件处理
const onEnter = () => {
  // 进入动画开始
}

const onAfterEnter = () => {
  emit('opened')
}

const onLeave = () => {
  // 离开动画开始
}

const onAfterLeave = () => {
  emit('closed')
  document.body.style.overflow = ''
}
</script>

<style lang="scss" scoped>
.modal-overlay {
  @apply fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4;
  z-index: v-bind(zIndex);
}

.modal-container {
  @apply relative;
}

.modal-header {
  @apply flex items-center justify-between p-6 border-b border-gray-200;
}

.modal-title {
  @apply text-lg font-semibold text-gray-900;
}

.modal-close {
  @apply -mr-2;
}

.modal-body {
  @apply flex-1 p-6 overflow-y-auto;
}

.modal-footer {
  @apply px-6 py-4 border-t border-gray-200 bg-gray-50;
}

// 动画样式
.modal-enter-active,
.modal-leave-active {
  transition: opacity 0.3s ease;

  .modal-container {
    transition: transform 0.3s ease;
  }
}

.modal-enter-from,
.modal-leave-to {
  opacity: 0;

  .modal-container {
    transform: scale(0.9) translateY(-20px);
  }
}

// 深色主题适配
.theme-dark {
  .modal-container {
    @apply bg-gray-800 text-white;
  }

  .modal-header {
    @apply border-gray-700;
  }

  .modal-title {
    @apply text-white;
  }

  .modal-footer {
    @apply border-gray-700 bg-gray-900;
  }
}
</style>
```

### 4.2 工具函数与辅助类

#### 4.2.1 通用工具函数

**utils/format.ts - 格式化工具**
```typescript
// utils/format.ts
import { format as dateFnsFormat, formatDistanceToNow, isValid } from 'date-fns'
import { zhCN, enUS } from 'date-fns/locale'

// 文件大小格式化
export const formatFileSize = (bytes: number, decimals: number = 2): string => {
  if (bytes === 0) return '0 B'

  const k = 1024
  const dm = decimals < 0 ? 0 : decimals
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB']

  const i = Math.floor(Math.log(bytes) / Math.log(k))

  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i]
}

// 数字格式化
export const formatNumber = (
  num: number,
  options: {
    locale?: string
    minimumFractionDigits?: number
    maximumFractionDigits?: number
    useGrouping?: boolean
  } = {}
): string => {
  const {
    locale = 'zh-CN',
    minimumFractionDigits = 0,
    maximumFractionDigits = 2,
    useGrouping = true
  } = options

  return new Intl.NumberFormat(locale, {
    minimumFractionDigits,
    maximumFractionDigits,
    useGrouping
  }).format(num)
}

// 百分比格式化
export const formatPercentage = (
  value: number,
  decimals: number = 1
): string => {
  return `${(value * 100).toFixed(decimals)}%`
}

// 日期格式化
export const formatDate = (
  date: Date | string | number,
  formatStr: string = 'yyyy-MM-dd HH:mm:ss',
  locale: string = 'zh-CN'
): string => {
  const dateObj = new Date(date)

  if (!isValid(dateObj)) {
    return '无效日期'
  }

  const localeObj = locale === 'zh-CN' ? zhCN : enUS

  return dateFnsFormat(dateObj, formatStr, { locale: localeObj })
}

// 相对时间格式化
export const formatRelativeTime = (
  date: Date | string | number,
  locale: string = 'zh-CN'
): string => {
  const dateObj = new Date(date)

  if (!isValid(dateObj)) {
    return '无效日期'
  }

  const localeObj = locale === 'zh-CN' ? zhCN : enUS

  return formatDistanceToNow(dateObj, {
    addSuffix: true,
    locale: localeObj
  })
}

// 时长格式化
export const formatDuration = (milliseconds: number): string => {
  const seconds = Math.floor(milliseconds / 1000)
  const minutes = Math.floor(seconds / 60)
  const hours = Math.floor(minutes / 60)
  const days = Math.floor(hours / 24)

  if (days > 0) {
    return `${days}天 ${hours % 24}小时`
  } else if (hours > 0) {
    return `${hours}小时 ${minutes % 60}分钟`
  } else if (minutes > 0) {
    return `${minutes}分钟 ${seconds % 60}秒`
  } else {
    return `${seconds}秒`
  }
}

// 货币格式化
export const formatCurrency = (
  amount: number,
  currency: string = 'CNY',
  locale: string = 'zh-CN'
): string => {
  return new Intl.NumberFormat(locale, {
    style: 'currency',
    currency
  }).format(amount)
}

// 文本截断
export const truncateText = (
  text: string,
  maxLength: number,
  suffix: string = '...'
): string => {
  if (text.length <= maxLength) {
    return text
  }

  return text.slice(0, maxLength - suffix.length) + suffix
}

// 首字母大写
export const capitalize = (str: string): string => {
  return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase()
}

// 驼峰命名转换
export const camelCase = (str: string): string => {
  return str
    .replace(/(?:^\w|[A-Z]|\b\w)/g, (word, index) => {
      return index === 0 ? word.toLowerCase() : word.toUpperCase()
    })
    .replace(/\s+/g, '')
}

// 短横线命名转换
export const kebabCase = (str: string): string => {
  return str
    .replace(/([a-z])([A-Z])/g, '$1-$2')
    .replace(/[\s_]+/g, '-')
    .toLowerCase()
}

// 下划线命名转换
export const snakeCase = (str: string): string => {
  return str
    .replace(/([a-z])([A-Z])/g, '$1_$2')
    .replace(/[\s-]+/g, '_')
    .toLowerCase()
}

// 掩码处理
export const maskString = (
  str: string,
  start: number = 3,
  end: number = 4,
  mask: string = '*'
): string => {
  if (str.length <= start + end) {
    return str
  }

  const startStr = str.slice(0, start)
  const endStr = str.slice(-end)
  const maskLength = str.length - start - end

  return startStr + mask.repeat(maskLength) + endStr
}

// 高亮文本
export const highlightText = (
  text: string,
  keyword: string,
  className: string = 'highlight'
): string => {
  if (!keyword) return text

  const regex = new RegExp(`(${keyword})`, 'gi')
  return text.replace(regex, `<span class="${className}">$1</span>`)
}
```

---

## 5. 数据库设计

### 5.1 SQLite关系型数据库

#### 5.1.1 数据库架构设计

AI Studio 采用 SQLite 作为主要的关系型数据库，负责存储结构化数据，包括用户配置、聊天记录、模型信息、系统日志等。SQLite 的选择基于以下考虑：

**选择理由：**
- **零配置**：无需安装和配置数据库服务器
- **高性能**：对于桌面应用的数据量，性能表现优异
- **可靠性**：ACID事务支持，数据安全可靠
- **跨平台**：完美支持Windows和macOS
- **轻量级**：占用资源少，适合桌面应用

#### 5.1.2 数据库表结构设计

**核心表结构**
```sql
-- ===================================================
-- AI Studio 数据库表结构设计
-- 版本: v1.0
-- 创建日期: 2025-01-11
-- ===================================================

-- 用户配置表
CREATE TABLE user_settings (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    key TEXT NOT NULL UNIQUE,           -- 配置键名
    value TEXT NOT NULL,                -- 配置值(JSON格式)
    category TEXT NOT NULL DEFAULT 'general', -- 配置分类
    description TEXT,                   -- 配置描述
    data_type TEXT NOT NULL DEFAULT 'string', -- 数据类型
    is_encrypted BOOLEAN DEFAULT FALSE, -- 是否加密存储
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,

    INDEX idx_settings_key (key),
    INDEX idx_settings_category (category)
);

-- 聊天会话表
CREATE TABLE chat_sessions (
    id TEXT PRIMARY KEY,                -- UUID格式的会话ID
    title TEXT NOT NULL,                -- 会话标题
    model_id TEXT NOT NULL,             -- 使用的模型ID
    system_prompt TEXT,                 -- 系统提示词
    temperature REAL DEFAULT 0.7,      -- 温度参数
    max_tokens INTEGER DEFAULT 2048,   -- 最大token数
    top_p REAL DEFAULT 0.9,            -- top_p参数
    frequency_penalty REAL DEFAULT 0.0, -- 频率惩罚
    presence_penalty REAL DEFAULT 0.0,  -- 存在惩罚
    is_archived BOOLEAN DEFAULT FALSE,  -- 是否归档
    is_pinned BOOLEAN DEFAULT FALSE,    -- 是否置顶
    tags TEXT,                          -- 标签(JSON数组)
    metadata TEXT,                      -- 元数据(JSON)
    message_count INTEGER DEFAULT 0,    -- 消息数量
    total_tokens INTEGER DEFAULT 0,     -- 总token数
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,

    INDEX idx_sessions_model (model_id),
    INDEX idx_sessions_created (created_at),
    INDEX idx_sessions_updated (updated_at),
    INDEX idx_sessions_archived (is_archived),
    INDEX idx_sessions_pinned (is_pinned)
);

-- 聊天消息表
CREATE TABLE chat_messages (
    id TEXT PRIMARY KEY,                -- UUID格式的消息ID
    session_id TEXT NOT NULL,           -- 所属会话ID
    role TEXT NOT NULL CHECK (role IN ('user', 'assistant', 'system')), -- 消息角色
    content TEXT NOT NULL,              -- 消息内容
    content_type TEXT DEFAULT 'text',   -- 内容类型
    raw_content TEXT,                   -- 原始内容
    tokens INTEGER DEFAULT 0,           -- token数量
    model_id TEXT,                      -- 生成消息的模型ID
    parent_id TEXT,                     -- 父消息ID(用于分支对话)
    children_ids TEXT,                  -- 子消息ID列表(JSON数组)
    status TEXT DEFAULT 'sent' CHECK (status IN ('sending', 'sent', 'error', 'deleted')),
    error_message TEXT,                 -- 错误信息
    metadata TEXT,                      -- 元数据(JSON)
    attachments TEXT,                   -- 附件信息(JSON数组)
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (session_id) REFERENCES chat_sessions(id) ON DELETE CASCADE,
    INDEX idx_messages_session (session_id),
    INDEX idx_messages_role (role),
    INDEX idx_messages_created (created_at),
    INDEX idx_messages_status (status),
    INDEX idx_messages_parent (parent_id)
);

-- 知识库表
CREATE TABLE knowledge_bases (
    id TEXT PRIMARY KEY,                -- UUID格式的知识库ID
    name TEXT NOT NULL,                 -- 知识库名称
    description TEXT,                   -- 知识库描述
    type TEXT DEFAULT 'general',        -- 知识库类型
    embedding_model TEXT NOT NULL,      -- 向量化模型
    chunk_size INTEGER DEFAULT 512,     -- 分块大小
    chunk_overlap INTEGER DEFAULT 50,   -- 分块重叠
    vector_dimension INTEGER DEFAULT 768, -- 向量维度
    document_count INTEGER DEFAULT 0,   -- 文档数量
    chunk_count INTEGER DEFAULT 0,      -- 分块数量
    total_size INTEGER DEFAULT 0,       -- 总大小(字节)
    status TEXT DEFAULT 'active' CHECK (status IN ('active', 'processing', 'error', 'archived')),
    config TEXT,                        -- 配置信息(JSON)
    metadata TEXT,                      -- 元数据(JSON)
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,

    INDEX idx_kb_name (name),
    INDEX idx_kb_type (type),
    INDEX idx_kb_status (status),
    INDEX idx_kb_created (created_at)
);

-- 文档表
CREATE TABLE documents (
    id TEXT PRIMARY KEY,                -- UUID格式的文档ID
    knowledge_base_id TEXT NOT NULL,    -- 所属知识库ID
    filename TEXT NOT NULL,             -- 文件名
    original_filename TEXT NOT NULL,    -- 原始文件名
    file_path TEXT NOT NULL,            -- 文件路径
    file_type TEXT NOT NULL,            -- 文件类型
    file_size INTEGER NOT NULL,         -- 文件大小(字节)
    mime_type TEXT,                     -- MIME类型
    encoding TEXT DEFAULT 'utf-8',      -- 文件编码
    language TEXT DEFAULT 'zh',         -- 文档语言
    title TEXT,                         -- 文档标题
    author TEXT,                        -- 文档作者
    summary TEXT,                       -- 文档摘要
    content_preview TEXT,               -- 内容预览
    chunk_count INTEGER DEFAULT 0,      -- 分块数量
    processing_status TEXT DEFAULT 'pending' CHECK (
        processing_status IN ('pending', 'processing', 'completed', 'error', 'skipped')
    ),
    processing_progress REAL DEFAULT 0.0, -- 处理进度(0-1)
    error_message TEXT,                 -- 错误信息
    metadata TEXT,                      -- 元数据(JSON)
    tags TEXT,                          -- 标签(JSON数组)
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    processed_at DATETIME,              -- 处理完成时间

    FOREIGN KEY (knowledge_base_id) REFERENCES knowledge_bases(id) ON DELETE CASCADE,
    INDEX idx_docs_kb (knowledge_base_id),
    INDEX idx_docs_filename (filename),
    INDEX idx_docs_type (file_type),
    INDEX idx_docs_status (processing_status),
    INDEX idx_docs_created (created_at)
);

-- 文档分块表
CREATE TABLE document_chunks (
    id TEXT PRIMARY KEY,                -- UUID格式的分块ID
    document_id TEXT NOT NULL,          -- 所属文档ID
    knowledge_base_id TEXT NOT NULL,    -- 所属知识库ID
    chunk_index INTEGER NOT NULL,       -- 分块索引
    content TEXT NOT NULL,              -- 分块内容
    content_hash TEXT NOT NULL,         -- 内容哈希
    token_count INTEGER DEFAULT 0,      -- token数量
    char_count INTEGER DEFAULT 0,       -- 字符数量
    start_position INTEGER DEFAULT 0,   -- 在原文档中的起始位置
    end_position INTEGER DEFAULT 0,     -- 在原文档中的结束位置
    embedding_status TEXT DEFAULT 'pending' CHECK (
        embedding_status IN ('pending', 'processing', 'completed', 'error')
    ),
    embedding_model TEXT,               -- 向量化模型
    vector_id TEXT,                     -- 向量数据库中的ID
    metadata TEXT,                      -- 元数据(JSON)
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (document_id) REFERENCES documents(id) ON DELETE CASCADE,
    FOREIGN KEY (knowledge_base_id) REFERENCES knowledge_bases(id) ON DELETE CASCADE,
    INDEX idx_chunks_doc (document_id),
    INDEX idx_chunks_kb (knowledge_base_id),
    INDEX idx_chunks_index (chunk_index),
    INDEX idx_chunks_hash (content_hash),
    INDEX idx_chunks_status (embedding_status),
    INDEX idx_chunks_vector (vector_id),
    UNIQUE (document_id, chunk_index)
);

-- 模型信息表
CREATE TABLE models (
    id TEXT PRIMARY KEY,                -- 模型ID
    name TEXT NOT NULL,                 -- 模型名称
    display_name TEXT NOT NULL,         -- 显示名称
    description TEXT,                   -- 模型描述
    type TEXT NOT NULL CHECK (type IN ('llm', 'embedding', 'multimodal')), -- 模型类型
    provider TEXT NOT NULL,             -- 提供商
    version TEXT,                       -- 版本号
    architecture TEXT,                  -- 架构类型
    parameter_count TEXT,               -- 参数数量
    context_length INTEGER DEFAULT 2048, -- 上下文长度
    file_path TEXT,                     -- 本地文件路径
    file_size INTEGER DEFAULT 0,        -- 文件大小(字节)
    download_url TEXT,                  -- 下载地址
    download_status TEXT DEFAULT 'not_downloaded' CHECK (
        download_status IN ('not_downloaded', 'downloading', 'downloaded', 'error', 'corrupted')
    ),
    download_progress REAL DEFAULT 0.0, -- 下载进度(0-1)
    load_status TEXT DEFAULT 'unloaded' CHECK (
        load_status IN ('unloaded', 'loading', 'loaded', 'error')
    ),
    memory_usage INTEGER DEFAULT 0,     -- 内存使用量(MB)
    gpu_memory_usage INTEGER DEFAULT 0, -- GPU内存使用量(MB)
    supported_features TEXT,            -- 支持的功能(JSON数组)
    requirements TEXT,                  -- 系统要求(JSON)
    config TEXT,                        -- 模型配置(JSON)
    metadata TEXT,                      -- 元数据(JSON)
    is_default BOOLEAN DEFAULT FALSE,   -- 是否为默认模型
    is_enabled BOOLEAN DEFAULT TRUE,    -- 是否启用
    usage_count INTEGER DEFAULT 0,      -- 使用次数
    last_used_at DATETIME,              -- 最后使用时间
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,

    INDEX idx_models_type (type),
    INDEX idx_models_provider (provider),
    INDEX idx_models_status (download_status),
    INDEX idx_models_load_status (load_status),
    INDEX idx_models_default (is_default),
    INDEX idx_models_enabled (is_enabled),
    INDEX idx_models_usage (usage_count),
    INDEX idx_models_last_used (last_used_at)
);

-- 插件信息表
CREATE TABLE plugins (
    id TEXT PRIMARY KEY,                -- 插件ID
    name TEXT NOT NULL,                 -- 插件名称
    display_name TEXT NOT NULL,         -- 显示名称
    description TEXT,                   -- 插件描述
    version TEXT NOT NULL,              -- 版本号
    author TEXT,                        -- 作者
    homepage TEXT,                      -- 主页地址
    repository TEXT,                    -- 仓库地址
    license TEXT,                       -- 许可证
    category TEXT DEFAULT 'general',    -- 插件分类
    tags TEXT,                          -- 标签(JSON数组)
    file_path TEXT NOT NULL,            -- 插件文件路径
    file_size INTEGER DEFAULT 0,        -- 文件大小(字节)
    install_status TEXT DEFAULT 'installed' CHECK (
        install_status IN ('installed', 'installing', 'uninstalling', 'error')
    ),
    enable_status TEXT DEFAULT 'enabled' CHECK (
        enable_status IN ('enabled', 'disabled')
    ),
    config TEXT,                        -- 插件配置(JSON)
    permissions TEXT,                   -- 权限列表(JSON数组)
    dependencies TEXT,                  -- 依赖列表(JSON数组)
    api_version TEXT DEFAULT '1.0',     -- API版本
    min_app_version TEXT,               -- 最小应用版本要求
    max_app_version TEXT,               -- 最大应用版本要求
    metadata TEXT,                      -- 元数据(JSON)
    usage_count INTEGER DEFAULT 0,      -- 使用次数
    error_count INTEGER DEFAULT 0,      -- 错误次数
    last_used_at DATETIME,              -- 最后使用时间
    last_error_at DATETIME,             -- 最后错误时间
    installed_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,

    INDEX idx_plugins_name (name),
    INDEX idx_plugins_category (category),
    INDEX idx_plugins_status (install_status),
    INDEX idx_plugins_enabled (enable_status),
    INDEX idx_plugins_usage (usage_count),
    INDEX idx_plugins_version (version)
);

-- 系统日志表
CREATE TABLE system_logs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    level TEXT NOT NULL CHECK (level IN ('DEBUG', 'INFO', 'WARN', 'ERROR', 'FATAL')),
    category TEXT NOT NULL,             -- 日志分类
    module TEXT NOT NULL,               -- 模块名称
    message TEXT NOT NULL,              -- 日志消息
    details TEXT,                       -- 详细信息(JSON)
    user_id TEXT,                       -- 用户ID(如果适用)
    session_id TEXT,                    -- 会话ID(如果适用)
    request_id TEXT,                    -- 请求ID(如果适用)
    ip_address TEXT,                    -- IP地址
    user_agent TEXT,                    -- 用户代理
    stack_trace TEXT,                   -- 堆栈跟踪
    metadata TEXT,                      -- 元数据(JSON)
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,

    INDEX idx_logs_level (level),
    INDEX idx_logs_category (category),
    INDEX idx_logs_module (module),
    INDEX idx_logs_created (created_at),
    INDEX idx_logs_session (session_id),
    INDEX idx_logs_request (request_id)
);

-- 性能指标表
CREATE TABLE performance_metrics (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    metric_name TEXT NOT NULL,          -- 指标名称
    metric_value REAL NOT NULL,         -- 指标值
    metric_unit TEXT,                   -- 指标单位
    category TEXT NOT NULL,             -- 指标分类
    tags TEXT,                          -- 标签(JSON)
    metadata TEXT,                      -- 元数据(JSON)
    recorded_at DATETIME DEFAULT CURRENT_TIMESTAMP,

    INDEX idx_metrics_name (metric_name),
    INDEX idx_metrics_category (category),
    INDEX idx_metrics_recorded (recorded_at)
);

-- 文件缓存表
CREATE TABLE file_cache (
    id TEXT PRIMARY KEY,                -- 缓存ID
    cache_key TEXT NOT NULL UNIQUE,     -- 缓存键
    file_path TEXT NOT NULL,            -- 文件路径
    file_size INTEGER NOT NULL,         -- 文件大小
    mime_type TEXT,                     -- MIME类型
    checksum TEXT NOT NULL,             -- 文件校验和
    access_count INTEGER DEFAULT 0,     -- 访问次数
    last_accessed_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    expires_at DATETIME,                -- 过期时间
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,

    INDEX idx_cache_key (cache_key),
    INDEX idx_cache_accessed (last_accessed_at),
    INDEX idx_cache_expires (expires_at)
);
```

#### 5.1.3 数据库索引优化

**索引策略设计**
```sql
-- ===================================================
-- 数据库索引优化策略
-- ===================================================

-- 复合索引优化
CREATE INDEX idx_messages_session_created ON chat_messages(session_id, created_at DESC);
CREATE INDEX idx_messages_session_role ON chat_messages(session_id, role);
CREATE INDEX idx_chunks_kb_status ON document_chunks(knowledge_base_id, embedding_status);
CREATE INDEX idx_docs_kb_status ON documents(knowledge_base_id, processing_status);
CREATE INDEX idx_logs_category_level_created ON system_logs(category, level, created_at DESC);

-- 部分索引(条件索引)
CREATE INDEX idx_sessions_active ON chat_sessions(updated_at DESC) WHERE is_archived = FALSE;
CREATE INDEX idx_models_available ON models(name) WHERE is_enabled = TRUE AND download_status = 'downloaded';
CREATE INDEX idx_plugins_enabled ON plugins(name) WHERE enable_status = 'enabled';
CREATE INDEX idx_chunks_pending ON document_chunks(created_at) WHERE embedding_status = 'pending';

-- 全文搜索索引
CREATE VIRTUAL TABLE chat_messages_fts USING fts5(
    content,
    content=chat_messages,
    content_rowid=rowid
);

-- FTS索引触发器
CREATE TRIGGER chat_messages_fts_insert AFTER INSERT ON chat_messages BEGIN
    INSERT INTO chat_messages_fts(rowid, content) VALUES (new.rowid, new.content);
END;

CREATE TRIGGER chat_messages_fts_delete AFTER DELETE ON chat_messages BEGIN
    INSERT INTO chat_messages_fts(chat_messages_fts, rowid, content) VALUES('delete', old.rowid, old.content);
END;

CREATE TRIGGER chat_messages_fts_update AFTER UPDATE ON chat_messages BEGIN
    INSERT INTO chat_messages_fts(chat_messages_fts, rowid, content) VALUES('delete', old.rowid, old.content);
    INSERT INTO chat_messages_fts(rowid, content) VALUES (new.rowid, new.content);
END;
```

### 5.2 ChromaDB向量数据库

#### 5.2.1 向量数据库架构

AI Studio 使用 ChromaDB 作为向量数据库，专门用于存储和检索文档的向量表示，支持语义搜索和RAG（检索增强生成）功能。

**ChromaDB选择理由：**
- **高性能**：专为向量搜索优化，支持大规模向量检索
- **易集成**：Python/Rust绑定，与应用无缝集成
- **多种距离**：支持余弦相似度、欧几里得距离等多种相似度计算
- **持久化**：支持数据持久化存储
- **可扩展**：支持分布式部署和水平扩展

#### 5.2.2 向量数据库设计

**Collection设计结构**
```python
# ChromaDB Collection 设计规范
# ===================================================

# 知识库向量集合设计
knowledge_base_collections = {
    # 集合命名规范: kb_{knowledge_base_id}
    "collection_name": "kb_550e8400-e29b-41d4-a716-446655440000",

    # 向量维度配置
    "embedding_dimension": 768,  # 根据使用的embedding模型确定

    # 距离度量方式
    "distance_metric": "cosine",  # cosine, euclidean, manhattan

    # 元数据结构
    "metadata_schema": {
        "document_id": "string",        # 文档ID
        "chunk_id": "string",           # 分块ID
        "chunk_index": "integer",       # 分块索引
        "document_title": "string",     # 文档标题
        "document_type": "string",      # 文档类型
        "file_path": "string",          # 文件路径
        "content_preview": "string",    # 内容预览
        "token_count": "integer",       # token数量
        "char_count": "integer",        # 字符数量
        "language": "string",           # 语言
        "tags": "array",               # 标签数组
        "created_at": "datetime",       # 创建时间
        "updated_at": "datetime",       # 更新时间
        "embedding_model": "string",    # 向量化模型
        "chunk_type": "string",         # 分块类型: text, table, image, code
        "section_title": "string",      # 章节标题
        "page_number": "integer",       # 页码
        "confidence_score": "float",    # 置信度分数
    }
}

# 对话历史向量集合设计
conversation_collections = {
    "collection_name": "conversations",
    "embedding_dimension": 768,
    "distance_metric": "cosine",
    "metadata_schema": {
        "session_id": "string",         # 会话ID
        "message_id": "string",         # 消息ID
        "role": "string",               # 角色: user, assistant, system
        "model_id": "string",           # 模型ID
        "timestamp": "datetime",        # 时间戳
        "token_count": "integer",       # token数量
        "conversation_turn": "integer", # 对话轮次
        "topic": "string",              # 话题
        "intent": "string",             # 意图
        "sentiment": "string",          # 情感
        "quality_score": "float",       # 质量分数
    }
}

# 模型向量集合设计
model_collections = {
    "collection_name": "model_embeddings",
    "embedding_dimension": 768,
    "distance_metric": "cosine",
    "metadata_schema": {
        "model_id": "string",           # 模型ID
        "model_name": "string",         # 模型名称
        "model_type": "string",         # 模型类型
        "description": "string",        # 描述
        "capabilities": "array",        # 能力列表
        "parameters": "object",         # 参数配置
        "performance_metrics": "object", # 性能指标
        "use_cases": "array",           # 使用场景
        "tags": "array",               # 标签
        "created_at": "datetime",       # 创建时间
    }
}
```

#### 5.2.3 向量数据库操作接口

**ChromaDB操作封装**
```rust
// src/core/database/chroma.rs
use chroma_rs::{ChromaClient, Collection, QueryOptions, EmbeddingFunction};
use serde::{Deserialize, Serialize};
use anyhow::{Result, anyhow};
use std::collections::HashMap;
use uuid::Uuid;

// ChromaDB管理器
pub struct ChromaManager {
    client: ChromaClient,
    collections: HashMap<String, Collection>,
    embedding_function: Box<dyn EmbeddingFunction>,
}

// 向量文档结构
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VectorDocument {
    pub id: String,
    pub content: String,
    pub embedding: Vec<f32>,
    pub metadata: HashMap<String, serde_json::Value>,
}

// 搜索结果结构
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SearchResult {
    pub id: String,
    pub content: String,
    pub score: f32,
    pub metadata: HashMap<String, serde_json::Value>,
    pub distance: f32,
}

// 搜索选项
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SearchOptions {
    pub limit: usize,
    pub threshold: f32,
    pub include_metadata: bool,
    pub include_content: bool,
    pub filters: Option<HashMap<String, serde_json::Value>>,
}

impl Default for SearchOptions {
    fn default() -> Self {
        Self {
            limit: 10,
            threshold: 0.7,
            include_metadata: true,
            include_content: true,
            filters: None,
        }
    }
}

impl ChromaManager {
    // 创建新的ChromaDB管理器
    pub async fn new(
        host: &str,
        port: u16,
        embedding_function: Box<dyn EmbeddingFunction>,
    ) -> Result<Self> {
        let client = ChromaClient::new(&format!("http://{}:{}", host, port))?;

        Ok(Self {
            client,
            collections: HashMap::new(),
            embedding_function,
        })
    }

    // 创建或获取集合
    pub async fn get_or_create_collection(
        &mut self,
        name: &str,
        dimension: usize,
        distance_metric: &str,
    ) -> Result<&Collection> {
        if !self.collections.contains_key(name) {
            let collection = self.client
                .create_collection(name)
                .dimension(dimension)
                .distance_metric(distance_metric)
                .build()
                .await?;

            self.collections.insert(name.to_string(), collection);
        }

        Ok(self.collections.get(name).unwrap())
    }

    // 添加文档到向量数据库
    pub async fn add_documents(
        &mut self,
        collection_name: &str,
        documents: Vec<VectorDocument>,
    ) -> Result<()> {
        let collection = self.collections.get_mut(collection_name)
            .ok_or_else(|| anyhow!("Collection {} not found", collection_name))?;

        let ids: Vec<String> = documents.iter().map(|d| d.id.clone()).collect();
        let contents: Vec<String> = documents.iter().map(|d| d.content.clone()).collect();
        let embeddings: Vec<Vec<f32>> = documents.iter().map(|d| d.embedding.clone()).collect();
        let metadatas: Vec<HashMap<String, serde_json::Value>> =
            documents.iter().map(|d| d.metadata.clone()).collect();

        collection
            .add()
            .ids(ids)
            .documents(contents)
            .embeddings(embeddings)
            .metadatas(metadatas)
            .execute()
            .await?;

        Ok(())
    }

    // 批量添加文档
    pub async fn add_documents_batch(
        &mut self,
        collection_name: &str,
        documents: Vec<VectorDocument>,
        batch_size: usize,
    ) -> Result<()> {
        for chunk in documents.chunks(batch_size) {
            self.add_documents(collection_name, chunk.to_vec()).await?;

            // 添加延迟以避免过载
            tokio::time::sleep(tokio::time::Duration::from_millis(100)).await;
        }

        Ok(())
    }

    // 语义搜索
    pub async fn search(
        &self,
        collection_name: &str,
        query: &str,
        options: SearchOptions,
    ) -> Result<Vec<SearchResult>> {
        let collection = self.collections.get(collection_name)
            .ok_or_else(|| anyhow!("Collection {} not found", collection_name))?;

        // 生成查询向量
        let query_embedding = self.embedding_function.embed_query(query).await?;

        // 构建查询选项
        let mut query_builder = collection
            .query()
            .query_embeddings(vec![query_embedding])
            .n_results(options.limit);

        if let Some(filters) = options.filters {
            query_builder = query_builder.where_metadata(filters);
        }

        if options.include_metadata {
            query_builder = query_builder.include_metadata();
        }

        if options.include_content {
            query_builder = query_builder.include_documents();
        }

        let results = query_builder.execute().await?;

        // 转换结果格式
        let mut search_results = Vec::new();

        for (i, id) in results.ids[0].iter().enumerate() {
            let score = results.distances[0][i];

            // 过滤低于阈值的结果
            if score >= options.threshold {
                let content = if options.include_content {
                    results.documents[0][i].clone().unwrap_or_default()
                } else {
                    String::new()
                };

                let metadata = if options.include_metadata {
                    results.metadatas[0][i].clone().unwrap_or_default()
                } else {
                    HashMap::new()
                };

                search_results.push(SearchResult {
                    id: id.clone(),
                    content,
                    score,
                    metadata,
                    distance: score,
                });
            }
        }

        // 按相似度排序
        search_results.sort_by(|a, b| b.score.partial_cmp(&a.score).unwrap());

        Ok(search_results)
    }

    // 混合搜索(向量搜索 + 关键词搜索)
    pub async fn hybrid_search(
        &self,
        collection_name: &str,
        query: &str,
        keywords: Vec<String>,
        options: SearchOptions,
    ) -> Result<Vec<SearchResult>> {
        // 向量搜索
        let vector_results = self.search(collection_name, query, options.clone()).await?;

        // 关键词过滤
        let filtered_results: Vec<SearchResult> = vector_results
            .into_iter()
            .filter(|result| {
                if keywords.is_empty() {
                    return true;
                }

                let content_lower = result.content.to_lowercase();
                keywords.iter().any(|keyword| {
                    content_lower.contains(&keyword.to_lowercase())
                })
            })
            .collect();

        Ok(filtered_results)
    }

    // 删除文档
    pub async fn delete_documents(
        &mut self,
        collection_name: &str,
        document_ids: Vec<String>,
    ) -> Result<()> {
        let collection = self.collections.get_mut(collection_name)
            .ok_or_else(|| anyhow!("Collection {} not found", collection_name))?;

        collection
            .delete()
            .ids(document_ids)
            .execute()
            .await?;

        Ok(())
    }

    // 更新文档
    pub async fn update_documents(
        &mut self,
        collection_name: &str,
        documents: Vec<VectorDocument>,
    ) -> Result<()> {
        let collection = self.collections.get_mut(collection_name)
            .ok_or_else(|| anyhow!("Collection {} not found", collection_name))?;

        let ids: Vec<String> = documents.iter().map(|d| d.id.clone()).collect();
        let contents: Vec<String> = documents.iter().map(|d| d.content.clone()).collect();
        let embeddings: Vec<Vec<f32>> = documents.iter().map(|d| d.embedding.clone()).collect();
        let metadatas: Vec<HashMap<String, serde_json::Value>> =
            documents.iter().map(|d| d.metadata.clone()).collect();

        collection
            .update()
            .ids(ids)
            .documents(contents)
            .embeddings(embeddings)
            .metadatas(metadatas)
            .execute()
            .await?;

        Ok(())
    }

    // 获取集合统计信息
    pub async fn get_collection_stats(&self, collection_name: &str) -> Result<CollectionStats> {
        let collection = self.collections.get(collection_name)
            .ok_or_else(|| anyhow!("Collection {} not found", collection_name))?;

        let count = collection.count().await?;

        Ok(CollectionStats {
            name: collection_name.to_string(),
            document_count: count,
            dimension: collection.dimension(),
            distance_metric: collection.distance_metric().to_string(),
        })
    }

    // 清空集合
    pub async fn clear_collection(&mut self, collection_name: &str) -> Result<()> {
        let collection = self.collections.get_mut(collection_name)
            .ok_or_else(|| anyhow!("Collection {} not found", collection_name))?;

        collection.delete().execute().await?;

        Ok(())
    }

    // 删除集合
    pub async fn delete_collection(&mut self, collection_name: &str) -> Result<()> {
        self.client.delete_collection(collection_name).await?;
        self.collections.remove(collection_name);

        Ok(())
    }

    // 备份集合
    pub async fn backup_collection(
        &self,
        collection_name: &str,
        backup_path: &str,
    ) -> Result<()> {
        let collection = self.collections.get(collection_name)
            .ok_or_else(|| anyhow!("Collection {} not found", collection_name))?;

        // 获取所有文档
        let results = collection
            .get()
            .include_documents()
            .include_metadata()
            .include_embeddings()
            .execute()
            .await?;

        // 序列化并保存到文件
        let backup_data = serde_json::to_string_pretty(&results)?;
        tokio::fs::write(backup_path, backup_data).await?;

        Ok(())
    }

    // 恢复集合
    pub async fn restore_collection(
        &mut self,
        collection_name: &str,
        backup_path: &str,
        dimension: usize,
        distance_metric: &str,
    ) -> Result<()> {
        // 读取备份文件
        let backup_data = tokio::fs::read_to_string(backup_path).await?;
        let results: serde_json::Value = serde_json::from_str(&backup_data)?;

        // 创建新集合
        self.get_or_create_collection(collection_name, dimension, distance_metric).await?;

        // 恢复数据
        if let Some(ids) = results["ids"].as_array() {
            let documents: Vec<VectorDocument> = ids
                .iter()
                .enumerate()
                .map(|(i, id)| {
                    VectorDocument {
                        id: id.as_str().unwrap_or_default().to_string(),
                        content: results["documents"][i].as_str().unwrap_or_default().to_string(),
                        embedding: results["embeddings"][i]
                            .as_array()
                            .unwrap_or(&vec![])
                            .iter()
                            .map(|v| v.as_f64().unwrap_or(0.0) as f32)
                            .collect(),
                        metadata: results["metadatas"][i]
                            .as_object()
                            .unwrap_or(&serde_json::Map::new())
                            .iter()
                            .map(|(k, v)| (k.clone(), v.clone()))
                            .collect(),
                    }
                })
                .collect();

            self.add_documents_batch(collection_name, documents, 100).await?;
        }

        Ok(())
    }
}

// 集合统计信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CollectionStats {
    pub name: String,
    pub document_count: usize,
    pub dimension: usize,
    pub distance_metric: String,
}

// 向量化函数接口
#[async_trait::async_trait]
pub trait EmbeddingFunction: Send + Sync {
    async fn embed_documents(&self, texts: Vec<String>) -> Result<Vec<Vec<f32>>>;
    async fn embed_query(&self, text: &str) -> Result<Vec<f32>>;
    fn dimension(&self) -> usize;
    fn model_name(&self) -> &str;
}

// 本地向量化实现
pub struct LocalEmbeddingFunction {
    model_name: String,
    dimension: usize,
    // 这里可以集成具体的向量化模型
}

#[async_trait::async_trait]
impl EmbeddingFunction for LocalEmbeddingFunction {
    async fn embed_documents(&self, texts: Vec<String>) -> Result<Vec<Vec<f32>>> {
        // 实现批量文档向量化
        let mut embeddings = Vec::new();

        for text in texts {
            let embedding = self.embed_query(&text).await?;
            embeddings.push(embedding);
        }

        Ok(embeddings)
    }

    async fn embed_query(&self, text: &str) -> Result<Vec<f32>> {
        // 实现单个文本向量化
        // 这里需要集成具体的向量化模型，如sentence-transformers

        // 示例：返回随机向量（实际应用中需要替换为真实的向量化逻辑）
        let embedding: Vec<f32> = (0..self.dimension)
            .map(|_| rand::random::<f32>())
            .collect();

        Ok(embedding)
    }

    fn dimension(&self) -> usize {
        self.dimension
    }

    fn model_name(&self) -> &str {
        &self.model_name
    }
}

impl LocalEmbeddingFunction {
    pub fn new(model_name: String, dimension: usize) -> Self {
        Self {
            model_name,
            dimension,
        }
    }
}
```

### 5.3 数据库关系图

#### 5.3.1 SQLite数据库关系图

```
SQLite数据库实体关系图 (ERD):

┌─────────────────────────────────────────────────────────────────────────────────────┐
│                              AI Studio 数据库关系图                                  │
├─────────────────────────────────────────────────────────────────────────────────────┤
│                                                                                     │
│  ┌─────────────────┐    1:N    ┌─────────────────┐    1:N    ┌─────────────────┐   │
│  │  chat_sessions  │ ────────→ │  chat_messages  │ ────────→ │   attachments   │   │
│  │                 │           │                 │           │                 │   │
│  │ • id (PK)       │           │ • id (PK)       │           │ • id (PK)       │   │
│  │ • title         │           │ • session_id(FK)│           │ • message_id(FK)│   │
│  │ • model_id      │           │ • role          │           │ • file_path     │   │
│  │ • system_prompt │           │ • content       │           │ • file_type     │   │
│  │ • temperature   │           │ • tokens        │           │ • file_size     │   │
│  │ • max_tokens    │           │ • model_id      │           │ • created_at    │   │
│  │ • created_at    │           │ • parent_id     │           └─────────────────┘   │
│  │ • updated_at    │           │ • status        │                                 │
│  └─────────────────┘           │ • created_at    │                                 │
│           │                    └─────────────────┘                                 │
│           │                                                                        │
│           │ N:1                                                                    │
│           ↓                                                                        │
│  ┌─────────────────┐                                                              │
│  │     models      │                                                              │
│  │                 │                                                              │
│  │ • id (PK)       │                                                              │
│  │ • name          │                                                              │
│  │ • display_name  │                                                              │
│  │ • type          │                                                              │
│  │ • provider      │                                                              │
│  │ • file_path     │                                                              │
│  │ • load_status   │                                                              │
│  │ • created_at    │                                                              │
│  └─────────────────┘                                                              │
│                                                                                    │
│  ┌─────────────────┐    1:N    ┌─────────────────┐    1:N    ┌─────────────────┐  │
│  │knowledge_bases  │ ────────→ │   documents     │ ────────→ │document_chunks  │  │
│  │                 │           │                 │           │                 │  │
│  │ • id (PK)       │           │ • id (PK)       │           │ • id (PK)       │  │
│  │ • name          │           │ • kb_id (FK)    │           │ • document_id(FK)│  │
│  │ • description   │           │ • filename      │           │ • kb_id (FK)    │  │
│  │ • type          │           │ • file_path     │           │ • chunk_index   │  │
│  │ • embedding_model│          │ • file_type     │           │ • content       │  │
│  │ • chunk_size    │           │ • file_size     │           │ • content_hash  │  │
│  │ • vector_dimension│         │ • processing_status│        │ • token_count   │  │
│  │ • document_count│           │ • chunk_count   │           │ • embedding_status│ │
│  │ • status        │           │ • created_at    │           │ • vector_id     │  │
│  │ • created_at    │           └─────────────────┘           │ • created_at    │  │
│  └─────────────────┘                                         └─────────────────┘  │
│                                                                        │           │
│                                                                        │           │
│                                                                        │ 1:1       │
│                                                                        ↓           │
│                                                               ┌─────────────────┐  │
│                                                               │  ChromaDB       │  │
│                                                               │  Collections    │  │
│                                                               │                 │  │
│                                                               │ • vector_id     │  │
│                                                               │ • embedding     │  │
│                                                               │ • metadata      │  │
│                                                               │ • distance      │  │
│                                                               └─────────────────┘  │
│                                                                                    │
│  ┌─────────────────┐                        ┌─────────────────┐                  │
│  │    plugins      │                        │  system_logs    │                  │
│  │                 │                        │                 │                  │
│  │ • id (PK)       │                        │ • id (PK)       │                  │
│  │ • name          │                        │ • level         │                  │
│  │ • display_name  │                        │ • category      │                  │
│  │ • version       │                        │ • module        │                  │
│  │ • file_path     │                        │ • message       │                  │
│  │ • install_status│                        │ • details       │                  │
│  │ • enable_status │                        │ • session_id    │                  │
│  │ • config        │                        │ • created_at    │                  │
│  │ • created_at    │                        └─────────────────┘                  │
│  └─────────────────┘                                                             │
│                                                                                    │
│  ┌─────────────────┐                        ┌─────────────────┐                  │
│  │ user_settings   │                        │performance_metrics│                │
│  │                 │                        │                 │                  │
│  │ • id (PK)       │                        │ • id (PK)       │                  │
│  │ • key           │                        │ • metric_name   │                  │
│  │ • value         │                        │ • metric_value  │                  │
│  │ • category      │                        │ • category      │                  │
│  │ • data_type     │                        │ • recorded_at   │                  │
│  │ • is_encrypted  │                        └─────────────────┘                  │
│  │ • created_at    │                                                             │
│  └─────────────────┘                                                             │
│                                                                                    │
└─────────────────────────────────────────────────────────────────────────────────────┘

关系说明:
• 1:N = 一对多关系
• N:1 = 多对一关系
• 1:1 = 一对一关系
• PK = 主键 (Primary Key)
• FK = 外键 (Foreign Key)
```

#### 5.3.2 数据流向图

```
数据流向与处理流程图:

┌─────────────────────────────────────────────────────────────────────────────────────┐
│                              数据处理流程图                                          │
├─────────────────────────────────────────────────────────────────────────────────────┤
│                                                                                     │
│  用户输入                                                                            │
│     │                                                                              │
│     ↓                                                                              │
│  ┌─────────────────┐    验证    ┌─────────────────┐    存储    ┌─────────────────┐  │
│  │   前端界面      │ ────────→ │   业务逻辑层     │ ────────→ │   SQLite数据库   │  │
│  │                 │           │                 │           │                 │  │
│  │ • 用户交互      │           │ • 参数验证      │           │ • 会话数据      │  │
│  │ • 数据收集      │           │ • 业务处理      │           │ • 消息记录      │  │
│  │ • 状态显示      │           │ • 权限检查      │           │ • 配置信息      │  │
│  └─────────────────┘           │ • 错误处理      │           │ • 日志数据      │  │
│                                └─────────────────┘           └─────────────────┘  │
│                                         │                                          │
│                                         │ 文档处理                                  │
│                                         ↓                                          │
│                                ┌─────────────────┐                                │
│                                │   文档解析器     │                                │
│                                │                 │                                │
│                                │ • 格式识别      │                                │
│                                │ • 内容提取      │                                │
│                                │ • 文本分块      │                                │
│                                │ • 元数据提取    │                                │
│                                └─────────────────┘                                │
│                                         │                                          │
│                                         │ 向量化                                   │
│                                         ↓                                          │
│                                ┌─────────────────┐                                │
│                                │   向量化引擎     │                                │
│                                │                 │                                │
│                                │ • 文本编码      │                                │
│                                │ • 向量生成      │                                │
│                                │ • 批量处理      │                                │
│                                │ • 质量检查      │                                │
│                                └─────────────────┘                                │
│                                         │                                          │
│                                         │ 存储                                     │
│                                         ↓                                          │
│                                ┌─────────────────┐                                │
│                                │   ChromaDB      │                                │
│                                │                 │                                │
│                                │ • 向量存储      │                                │
│                                │ • 索引构建      │                                │
│                                │ • 相似度计算    │                                │
│                                │ • 检索优化      │                                │
│                                └─────────────────┘                                │
│                                                                                    │
│  查询请求                                                                           │
│     │                                                                              │
│     ↓                                                                              │
│  ┌─────────────────┐    检索    ┌─────────────────┐    生成    ┌─────────────────┐  │
│  │   搜索接口      │ ────────→ │   RAG引擎       │ ────────→ │   AI推理引擎     │  │
│  │                 │           │                 │           │                 │  │
│  │ • 查询解析      │           │ • 向量搜索      │           │ • 模型加载      │  │
│  │ • 参数处理      │           │ • 上下文构建    │           │ • 推理执行      │  │
│  │ • 结果格式化    │           │ • 相关性排序    │           │ • 流式输出      │  │
│  └─────────────────┘           │ • 结果过滤      │           │ • 结果后处理    │  │
│                                └─────────────────┘           └─────────────────┘  │
│                                         ↑                            │            │
│                                         │                            │            │
│                                         │ 反馈学习                    │ 结果存储    │
│                                         │                            ↓            │
│                                ┌─────────────────┐           ┌─────────────────┐  │
│                                │   学习优化      │           │   结果缓存      │  │
│                                │                 │           │                 │  │
│                                │ • 用户反馈      │           │ • 响应缓存      │  │
│                                │ • 质量评估      │           │ • 性能统计      │  │
│                                │ • 模型调优      │           │ • 使用分析      │  │
│                                │ • 策略更新      │           │ • 清理策略      │  │
│                                └─────────────────┘           └─────────────────┘  │
│                                                                                    │
└─────────────────────────────────────────────────────────────────────────────────────┘
```

---

## 6. 用户界面设计规范

### 6.1 组件库设计

#### 6.1.1 设计系统概述

AI Studio 的用户界面设计遵循现代化的设计原则，注重用户体验和视觉一致性。设计系统包含颜色系统、字体系统、间距系统、组件库等核心要素。

**设计原则：**
- **一致性**：统一的视觉语言和交互模式
- **可访问性**：支持无障碍访问，符合WCAG 2.1标准
- **响应式**：适配不同屏幕尺寸和分辨率
- **性能优先**：轻量级组件，快速渲染
- **可维护性**：模块化设计，易于扩展和维护

#### 6.1.2 颜色系统设计

**主色调系统**
```scss
// 颜色系统定义
// ===================================================

// 主色调 - 蓝色系
$primary-colors: (
  50:  #f0f9ff,   // 最浅色调 - 背景色
  100: #e0f2fe,   // 浅色调 - 悬浮状态
  200: #bae6fd,   // 较浅色调 - 禁用状态
  300: #7dd3fc,   // 中浅色调 - 边框色
  400: #38bdf8,   // 中色调 - 图标色
  500: #0ea5e9,   // 标准色调 - 主要按钮
  600: #0284c7,   // 中深色调 - 悬浮状态
  700: #0369a1,   // 深色调 - 激活状态
  800: #075985,   // 较深色调 - 文字色
  900: #0c4a6e,   // 最深色调 - 标题色
);

// 辅助色系 - 灰色系
$neutral-colors: (
  50:  #fafafa,   // 背景色
  100: #f5f5f5,   // 卡片背景
  200: #e5e5e5,   // 分割线
  300: #d4d4d4,   // 边框色
  400: #a3a3a3,   // 占位符
  500: #737373,   // 辅助文字
  600: #525252,   // 次要文字
  700: #404040,   // 主要文字
  800: #262626,   // 标题文字
  900: #171717,   // 强调文字
);

// 语义色系
$semantic-colors: (
  // 成功色 - 绿色系
  success: (
    50:  #f0fdf4,
    100: #dcfce7,
    200: #bbf7d0,
    300: #86efac,
    400: #4ade80,
    500: #22c55e,   // 主色调
    600: #16a34a,
    700: #15803d,
    800: #166534,
    900: #14532d,
  ),

  // 警告色 - 橙色系
  warning: (
    50:  #fffbeb,
    100: #fef3c7,
    200: #fde68a,
    300: #fcd34d,
    400: #fbbf24,
    500: #f59e0b,   // 主色调
    600: #d97706,
    700: #b45309,
    800: #92400e,
    900: #78350f,
  ),

  // 错误色 - 红色系
  error: (
    50:  #fef2f2,
    100: #fee2e2,
    200: #fecaca,
    300: #fca5a5,
    400: #f87171,
    500: #ef4444,   // 主色调
    600: #dc2626,
    700: #b91c1c,
    800: #991b1b,
    900: #7f1d1d,
  ),

  // 信息色 - 蓝色系
  info: (
    50:  #eff6ff,
    100: #dbeafe,
    200: #bfdbfe,
    300: #93c5fd,
    400: #60a5fa,
    500: #3b82f6,   // 主色调
    600: #2563eb,
    700: #1d4ed8,
    800: #1e40af,
    900: #1e3a8a,
  ),
);

// 颜色使用规范
$color-usage: (
  // 文字颜色
  text-primary: map-get($neutral-colors, 900),
  text-secondary: map-get($neutral-colors, 600),
  text-tertiary: map-get($neutral-colors, 400),
  text-inverse: #ffffff,
  text-link: map-get($primary-colors, 600),
  text-success: map-get(map-get($semantic-colors, success), 600),
  text-warning: map-get(map-get($semantic-colors, warning), 600),
  text-error: map-get(map-get($semantic-colors, error), 600),

  // 背景颜色
  bg-primary: #ffffff,
  bg-secondary: map-get($neutral-colors, 50),
  bg-tertiary: map-get($neutral-colors, 100),
  bg-overlay: rgba(0, 0, 0, 0.5),
  bg-success: map-get(map-get($semantic-colors, success), 50),
  bg-warning: map-get(map-get($semantic-colors, warning), 50),
  bg-error: map-get(map-get($semantic-colors, error), 50),
  bg-info: map-get(map-get($semantic-colors, info), 50),

  // 边框颜色
  border-primary: map-get($neutral-colors, 200),
  border-secondary: map-get($neutral-colors, 300),
  border-focus: map-get($primary-colors, 500),
  border-success: map-get(map-get($semantic-colors, success), 300),
  border-warning: map-get(map-get($semantic-colors, warning), 300),
  border-error: map-get(map-get($semantic-colors, error), 300),
);
```

#### 6.1.3 字体系统设计

**字体层次结构**
```scss
// 字体系统定义
// ===================================================

// 字体族
$font-families: (
  sans: ('Inter', 'PingFang SC', 'Microsoft YaHei', 'Helvetica Neue', 'Arial', sans-serif),
  mono: ('JetBrains Mono', 'Fira Code', 'Consolas', 'Monaco', 'Courier New', monospace),
  serif: ('Georgia', 'Times New Roman', 'serif'),
);

// 字体大小系统
$font-sizes: (
  xs:   0.75rem,    // 12px - 辅助信息
  sm:   0.875rem,   // 14px - 次要文字
  base: 1rem,       // 16px - 正文
  lg:   1.125rem,   // 18px - 小标题
  xl:   1.25rem,    // 20px - 中标题
  2xl:  1.5rem,     // 24px - 大标题
  3xl:  1.875rem,   // 30px - 页面标题
  4xl:  2.25rem,    // 36px - 主标题
  5xl:  3rem,       // 48px - 超大标题
  6xl:  3.75rem,    // 60px - 展示标题
);

// 字体粗细
$font-weights: (
  thin:       100,
  extralight: 200,
  light:      300,
  normal:     400,
  medium:     500,
  semibold:   600,
  bold:       700,
  extrabold:  800,
  black:      900,
);

// 行高系统
$line-heights: (
  none:     1,
  tight:    1.25,
  snug:     1.375,
  normal:   1.5,
  relaxed:  1.625,
  loose:    2,
);

// 字间距
$letter-spacings: (
  tighter: -0.05em,
  tight:   -0.025em,
  normal:  0,
  wide:    0.025em,
  wider:   0.05em,
  widest:  0.1em,
);

// 字体使用规范
$typography-scale: (
  // 标题系列
  h1: (
    font-size: map-get($font-sizes, 4xl),
    font-weight: map-get($font-weights, bold),
    line-height: map-get($line-heights, tight),
    letter-spacing: map-get($letter-spacings, tight),
    margin-bottom: 1.5rem,
  ),
  h2: (
    font-size: map-get($font-sizes, 3xl),
    font-weight: map-get($font-weights, semibold),
    line-height: map-get($line-heights, tight),
    letter-spacing: map-get($letter-spacings, tight),
    margin-bottom: 1.25rem,
  ),
  h3: (
    font-size: map-get($font-sizes, 2xl),
    font-weight: map-get($font-weights, semibold),
    line-height: map-get($line-heights, snug),
    letter-spacing: map-get($letter-spacings, normal),
    margin-bottom: 1rem,
  ),
  h4: (
    font-size: map-get($font-sizes, xl),
    font-weight: map-get($font-weights, medium),
    line-height: map-get($line-heights, snug),
    letter-spacing: map-get($letter-spacings, normal),
    margin-bottom: 0.75rem,
  ),
  h5: (
    font-size: map-get($font-sizes, lg),
    font-weight: map-get($font-weights, medium),
    line-height: map-get($line-heights, normal),
    letter-spacing: map-get($letter-spacings, normal),
    margin-bottom: 0.5rem,
  ),
  h6: (
    font-size: map-get($font-sizes, base),
    font-weight: map-get($font-weights, medium),
    line-height: map-get($line-heights, normal),
    letter-spacing: map-get($letter-spacings, normal),
    margin-bottom: 0.5rem,
  ),

  // 正文系列
  body-large: (
    font-size: map-get($font-sizes, lg),
    font-weight: map-get($font-weights, normal),
    line-height: map-get($line-heights, relaxed),
    letter-spacing: map-get($letter-spacings, normal),
  ),
  body: (
    font-size: map-get($font-sizes, base),
    font-weight: map-get($font-weights, normal),
    line-height: map-get($line-heights, normal),
    letter-spacing: map-get($letter-spacings, normal),
  ),
  body-small: (
    font-size: map-get($font-sizes, sm),
    font-weight: map-get($font-weights, normal),
    line-height: map-get($line-heights, normal),
    letter-spacing: map-get($letter-spacings, normal),
  ),

  // 特殊用途
  caption: (
    font-size: map-get($font-sizes, xs),
    font-weight: map-get($font-weights, normal),
    line-height: map-get($line-heights, normal),
    letter-spacing: map-get($letter-spacings, wide),
    text-transform: uppercase,
  ),
  code: (
    font-family: map-get($font-families, mono),
    font-size: 0.875em,
    font-weight: map-get($font-weights, normal),
    line-height: map-get($line-heights, normal),
    letter-spacing: map-get($letter-spacings, normal),
  ),
  button: (
    font-size: map-get($font-sizes, sm),
    font-weight: map-get($font-weights, medium),
    line-height: map-get($line-heights, none),
    letter-spacing: map-get($letter-spacings, wide),
  ),
);
```

### 6.2 样式指南与主题系统

#### 6.2.1 主题系统架构

AI Studio 支持多主题切换，包括浅色主题、深色主题和高对比度主题，满足不同用户的视觉需求和无障碍要求。

**主题切换机制**
```typescript
// stores/theme.ts - 主题管理状态
import { defineStore } from 'pinia'
import { ref, computed, watch } from 'vue'

export interface ThemeConfig {
  id: string
  name: string
  displayName: string
  colors: {
    primary: string
    secondary: string
    background: string
    surface: string
    text: string
    border: string
    shadow: string
  }
  fonts: {
    sans: string[]
    mono: string[]
    serif: string[]
  }
  spacing: {
    xs: string
    sm: string
    md: string
    lg: string
    xl: string
  }
  borderRadius: {
    sm: string
    md: string
    lg: string
    full: string
  }
  shadows: {
    sm: string
    md: string
    lg: string
    xl: string
  }
}

export const useThemeStore = defineStore('theme', () => {
  // 当前主题
  const currentTheme = ref<string>('light')

  // 系统主题检测
  const systemTheme = ref<'light' | 'dark'>('light')

  // 自动跟随系统主题
  const followSystem = ref<boolean>(false)

  // 主题配置
  const themes = ref<Record<string, ThemeConfig>>({
    light: {
      id: 'light',
      name: 'light',
      displayName: '浅色主题',
      colors: {
        primary: '#0ea5e9',
        secondary: '#64748b',
        background: '#ffffff',
        surface: '#f8fafc',
        text: '#1e293b',
        border: '#e2e8f0',
        shadow: 'rgba(0, 0, 0, 0.1)',
      },
      fonts: {
        sans: ['Inter', 'PingFang SC', 'Microsoft YaHei', 'sans-serif'],
        mono: ['JetBrains Mono', 'Consolas', 'Monaco', 'monospace'],
        serif: ['Georgia', 'Times New Roman', 'serif'],
      },
      spacing: {
        xs: '0.25rem',
        sm: '0.5rem',
        md: '1rem',
        lg: '1.5rem',
        xl: '2rem',
      },
      borderRadius: {
        sm: '0.25rem',
        md: '0.375rem',
        lg: '0.5rem',
        full: '9999px',
      },
      shadows: {
        sm: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
        md: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
        lg: '0 10px 15px -3px rgba(0, 0, 0, 0.1)',
        xl: '0 20px 25px -5px rgba(0, 0, 0, 0.1)',
      },
    },

    dark: {
      id: 'dark',
      name: 'dark',
      displayName: '深色主题',
      colors: {
        primary: '#60a5fa',
        secondary: '#cbd5e1',
        background: '#0f172a',
        surface: '#1e293b',
        text: '#f8fafc',
        border: '#334155',
        shadow: 'rgba(0, 0, 0, 0.3)',
      },
      fonts: {
        sans: ['Inter', 'PingFang SC', 'Microsoft YaHei', 'sans-serif'],
        mono: ['JetBrains Mono', 'Consolas', 'Monaco', 'monospace'],
        serif: ['Georgia', 'Times New Roman', 'serif'],
      },
      spacing: {
        xs: '0.25rem',
        sm: '0.5rem',
        md: '1rem',
        lg: '1.5rem',
        xl: '2rem',
      },
      borderRadius: {
        sm: '0.25rem',
        md: '0.375rem',
        lg: '0.5rem',
        full: '9999px',
      },
      shadows: {
        sm: '0 1px 2px 0 rgba(0, 0, 0, 0.3)',
        md: '0 4px 6px -1px rgba(0, 0, 0, 0.3)',
        lg: '0 10px 15px -3px rgba(0, 0, 0, 0.3)',
        xl: '0 20px 25px -5px rgba(0, 0, 0, 0.3)',
      },
    },

    'high-contrast': {
      id: 'high-contrast',
      name: 'high-contrast',
      displayName: '高对比度主题',
      colors: {
        primary: '#0000ff',
        secondary: '#333333',
        background: '#ffffff',
        surface: '#f0f0f0',
        text: '#000000',
        border: '#000000',
        shadow: 'rgba(0, 0, 0, 0.5)',
      },
      fonts: {
        sans: ['Inter', 'PingFang SC', 'Microsoft YaHei', 'sans-serif'],
        mono: ['JetBrains Mono', 'Consolas', 'Monaco', 'monospace'],
        serif: ['Georgia', 'Times New Roman', 'serif'],
      },
      spacing: {
        xs: '0.25rem',
        sm: '0.5rem',
        md: '1rem',
        lg: '1.5rem',
        xl: '2rem',
      },
      borderRadius: {
        sm: '0.25rem',
        md: '0.375rem',
        lg: '0.5rem',
        full: '9999px',
      },
      shadows: {
        sm: '0 1px 2px 0 rgba(0, 0, 0, 0.5)',
        md: '0 4px 6px -1px rgba(0, 0, 0, 0.5)',
        lg: '0 10px 15px -3px rgba(0, 0, 0, 0.5)',
        xl: '0 20px 25px -5px rgba(0, 0, 0, 0.5)',
      },
    },
  })

  // 计算属性
  const activeTheme = computed(() => {
    const themeId = followSystem.value ? systemTheme.value : currentTheme.value
    return themes.value[themeId] || themes.value.light
  })

  const isDarkMode = computed(() => {
    return activeTheme.value.name === 'dark'
  })

  const isHighContrast = computed(() => {
    return activeTheme.value.name === 'high-contrast'
  })

  // 方法
  const setTheme = (themeId: string) => {
    if (themes.value[themeId]) {
      currentTheme.value = themeId
      followSystem.value = false
      applyTheme(themes.value[themeId])
      saveThemePreference()
    }
  }

  const toggleTheme = () => {
    const nextTheme = currentTheme.value === 'light' ? 'dark' : 'light'
    setTheme(nextTheme)
  }

  const setFollowSystem = (follow: boolean) => {
    followSystem.value = follow
    if (follow) {
      applyTheme(themes.value[systemTheme.value])
    }
    saveThemePreference()
  }

  const detectSystemTheme = () => {
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
    systemTheme.value = mediaQuery.matches ? 'dark' : 'light'

    mediaQuery.addEventListener('change', (e) => {
      systemTheme.value = e.matches ? 'dark' : 'light'
      if (followSystem.value) {
        applyTheme(themes.value[systemTheme.value])
      }
    })
  }

  const applyTheme = (theme: ThemeConfig) => {
    const root = document.documentElement

    // 应用CSS自定义属性
    Object.entries(theme.colors).forEach(([key, value]) => {
      root.style.setProperty(`--color-${key}`, value)
    })

    Object.entries(theme.spacing).forEach(([key, value]) => {
      root.style.setProperty(`--spacing-${key}`, value)
    })

    Object.entries(theme.borderRadius).forEach(([key, value]) => {
      root.style.setProperty(`--radius-${key}`, value)
    })

    Object.entries(theme.shadows).forEach(([key, value]) => {
      root.style.setProperty(`--shadow-${key}`, value)
    })

    // 设置主题类名
    root.className = root.className.replace(/theme-\w+/g, '')
    root.classList.add(`theme-${theme.name}`)

    // 设置字体
    root.style.setProperty('--font-sans', theme.fonts.sans.join(', '))
    root.style.setProperty('--font-mono', theme.fonts.mono.join(', '))
    root.style.setProperty('--font-serif', theme.fonts.serif.join(', '))
  }

  const saveThemePreference = () => {
    const preferences = {
      currentTheme: currentTheme.value,
      followSystem: followSystem.value,
    }
    localStorage.setItem('theme-preferences', JSON.stringify(preferences))
  }

  const loadThemePreference = () => {
    try {
      const saved = localStorage.getItem('theme-preferences')
      if (saved) {
        const preferences = JSON.parse(saved)
        currentTheme.value = preferences.currentTheme || 'light'
        followSystem.value = preferences.followSystem || false
      }
    } catch (error) {
      console.warn('Failed to load theme preferences:', error)
    }
  }

  const initializeTheme = () => {
    detectSystemTheme()
    loadThemePreference()
    applyTheme(activeTheme.value)
  }

  // 监听主题变化
  watch(activeTheme, (newTheme) => {
    applyTheme(newTheme)
  })

  return {
    // 状态
    currentTheme,
    systemTheme,
    followSystem,
    themes,

    // 计算属性
    activeTheme,
    isDarkMode,
    isHighContrast,

    // 方法
    setTheme,
    toggleTheme,
    setFollowSystem,
    detectSystemTheme,
    applyTheme,
    saveThemePreference,
    loadThemePreference,
    initializeTheme,
  }
})
```

#### 6.2.2 主题切换组件

**ThemeSelector.vue - 主题选择器组件**
```vue
<template>
  <div class="theme-selector">
    <Dropdown
      v-model:visible="dropdownVisible"
      :options="themeOptions"
      @select="handleThemeSelect"
    >
      <template #trigger>
        <Button
          variant="ghost"
          size="sm"
          :icon="currentThemeIcon"
          class="theme-toggle-btn"
          @click="dropdownVisible = !dropdownVisible"
        >
          {{ currentThemeLabel }}
        </Button>
      </template>

      <template #option="{ option }">
        <div class="theme-option">
          <Icon :name="option.icon" class="theme-option-icon" />
          <div class="theme-option-content">
            <div class="theme-option-name">{{ option.label }}</div>
            <div class="theme-option-desc">{{ option.description }}</div>
          </div>
          <Icon
            v-if="option.value === currentTheme"
            name="check"
            class="theme-option-check"
          />
        </div>
      </template>
    </Dropdown>

    <!-- 系统主题跟随选项 -->
    <div class="theme-system-follow">
      <label class="flex items-center space-x-2 cursor-pointer">
        <input
          v-model="followSystem"
          type="checkbox"
          class="form-checkbox"
          @change="handleFollowSystemChange"
        />
        <span class="text-sm text-secondary">跟随系统主题</span>
      </label>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useThemeStore } from '@/stores/theme'
import Button from '@/components/common/Button.vue'
import Dropdown from '@/components/common/Dropdown.vue'
import Icon from '@/components/common/Icon.vue'

const themeStore = useThemeStore()

const dropdownVisible = ref(false)

const themeOptions = computed(() => [
  {
    value: 'light',
    label: '浅色主题',
    description: '适合白天使用的明亮主题',
    icon: 'sun',
  },
  {
    value: 'dark',
    label: '深色主题',
    description: '适合夜间使用的暗色主题',
    icon: 'moon',
  },
  {
    value: 'high-contrast',
    label: '高对比度',
    description: '提高可读性的高对比度主题',
    icon: 'contrast',
  },
])

const currentTheme = computed(() => themeStore.currentTheme)
const followSystem = computed({
  get: () => themeStore.followSystem,
  set: (value) => themeStore.setFollowSystem(value)
})

const currentThemeLabel = computed(() => {
  const option = themeOptions.value.find(opt => opt.value === currentTheme.value)
  return option?.label || '浅色主题'
})

const currentThemeIcon = computed(() => {
  const option = themeOptions.value.find(opt => opt.value === currentTheme.value)
  return option?.icon || 'sun'
})

const handleThemeSelect = (option: any) => {
  themeStore.setTheme(option.value)
  dropdownVisible.value = false
}

const handleFollowSystemChange = () => {
  themeStore.setFollowSystem(followSystem.value)
}
</script>

<style lang="scss" scoped>
.theme-selector {
  @apply space-y-3;
}

.theme-toggle-btn {
  @apply min-w-[120px] justify-start;
}

.theme-option {
  @apply flex items-center space-x-3 p-3 hover:bg-gray-50 transition-colors;
}

.theme-option-icon {
  @apply w-5 h-5 text-gray-500;
}

.theme-option-content {
  @apply flex-1;
}

.theme-option-name {
  @apply font-medium text-gray-900;
}

.theme-option-desc {
  @apply text-sm text-gray-500;
}

.theme-option-check {
  @apply w-4 h-4 text-primary-600;
}

.theme-system-follow {
  @apply pt-2 border-t border-gray-200;
}

.form-checkbox {
  @apply w-4 h-4 text-primary-600 border-gray-300 rounded focus:ring-primary-500;
}

// 深色主题适配
.theme-dark {
  .theme-option {
    @apply hover:bg-gray-800;
  }

  .theme-option-name {
    @apply text-gray-100;
  }

  .theme-option-desc {
    @apply text-gray-400;
  }

  .theme-system-follow {
    @apply border-gray-700;
  }

  .form-checkbox {
    @apply border-gray-600 bg-gray-700;
  }
}
</style>
```

### 6.3 国际化设计方案

#### 6.3.1 国际化架构设计

AI Studio 支持中文和英文双语切换，采用 Vue I18n 作为国际化解决方案，支持动态语言切换、复数形式、日期时间格式化等功能。

**国际化配置结构**
```typescript
// i18n/index.ts - 国际化配置
import { createI18n } from 'vue-i18n'
import { ref, computed } from 'vue'

// 语言配置接口
export interface LanguageConfig {
  code: string
  name: string
  nativeName: string
  flag: string
  rtl: boolean
  dateFormat: string
  timeFormat: string
  numberFormat: {
    decimal: string
    thousands: string
    currency: string
  }
}

// 支持的语言列表
export const supportedLanguages: Record<string, LanguageConfig> = {
  'zh-CN': {
    code: 'zh-CN',
    name: 'Chinese (Simplified)',
    nativeName: '简体中文',
    flag: '🇨🇳',
    rtl: false,
    dateFormat: 'YYYY年MM月DD日',
    timeFormat: 'HH:mm:ss',
    numberFormat: {
      decimal: '.',
      thousands: ',',
      currency: '¥',
    },
  },
  'en-US': {
    code: 'en-US',
    name: 'English (United States)',
    nativeName: 'English',
    flag: '🇺🇸',
    rtl: false,
    dateFormat: 'MM/DD/YYYY',
    timeFormat: 'h:mm:ss A',
    numberFormat: {
      decimal: '.',
      thousands: ',',
      currency: '$',
    },
  },
}

// 动态导入语言包
const loadLanguageMessages = async (locale: string) => {
  try {
    const messages = await import(`./locales/${locale}.json`)
    return messages.default
  } catch (error) {
    console.warn(`Failed to load language pack: ${locale}`, error)
    return {}
  }
}

// 创建 i18n 实例
export const createAppI18n = async (defaultLocale: string = 'zh-CN') => {
  // 加载默认语言包
  const defaultMessages = await loadLanguageMessages(defaultLocale)

  const i18n = createI18n({
    legacy: false,
    locale: defaultLocale,
    fallbackLocale: 'zh-CN',
    messages: {
      [defaultLocale]: defaultMessages,
    },
    numberFormats: {
      'zh-CN': {
        currency: {
          style: 'currency',
          currency: 'CNY',
          notation: 'standard',
        },
        decimal: {
          style: 'decimal',
          minimumFractionDigits: 2,
          maximumFractionDigits: 2,
        },
        percent: {
          style: 'percent',
          useGrouping: false,
        },
      },
      'en-US': {
        currency: {
          style: 'currency',
          currency: 'USD',
          notation: 'standard',
        },
        decimal: {
          style: 'decimal',
          minimumFractionDigits: 2,
          maximumFractionDigits: 2,
        },
        percent: {
          style: 'percent',
          useGrouping: false,
        },
      },
    },
    datetimeFormats: {
      'zh-CN': {
        short: {
          year: 'numeric',
          month: 'short',
          day: 'numeric',
        },
        long: {
          year: 'numeric',
          month: 'short',
          day: 'numeric',
          weekday: 'short',
          hour: 'numeric',
          minute: 'numeric',
        },
      },
      'en-US': {
        short: {
          year: 'numeric',
          month: 'short',
          day: 'numeric',
        },
        long: {
          year: 'numeric',
          month: 'short',
          day: 'numeric',
          weekday: 'short',
          hour: 'numeric',
          minute: 'numeric',
        },
      },
    },
  })

  return i18n
}

// 语言切换工具函数
export const switchLanguage = async (i18n: any, locale: string) => {
  if (!supportedLanguages[locale]) {
    console.warn(`Unsupported locale: ${locale}`)
    return false
  }

  // 如果语言包未加载，则动态加载
  if (!i18n.global.availableLocales.includes(locale)) {
    const messages = await loadLanguageMessages(locale)
    i18n.global.setLocaleMessage(locale, messages)
  }

  // 切换语言
  i18n.global.locale.value = locale

  // 更新HTML lang属性
  document.documentElement.lang = locale

  // 更新文档方向
  const config = supportedLanguages[locale]
  document.documentElement.dir = config.rtl ? 'rtl' : 'ltr'

  // 保存用户偏好
  localStorage.setItem('user-locale', locale)

  return true
}

// 获取浏览器语言
export const getBrowserLanguage = (): string => {
  const browserLang = navigator.language || navigator.languages[0]

  // 检查是否支持完整的语言代码
  if (supportedLanguages[browserLang]) {
    return browserLang
  }

  // 检查是否支持语言的主要部分
  const mainLang = browserLang.split('-')[0]
  const supportedLang = Object.keys(supportedLanguages).find(lang =>
    lang.startsWith(mainLang)
  )

  return supportedLang || 'zh-CN'
}

// 获取保存的语言偏好
export const getSavedLanguage = (): string => {
  const saved = localStorage.getItem('user-locale')
  return saved && supportedLanguages[saved] ? saved : getBrowserLanguage()
}
```
---

## 7. 系统流程与业务逻辑

### 7.1 用户操作流程

#### 7.1.1 应用启动流程

```
应用启动流程图:

用户启动应用
     │
     ↓
┌─────────────────┐
│  系统初始化     │ ← 检查系统环境、权限、依赖
└─────────────────┘
     │
     ↓
┌─────────────────┐
│  配置加载       │ ← 加载用户设置、主题、语言
└─────────────────┘
     │
     ↓
┌─────────────────┐
│  数据库连接     │ ← 连接SQLite、ChromaDB
└─────────────────┘
     │
     ↓
┌─────────────────┐
│  服务启动       │ ← 启动后端服务、AI引擎
└─────────────────┘
     │
     ↓
┌─────────────────┐
│  界面渲染       │ ← 渲染主界面、加载组件
└─────────────────┘
     │
     ↓
┌─────────────────┐
│  就绪状态       │ ← 应用可用，等待用户操作
└─────────────────┘
```

**详细启动步骤：**

1. **系统环境检查**
   - 检查操作系统版本兼容性
   - 验证必要的系统权限
   - 检查硬件资源（内存、存储空间）
   - 验证网络连接状态

2. **应用初始化**
   - 创建应用数据目录
   - 初始化日志系统
   - 加载应用配置文件
   - 设置错误处理机制

3. **数据库初始化**
   - 连接SQLite数据库
   - 执行数据库迁移
   - 初始化ChromaDB连接
   - 验证数据完整性

4. **服务启动**
   - 启动Tauri后端服务
   - 初始化AI推理引擎
   - 启动网络服务
   - 加载插件系统

5. **用户界面加载**
   - 应用主题和语言设置
   - 渲染主界面组件
   - 恢复用户会话状态
   - 显示欢迎界面或上次使用状态

#### 7.1.2 聊天对话流程

```
聊天对话完整流程:

用户输入消息
     │
     ↓
┌─────────────────┐
│  输入验证       │ ← 检查消息长度、格式、权限
└─────────────────┘
     │
     ↓
┌─────────────────┐
│  消息预处理     │ ← 文本清理、格式化、安全检查
└─────────────────┘
     │
     ↓
┌─────────────────┐
│  上下文构建     │ ← 获取历史消息、系统提示词
└─────────────────┘
     │
     ↓
┌─────────────────┐
│  知识库检索     │ ← RAG检索相关文档（可选）
└─────────────────┘
     │
     ↓
┌─────────────────┐
│  模型推理       │ ← AI模型生成响应
└─────────────────┘
     │
     ↓
┌─────────────────┐
│  响应后处理     │ ← 格式化、安全过滤、质量检查
└─────────────────┘
     │
     ↓
┌─────────────────┐
│  消息存储       │ ← 保存到数据库、更新统计
└─────────────────┘
     │
     ↓
┌─────────────────┐
│  界面更新       │ ← 显示响应、更新UI状态
└─────────────────┘
```

#### 7.1.3 知识库管理流程

```
知识库文档处理流程:

用户上传文档
     │
     ↓
┌─────────────────┐
│  文件验证       │ ← 格式检查、大小限制、安全扫描
└─────────────────┘
     │
     ↓
┌─────────────────┐
│  文档解析       │ ← 提取文本、元数据、结构信息
└─────────────────┘
     │
     ↓
┌─────────────────┐
│  内容分块       │ ← 智能分段、重叠处理、索引构建
└─────────────────┘
     │
     ↓
┌─────────────────┐
│  向量化处理     │ ← 生成文本向量、批量处理
└─────────────────┘
     │
     ↓
┌─────────────────┐
│  向量存储       │ ← 保存到ChromaDB、建立索引
└─────────────────┘
     │
     ↓
┌─────────────────┐
│  元数据存储     │ ← 保存到SQLite、关联关系
└─────────────────┘
     │
     ↓
┌─────────────────┐
│  处理完成       │ ← 更新状态、通知用户
└─────────────────┘
```

### 7.2 数据处理逻辑

#### 7.2.1 消息处理管道

**消息处理架构**
```typescript
// 消息处理管道设计
interface MessagePipeline {
  // 输入阶段
  input: {
    validation: MessageValidator
    sanitization: MessageSanitizer
    preprocessing: MessagePreprocessor
  }
  
  // 处理阶段
  processing: {
    contextBuilder: ContextBuilder
    knowledgeRetriever: KnowledgeRetriever
    modelInference: ModelInference
  }
  
  // 输出阶段
  output: {
    postprocessing: ResponsePostprocessor
    formatting: ResponseFormatter
    validation: ResponseValidator
  }
  
  // 存储阶段
  storage: {
    messageStorage: MessageStorage
    statisticsUpdater: StatisticsUpdater
    cacheManager: CacheManager
  }
}

// 消息验证器
class MessageValidator {
  validate(message: string, context: ChatContext): ValidationResult {
    const checks = [
      this.checkLength(message),
      this.checkFormat(message),
      this.checkContent(message),
      this.checkPermissions(context),
      this.checkRateLimit(context)
    ]
    
    return {
      isValid: checks.every(check => check.passed),
      errors: checks.filter(check => !check.passed).map(check => check.error),
      warnings: checks.filter(check => check.warning).map(check => check.warning)
    }
  }
  
  private checkLength(message: string): CheckResult {
    const maxLength = 10000 // 最大字符数
    return {
      passed: message.length <= maxLength,
      error: message.length > maxLength ? `消息长度超过限制 (${message.length}/${maxLength})` : null
    }
  }
  
  private checkFormat(message: string): CheckResult {
    // 检查消息格式，过滤恶意内容
    const hasValidFormat = /^[\s\S]*$/.test(message) // 基础格式检查
    return {
      passed: hasValidFormat,
      error: !hasValidFormat ? '消息格式无效' : null
    }
  }
  
  private checkContent(message: string): CheckResult {
    // 内容安全检查
    const sensitivePatterns = [
      /password\s*[:=]\s*\w+/i,
      /api[_-]?key\s*[:=]\s*\w+/i,
      /token\s*[:=]\s*\w+/i
    ]
    
    const hasSensitiveContent = sensitivePatterns.some(pattern => pattern.test(message))
    return {
      passed: !hasSensitiveContent,
      warning: hasSensitiveContent ? '消息可能包含敏感信息' : null
    }
  }
}

// 上下文构建器
class ContextBuilder {
  async buildContext(
    currentMessage: string,
    sessionId: string,
    settings: SessionSettings
  ): Promise<ChatContext> {
    // 获取历史消息
    const historyMessages = await this.getHistoryMessages(sessionId, settings.contextLength)
    
    // 构建系统提示词
    const systemPrompt = this.buildSystemPrompt(settings)
    
    // 构建完整上下文
    const context: ChatContext = {
      systemPrompt,
      messages: [
        ...historyMessages,
        {
          role: 'user',
          content: currentMessage,
          timestamp: Date.now()
        }
      ],
      settings,
      metadata: {
        sessionId,
        totalTokens: this.calculateTokens(historyMessages) + this.calculateTokens([currentMessage]),
        messageCount: historyMessages.length + 1
      }
    }
    
    return context
  }
  
  private async getHistoryMessages(sessionId: string, contextLength: number): Promise<Message[]> {
    // 从数据库获取历史消息，按时间倒序
    const messages = await MessageStorage.getRecentMessages(sessionId, contextLength)
    return messages.reverse() // 转为正序
  }
  
  private buildSystemPrompt(settings: SessionSettings): string {
    let prompt = settings.systemPrompt || '你是一个有用的AI助手。'
    
    // 添加当前时间信息
    const now = new Date()
    prompt += `\n\n当前时间: ${now.toLocaleString('zh-CN')}`
    
    // 添加用户偏好设置
    if (settings.responseStyle) {
      prompt += `\n回复风格: ${settings.responseStyle}`
    }
    
    return prompt
  }
}
```

#### 7.2.2 知识库检索逻辑

**RAG检索流程**
```typescript
// RAG检索引擎
class RAGRetriever {
  async retrieve(
    query: string,
    knowledgeBaseIds: string[],
    options: RetrievalOptions
  ): Promise<RetrievalResult> {
    // 1. 查询预处理
    const processedQuery = await this.preprocessQuery(query)
    
    // 2. 向量搜索
    const vectorResults = await this.vectorSearch(processedQuery, knowledgeBaseIds, options)
    
    // 3. 关键词搜索（补充）
    const keywordResults = await this.keywordSearch(processedQuery, knowledgeBaseIds, options)
    
    // 4. 结果融合
    const fusedResults = this.fuseResults(vectorResults, keywordResults, options)
    
    // 5. 重排序
    const rerankedResults = await this.rerank(fusedResults, processedQuery, options)
    
    // 6. 结果过滤
    const filteredResults = this.filterResults(rerankedResults, options)
    
    return {
      query: processedQuery,
      results: filteredResults,
      metadata: {
        totalFound: vectorResults.length + keywordResults.length,
        vectorMatches: vectorResults.length,
        keywordMatches: keywordResults.length,
        processingTime: Date.now() - startTime
      }
    }
  }
  
  private async preprocessQuery(query: string): Promise<ProcessedQuery> {
    return {
      original: query,
      cleaned: this.cleanQuery(query),
      keywords: this.extractKeywords(query),
      intent: await this.detectIntent(query),
      language: this.detectLanguage(query)
    }
  }
  
  private async vectorSearch(
    query: ProcessedQuery,
    knowledgeBaseIds: string[],
    options: RetrievalOptions
  ): Promise<SearchResult[]> {
    const results: SearchResult[] = []
    
    for (const kbId of knowledgeBaseIds) {
      const kbResults = await ChromaManager.search(
        `kb_${kbId}`,
        query.cleaned,
        {
          limit: options.maxResults,
          threshold: options.similarityThreshold,
          includeMetadata: true,
          includeContent: true
        }
      )
      
      results.push(...kbResults.map(result => ({
        ...result,
        source: 'vector',
        knowledgeBaseId: kbId
      })))
    }
    
    return results.sort((a, b) => b.score - a.score)
  }
  
  private fuseResults(
    vectorResults: SearchResult[],
    keywordResults: SearchResult[],
    options: RetrievalOptions
  ): SearchResult[] {
    // 使用RRF (Reciprocal Rank Fusion) 算法融合结果
    const k = 60 // RRF参数
    const scoreMap = new Map<string, number>()
    
    // 计算向量搜索结果的RRF分数
    vectorResults.forEach((result, index) => {
      const rrfScore = 1 / (k + index + 1)
      scoreMap.set(result.id, (scoreMap.get(result.id) || 0) + rrfScore * 0.7) // 向量搜索权重70%
    })
    
    // 计算关键词搜索结果的RRF分数
    keywordResults.forEach((result, index) => {
      const rrfScore = 1 / (k + index + 1)
      scoreMap.set(result.id, (scoreMap.get(result.id) || 0) + rrfScore * 0.3) // 关键词搜索权重30%
    })
    
    // 合并结果并按融合分数排序
    const allResults = [...vectorResults, ...keywordResults]
    const uniqueResults = Array.from(
      new Map(allResults.map(result => [result.id, result])).values()
    )
    
    return uniqueResults
      .map(result => ({
        ...result,
        fusedScore: scoreMap.get(result.id) || 0
      }))
      .sort((a, b) => b.fusedScore - a.fusedScore)
  }
}
```

### 7.3 AI推理流程

#### 7.3.1 模型推理管道

```
AI推理完整流程:

接收推理请求
     │
     ↓
┌─────────────────┐
│  请求验证       │ ← 参数检查、权限验证、资源检查
└─────────────────┘
     │
     ↓
┌─────────────────┐
│  模型选择       │ ← 根据任务类型选择最适合的模型
└─────────────────┘
     │
     ↓
┌─────────────────┐
│  模型加载       │ ← 检查模型状态、按需加载
└─────────────────┘
     │
     ↓
┌─────────────────┐
│  输入预处理     │ ← 分词、编码、格式转换
└─────────────────┘
     │
     ↓
┌─────────────────┐
│  推理执行       │ ← 模型前向传播、生成输出
└─────────────────┘
     │
     ↓
┌─────────────────┐
│  输出后处理     │ ← 解码、格式化、质量检查
└─────────────────┘
     │
     ↓
┌─────────────────┐
│  结果返回       │ ← 封装响应、更新统计、缓存结果
└─────────────────┘
```

**推理引擎实现**
```rust
// AI推理流程实现
impl InferenceEngine {
    pub async fn process_inference_request(
        &self,
        request: InferenceRequest,
    ) -> Result<InferenceResponse> {
        let start_time = Instant::now();
        
        // 1. 请求验证
        self.validate_request(&request).await?;
        
        // 2. 模型选择和加载
        let model = self.select_and_load_model(&request).await?;
        
        // 3. 输入预处理
        let processed_input = self.preprocess_input(&request, &model).await?;
        
        // 4. 推理执行
        let raw_output = match request.stream {
            true => self.stream_inference(&model, processed_input).await?,
            false => self.batch_inference(&model, processed_input).await?,
        };
        
        // 5. 输出后处理
        let processed_output = self.postprocess_output(raw_output, &request).await?;
        
        // 6. 性能统计
        let duration = start_time.elapsed();
        self.record_inference_metrics(&request, &processed_output, duration).await?;
        
        Ok(processed_output)
    }
    
    async fn validate_request(&self, request: &InferenceRequest) -> Result<()> {
        // 检查请求参数
        if request.messages.is_empty() {
            return Err(anyhow!("Messages cannot be empty"));
        }
        
        // 检查token限制
        let total_tokens = self.calculate_total_tokens(&request.messages)?;
        if total_tokens > request.max_tokens.unwrap_or(4096) {
            return Err(anyhow!("Total tokens exceed limit"));
        }
        
        // 检查模型可用性
        if !self.model_manager.is_model_available(&request.model_id).await? {
            return Err(anyhow!("Model {} is not available", request.model_id));
        }
        
        Ok(())
    }
    
    async fn select_and_load_model(&self, request: &InferenceRequest) -> Result<Arc<dyn Model>> {
        // 获取模型实例
        let model = self.model_manager.get_model(&request.model_id).await?;
        
        // 检查模型是否已加载
        if !model.is_loaded().await? {
            // 检查资源是否足够
            self.resource_manager.check_resources_for_model(&model).await?;
            
            // 加载模型
            model.load().await?;
            
            log::info!("Model {} loaded for inference", request.model_id);
        }
        
        Ok(model)
    }
    
    async fn stream_inference(
        &self,
        model: &Arc<dyn Model>,
        input: ProcessedInput,
    ) -> Result<StreamOutput> {
        let (tx, rx) = tokio::sync::mpsc::channel(100);
        
        let model_clone = model.clone();
        let input_clone = input.clone();
        
        tokio::spawn(async move {
            let mut token_stream = model_clone.generate_stream(input_clone).await.unwrap();
            
            while let Some(token) = token_stream.next().await {
                if tx.send(token).await.is_err() {
                    break; // 接收端已关闭
                }
            }
        });
        
        Ok(StreamOutput { receiver: rx })
    }
    
    async fn batch_inference(
        &self,
        model: &Arc<dyn Model>,
        input: ProcessedInput,
    ) -> Result<BatchOutput> {
        let output = model.generate(input).await?;
        Ok(BatchOutput { tokens: output.tokens, text: output.text })
    }
}
```
---

## 8. API接口设计

### 8.1 Tauri Invoke通信协议

#### 8.1.1 通信架构设计

AI Studio 采用 Tauri 的 invoke 机制实现前后端通信，所有的API调用都通过类型安全的接口进行，确保数据传输的可靠性和安全性。

**通信协议特点：**
- **类型安全**：TypeScript和Rust类型系统保证接口一致性
- **异步处理**：支持异步操作和流式数据传输
- **错误处理**：统一的错误处理机制和错误码
- **性能优化**：二进制数据传输和批量操作支持
- **安全控制**：权限验证和数据加密

#### 8.1.2 核心API接口规范

**聊天相关API**
```typescript
// 前端API接口定义
// types/api/chat.ts

// 创建聊天会话
export interface CreateSessionRequest {
  title?: string
  modelId: string
  systemPrompt?: string
  settings?: SessionSettings
}

export interface CreateSessionResponse {
  sessionId: string
  title: string
  modelId: string
  createdAt: number
}

// 发送消息
export interface SendMessageRequest {
  sessionId: string
  content: string
  attachments?: Attachment[]
  parentId?: string
  settings?: MessageSettings
}

export interface SendMessageResponse {
  messageId: string
  sessionId: string
  content: string
  role: 'assistant'
  tokens: number
  model: string
  finishReason: 'stop' | 'length' | 'error'
  usage: TokenUsage
  createdAt: number
}

// 流式消息响应
export interface StreamMessageChunk {
  messageId: string
  delta: string
  tokens?: number
  finishReason?: 'stop' | 'length' | 'error'
  usage?: TokenUsage
}

// 获取会话列表
export interface GetSessionsRequest {
  page?: number
  pageSize?: number
  archived?: boolean
  search?: string
  sortBy?: 'createdAt' | 'updatedAt' | 'title'
  sortOrder?: 'asc' | 'desc'
}

export interface GetSessionsResponse {
  sessions: ChatSession[]
  pagination: PaginationInfo
}

// 获取消息历史
export interface GetMessagesRequest {
  sessionId: string
  page?: number
  pageSize?: number
  beforeId?: string
  afterId?: string
}

export interface GetMessagesResponse {
  messages: ChatMessage[]
  pagination: PaginationInfo
  hasMore: boolean
}

// Tauri命令调用接口
export class ChatAPI {
  // 创建会话
  static async createSession(request: CreateSessionRequest): Promise<CreateSessionResponse> {
    return await invoke('chat_create_session', { request })
  }

  // 发送消息（非流式）
  static async sendMessage(request: SendMessageRequest): Promise<SendMessageResponse> {
    return await invoke('chat_send_message', { request })
  }

  // 发送消息（流式）
  static async sendMessageStream(
    request: SendMessageRequest,
    onChunk: (chunk: StreamMessageChunk) => void,
    onError: (error: string) => void,
    onComplete: () => void
  ): Promise<void> {
    const unlisten = await listen<StreamMessageChunk>('chat_message_chunk', (event) => {
      onChunk(event.payload)
    })

    const unlistenError = await listen<string>('chat_message_error', (event) => {
      onError(event.payload)
      unlisten()
      unlistenError()
    })

    const unlistenComplete = await listen('chat_message_complete', () => {
      onComplete()
      unlisten()
      unlistenError()
      unlistenComplete()
    })

    try {
      await invoke('chat_send_message_stream', { request })
    } catch (error) {
      unlisten()
      unlistenError()
      unlistenComplete()
      throw error
    }
  }

  // 获取会话列表
  static async getSessions(request: GetSessionsRequest = {}): Promise<GetSessionsResponse> {
    return await invoke('chat_get_sessions', { request })
  }

  // 获取消息历史
  static async getMessages(request: GetMessagesRequest): Promise<GetMessagesResponse> {
    return await invoke('chat_get_messages', { request })
  }

  // 删除会话
  static async deleteSession(sessionId: string): Promise<void> {
    return await invoke('chat_delete_session', { sessionId })
  }

  // 更新会话设置
  static async updateSessionSettings(
    sessionId: string,
    settings: Partial<SessionSettings>
  ): Promise<void> {
    return await invoke('chat_update_session_settings', { sessionId, settings })
  }

  // 归档会话
  static async archiveSession(sessionId: string, archived: boolean = true): Promise<void> {
    return await invoke('chat_archive_session', { sessionId, archived })
  }

  // 重命名会话
  static async renameSession(sessionId: string, title: string): Promise<void> {
    return await invoke('chat_rename_session', { sessionId, title })
  }

  // 导出会话
  static async exportSession(sessionId: string, format: 'json' | 'markdown' | 'txt'): Promise<string> {
    return await invoke('chat_export_session', { sessionId, format })
  }
}
```

**知识库相关API**
```typescript
// types/api/knowledge.ts

// 创建知识库
export interface CreateKnowledgeBaseRequest {
  name: string
  description?: string
  type?: string
  embeddingModel: string
  chunkSize?: number
  chunkOverlap?: number
  config?: Record<string, any>
}

export interface CreateKnowledgeBaseResponse {
  id: string
  name: string
  description?: string
  type: string
  embeddingModel: string
  createdAt: number
}

// 上传文档
export interface UploadDocumentsRequest {
  knowledgeBaseId: string
  files: FileInfo[]
  options?: {
    overwrite?: boolean
    skipDuplicates?: boolean
    extractMetadata?: boolean
  }
}

export interface UploadDocumentsResponse {
  uploadId: string
  documents: DocumentInfo[]
  totalFiles: number
  totalSize: number
}

// 文档处理进度
export interface ProcessingProgress {
  uploadId: string
  totalDocuments: number
  processedDocuments: number
  currentDocument?: string
  status: 'pending' | 'processing' | 'completed' | 'error'
  progress: number // 0-1
  estimatedTimeRemaining?: number
  errors?: ProcessingError[]
}

// 搜索文档
export interface SearchDocumentsRequest {
  knowledgeBaseId: string
  query: string
  options?: {
    limit?: number
    threshold?: number
    includeContent?: boolean
    includeMetadata?: boolean
    filters?: Record<string, any>
    searchType?: 'vector' | 'keyword' | 'hybrid'
  }
}

export interface SearchDocumentsResponse {
  query: string
  results: SearchResult[]
  totalFound: number
  processingTime: number
  metadata: {
    vectorMatches: number
    keywordMatches: number
    searchType: string
  }
}

// 知识库API类
export class KnowledgeAPI {
  // 创建知识库
  static async createKnowledgeBase(request: CreateKnowledgeBaseRequest): Promise<CreateKnowledgeBaseResponse> {
    return await invoke('knowledge_create_knowledge_base', { request })
  }

  // 获取知识库列表
  static async getKnowledgeBases(): Promise<KnowledgeBase[]> {
    return await invoke('knowledge_get_knowledge_bases')
  }

  // 上传文档
  static async uploadDocuments(request: UploadDocumentsRequest): Promise<UploadDocumentsResponse> {
    return await invoke('knowledge_upload_documents', { request })
  }

  // 监听处理进度
  static async monitorProcessing(
    uploadId: string,
    onProgress: (progress: ProcessingProgress) => void,
    onComplete: () => void,
    onError: (error: string) => void
  ): Promise<() => void> {
    const unlisten = await listen<ProcessingProgress>('knowledge_processing_progress', (event) => {
      const progress = event.payload
      if (progress.uploadId === uploadId) {
        onProgress(progress)
        
        if (progress.status === 'completed') {
          onComplete()
        } else if (progress.status === 'error') {
          onError('Processing failed')
        }
      }
    })

    return unlisten
  }

  // 搜索文档
  static async searchDocuments(request: SearchDocumentsRequest): Promise<SearchDocumentsResponse> {
    return await invoke('knowledge_search_documents', { request })
  }

  // 获取文档列表
  static async getDocuments(
    knowledgeBaseId: string,
    options?: {
      page?: number
      pageSize?: number
      status?: string
      search?: string
    }
  ): Promise<{ documents: DocumentInfo[], pagination: PaginationInfo }> {
    return await invoke('knowledge_get_documents', { knowledgeBaseId, options })
  }

  // 删除文档
  static async deleteDocuments(documentIds: string[]): Promise<void> {
    return await invoke('knowledge_delete_documents', { documentIds })
  }

  // 重新处理文档
  static async reprocessDocuments(documentIds: string[]): Promise<string> {
    return await invoke('knowledge_reprocess_documents', { documentIds })
  }

  // 获取知识库统计
  static async getKnowledgeBaseStats(knowledgeBaseId: string): Promise<KnowledgeBaseStats> {
    return await invoke('knowledge_get_knowledge_base_stats', { knowledgeBaseId })
  }

  // 导出知识库
  static async exportKnowledgeBase(
    knowledgeBaseId: string,
    format: 'json' | 'csv' | 'backup'
  ): Promise<string> {
    return await invoke('knowledge_export_knowledge_base', { knowledgeBaseId, format })
  }

  // 导入知识库
  static async importKnowledgeBase(filePath: string): Promise<string> {
    return await invoke('knowledge_import_knowledge_base', { filePath })
  }
}
```

**模型管理API**
```typescript
// types/api/model.ts

// 获取可用模型
export interface GetAvailableModelsRequest {
  type?: 'llm' | 'embedding' | 'multimodal'
  provider?: string
  local?: boolean
}

export interface GetAvailableModelsResponse {
  models: ModelInfo[]
  categories: ModelCategory[]
}

// 下载模型
export interface DownloadModelRequest {
  modelId: string
  source?: string
  priority?: 'low' | 'normal' | 'high'
}

export interface DownloadModelResponse {
  downloadId: string
  modelId: string
  totalSize: number
  estimatedTime: number
}

// 下载进度
export interface DownloadProgress {
  downloadId: string
  modelId: string
  status: 'pending' | 'downloading' | 'completed' | 'error' | 'paused'
  progress: number // 0-1
  downloadedBytes: number
  totalBytes: number
  speed: number // bytes per second
  estimatedTimeRemaining: number
  error?: string
}

// 模型API类
export class ModelAPI {
  // 获取可用模型
  static async getAvailableModels(request: GetAvailableModelsRequest = {}): Promise<GetAvailableModelsResponse> {
    return await invoke('model_get_available_models', { request })
  }

  // 获取已安装模型
  static async getInstalledModels(): Promise<ModelInfo[]> {
    return await invoke('model_get_installed_models')
  }

  // 下载模型
  static async downloadModel(request: DownloadModelRequest): Promise<DownloadModelResponse> {
    return await invoke('model_download_model', { request })
  }

  // 监听下载进度
  static async monitorDownload(
    downloadId: string,
    onProgress: (progress: DownloadProgress) => void,
    onComplete: () => void,
    onError: (error: string) => void
  ): Promise<() => void> {
    const unlisten = await listen<DownloadProgress>('model_download_progress', (event) => {
      const progress = event.payload
      if (progress.downloadId === downloadId) {
        onProgress(progress)
        
        if (progress.status === 'completed') {
          onComplete()
        } else if (progress.status === 'error') {
          onError(progress.error || 'Download failed')
        }
      }
    })

    return unlisten
  }

  // 暂停下载
  static async pauseDownload(downloadId: string): Promise<void> {
    return await invoke('model_pause_download', { downloadId })
  }

  // 恢复下载
  static async resumeDownload(downloadId: string): Promise<void> {
    return await invoke('model_resume_download', { downloadId })
  }

  // 取消下载
  static async cancelDownload(downloadId: string): Promise<void> {
    return await invoke('model_cancel_download', { downloadId })
  }

  // 加载模型
  static async loadModel(modelId: string): Promise<void> {
    return await invoke('model_load_model', { modelId })
  }

  // 卸载模型
  static async unloadModel(modelId: string): Promise<void> {
    return await invoke('model_unload_model', { modelId })
  }

  // 获取模型状态
  static async getModelStatus(modelId: string): Promise<ModelStatus> {
    return await invoke('model_get_model_status', { modelId })
  }

  // 删除模型
  static async deleteModel(modelId: string): Promise<void> {
    return await invoke('model_delete_model', { modelId })
  }

  // 获取模型配置
  static async getModelConfig(modelId: string): Promise<ModelConfig> {
    return await invoke('model_get_model_config', { modelId })
  }

  // 更新模型配置
  static async updateModelConfig(modelId: string, config: Partial<ModelConfig>): Promise<void> {
    return await invoke('model_update_model_config', { modelId, config })
  }
}
```

### 8.2 前后端接口规范

#### 8.2.1 接口设计原则

**统一响应格式**
```typescript
// 标准API响应格式
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  error?: {
    code: string
    message: string
    details?: any
    timestamp: number
  }
  metadata?: {
    requestId: string
    timestamp: number
    version: string
    processingTime?: number
  }
}

// 分页响应格式
export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: {
    page: number
    pageSize: number
    total: number
    totalPages: number
    hasNext: boolean
    hasPrev: boolean
  }
}

// 流式响应格式
export interface StreamResponse<T> {
  id: string
  event: string
  data: T
  timestamp: number
}
```

**错误处理规范**
```typescript
// 错误代码定义
export enum ErrorCode {
  // 通用错误 (1000-1999)
  UNKNOWN_ERROR = 'E1000',
  INVALID_REQUEST = 'E1001',
  UNAUTHORIZED = 'E1002',
  FORBIDDEN = 'E1003',
  NOT_FOUND = 'E1004',
  RATE_LIMITED = 'E1005',
  
  // 聊天相关错误 (2000-2999)
  CHAT_SESSION_NOT_FOUND = 'E2001',
  CHAT_MESSAGE_TOO_LONG = 'E2002',
  CHAT_MODEL_NOT_AVAILABLE = 'E2003',
  CHAT_CONTEXT_LIMIT_EXCEEDED = 'E2004',
  
  // 知识库相关错误 (3000-3999)
  KNOWLEDGE_BASE_NOT_FOUND = 'E3001',
  DOCUMENT_UPLOAD_FAILED = 'E3002',
  DOCUMENT_PROCESSING_FAILED = 'E3003',
  EMBEDDING_GENERATION_FAILED = 'E3004',
  
  // 模型相关错误 (4000-4999)
  MODEL_NOT_FOUND = 'E4001',
  MODEL_DOWNLOAD_FAILED = 'E4002',
  MODEL_LOAD_FAILED = 'E4003',
  MODEL_INFERENCE_FAILED = 'E4004',
  
  // 系统相关错误 (5000-5999)
  SYSTEM_RESOURCE_INSUFFICIENT = 'E5001',
  SYSTEM_DATABASE_ERROR = 'E5002',
  SYSTEM_NETWORK_ERROR = 'E5003',
  SYSTEM_PERMISSION_DENIED = 'E5004',
}

// 错误处理工具类
export class ApiError extends Error {
  constructor(
    public code: ErrorCode,
    message: string,
    public details?: any
  ) {
    super(message)
    this.name = 'ApiError'
  }

  toResponse(): ApiResponse {
    return {
      success: false,
      error: {
        code: this.code,
        message: this.message,
        details: this.details,
        timestamp: Date.now()
      }
    }
  }
}
```
---

## 9. 错误处理机制

### 9.1 异常捕获策略

#### 9.1.1 分层错误处理架构

AI Studio 采用分层错误处理架构，确保错误能够在合适的层级被捕获和处理，提供良好的用户体验和系统稳定性。

```
错误处理层次结构:

┌─────────────────────────────────────────────────────────────┐
│                    用户界面层                                │
│  • 用户友好的错误提示                                        │
│  • 错误状态显示                                             │
│  • 重试和恢复操作                                           │
│  • 错误上报机制                                             │
└─────────────────────────────────────────────────────────────┘
                              ↑
┌─────────────────────────────────────────────────────────────┐
│                    业务逻辑层                                │
│  • 业务规则验证                                             │
│  • 数据完整性检查                                           │
│  • 权限验证错误                                             │
│  • 业务流程异常                                             │
└─────────────────────────────────────────────────────────────┘
                              ↑
┌─────────────────────────────────────────────────────────────┐
│                    服务接口层                                │
│  • API调用错误                                              │
│  • 参数验证失败                                             │
│  • 服务不可用                                               │
│  • 超时和重试                                               │
└─────────────────────────────────────────────────────────────┘
                              ↑
┌─────────────────────────────────────────────────────────────┐
│                    数据访问层                                │
│  • 数据库连接错误                                           │
│  • SQL执行失败                                              │
│  • 数据一致性错误                                           │
│  • 事务回滚                                                 │
└─────────────────────────────────────────────────────────────┘
                              ↑
┌─────────────────────────────────────────────────────────────┐
│                    系统基础层                                │
│  • 网络连接错误                                             │
│  • 文件系统错误                                             │
│  • 内存不足                                                 │
│  • 系统资源错误                                             │
└─────────────────────────────────────────────────────────────┘
```

#### 9.1.2 前端错误处理实现

**全局错误处理器**
```typescript
// utils/errorHandler.ts
export class ErrorHandler {
  private static instance: ErrorHandler
  private errorQueue: ErrorInfo[] = []
  private maxQueueSize = 100

  static getInstance(): ErrorHandler {
    if (!ErrorHandler.instance) {
      ErrorHandler.instance = new ErrorHandler()
    }
    return ErrorHandler.instance
  }

  // 处理未捕获的错误
  handleGlobalError(error: Error, errorInfo?: any): void {
    const errorDetail: ErrorInfo = {
      id: uuid(),
      type: 'uncaught',
      message: error.message,
      stack: error.stack,
      timestamp: Date.now(),
      url: window.location.href,
      userAgent: navigator.userAgent,
      additional: errorInfo
    }

    this.logError(errorDetail)
    this.reportError(errorDetail)
    this.showUserNotification(errorDetail)
  }

  // 处理API错误
  handleApiError(error: ApiError, context?: string): void {
    const errorDetail: ErrorInfo = {
      id: uuid(),
      type: 'api',
      code: error.code,
      message: error.message,
      context,
      timestamp: Date.now(),
      additional: error.details
    }

    this.logError(errorDetail)
    
    // 根据错误类型决定是否上报
    if (this.shouldReportError(error.code)) {
      this.reportError(errorDetail)
    }

    this.showUserNotification(errorDetail)
  }

  // 处理业务逻辑错误
  handleBusinessError(error: BusinessError): void {
    const errorDetail: ErrorInfo = {
      id: uuid(),
      type: 'business',
      code: error.code,
      message: error.message,
      timestamp: Date.now(),
      additional: error.context
    }

    this.logError(errorDetail)
    this.showUserNotification(errorDetail)
  }

  private logError(error: ErrorInfo): void {
    // 添加到错误队列
    this.errorQueue.push(error)
    if (this.errorQueue.length > this.maxQueueSize) {
      this.errorQueue.shift()
    }

    // 控制台输出
    console.error(`[${error.type.toUpperCase()}] ${error.message}`, error)

    // 写入本地日志
    this.writeToLocalLog(error)
  }

  private async reportError(error: ErrorInfo): Promise<void> {
    try {
      // 发送错误报告到后端
      await invoke('system_report_error', { error })
    } catch (reportError) {
      console.warn('Failed to report error:', reportError)
    }
  }

  private showUserNotification(error: ErrorInfo): void {
    const userMessage = this.getUserFriendlyMessage(error)
    
    // 显示用户通知
    useNotificationStore().addNotification({
      type: 'error',
      title: '操作失败',
      message: userMessage,
      duration: 5000,
      actions: this.getErrorActions(error)
    })
  }

  private getUserFriendlyMessage(error: ErrorInfo): string {
    const messageMap: Record<string, string> = {
      [ErrorCode.CHAT_MODEL_NOT_AVAILABLE]: '当前模型不可用，请选择其他模型或稍后重试',
      [ErrorCode.KNOWLEDGE_BASE_NOT_FOUND]: '知识库不存在，请检查后重试',
      [ErrorCode.MODEL_DOWNLOAD_FAILED]: '模型下载失败，请检查网络连接后重试',
      [ErrorCode.SYSTEM_RESOURCE_INSUFFICIENT]: '系统资源不足，请关闭其他应用后重试',
      [ErrorCode.SYSTEM_NETWORK_ERROR]: '网络连接异常，请检查网络设置',
    }

    return messageMap[error.code as ErrorCode] || error.message || '发生未知错误，请稍后重试'
  }

  private getErrorActions(error: ErrorInfo): NotificationAction[] {
    const actions: NotificationAction[] = []

    // 重试操作
    if (this.isRetryableError(error.code)) {
      actions.push({
        label: '重试',
        action: () => this.retryLastOperation(error)
      })
    }

    // 查看详情
    actions.push({
      label: '查看详情',
      action: () => this.showErrorDetails(error)
    })

    // 反馈问题
    if (error.type === 'uncaught' || this.isCriticalError(error.code)) {
      actions.push({
        label: '反馈问题',
        action: () => this.openFeedbackDialog(error)
      })
    }

    return actions
  }
}

// Vue错误处理插件
export const errorHandlerPlugin = {
  install(app: App) {
    const errorHandler = ErrorHandler.getInstance()

    // 全局错误处理
    app.config.errorHandler = (error: Error, instance, info) => {
      errorHandler.handleGlobalError(error, { instance, info })
    }

    // 全局警告处理
    app.config.warnHandler = (msg, instance, trace) => {
      console.warn(`[Vue warn]: ${msg}`, { instance, trace })
    }

    // 未处理的Promise拒绝
    window.addEventListener('unhandledrejection', (event) => {
      errorHandler.handleGlobalError(
        new Error(event.reason?.message || 'Unhandled Promise Rejection'),
        { reason: event.reason }
      )
    })

    // 资源加载错误
    window.addEventListener('error', (event) => {
      if (event.target !== window) {
        errorHandler.handleGlobalError(
          new Error(`Resource loading failed: ${event.target?.src || event.target?.href}`),
          { target: event.target }
        )
      }
    })
  }
}
```

#### 9.1.3 后端错误处理实现

**Rust错误处理系统**
```rust
// src/core/error.rs
use serde::{Deserialize, Serialize};
use std::fmt;
use thiserror::Error;

// 应用错误类型定义
#[derive(Error, Debug, Clone, Serialize, Deserialize)]
pub enum AppError {
    #[error("数据库错误: {message}")]
    Database { message: String, source: Option<String> },
    
    #[error("网络错误: {message}")]
    Network { message: String, status_code: Option<u16> },
    
    #[error("文件系统错误: {message}")]
    FileSystem { message: String, path: Option<String> },
    
    #[error("AI推理错误: {message}")]
    Inference { message: String, model_id: Option<String> },
    
    #[error("验证错误: {message}")]
    Validation { message: String, field: Option<String> },
    
    #[error("权限错误: {message}")]
    Permission { message: String, required_permission: Option<String> },
    
    #[error("资源不足: {message}")]
    ResourceExhausted { message: String, resource_type: String },
    
    #[error("配置错误: {message}")]
    Configuration { message: String, config_key: Option<String> },
    
    #[error("外部服务错误: {message}")]
    ExternalService { message: String, service: String },
    
    #[error("内部错误: {message}")]
    Internal { message: String },
}

impl AppError {
    pub fn error_code(&self) -> &'static str {
        match self {
            AppError::Database { .. } => "E5002",
            AppError::Network { .. } => "E5003",
            AppError::FileSystem { .. } => "E5004",
            AppError::Inference { .. } => "E4004",
            AppError::Validation { .. } => "E1001",
            AppError::Permission { .. } => "E1003",
            AppError::ResourceExhausted { .. } => "E5001",
            AppError::Configuration { .. } => "E1001",
            AppError::ExternalService { .. } => "E5003",
            AppError::Internal { .. } => "E1000",
        }
    }

    pub fn is_retryable(&self) -> bool {
        matches!(
            self,
            AppError::Network { .. } | 
            AppError::ExternalService { .. } |
            AppError::ResourceExhausted { .. }
        )
    }

    pub fn severity(&self) -> ErrorSeverity {
        match self {
            AppError::Internal { .. } => ErrorSeverity::Critical,
            AppError::Database { .. } => ErrorSeverity::High,
            AppError::ResourceExhausted { .. } => ErrorSeverity::High,
            AppError::Permission { .. } => ErrorSeverity::Medium,
            AppError::Validation { .. } => ErrorSeverity::Low,
            _ => ErrorSeverity::Medium,
        }
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum ErrorSeverity {
    Low,
    Medium,
    High,
    Critical,
}

// 错误处理器
pub struct ErrorHandler {
    logger: Arc<Logger>,
    metrics: Arc<MetricsCollector>,
    notifier: Arc<ErrorNotifier>,
}

impl ErrorHandler {
    pub fn new(
        logger: Arc<Logger>,
        metrics: Arc<MetricsCollector>,
        notifier: Arc<ErrorNotifier>,
    ) -> Self {
        Self {
            logger,
            metrics,
            notifier,
        }
    }

    pub async fn handle_error(&self, error: AppError, context: ErrorContext) -> AppError {
        let error_info = ErrorInfo {
            id: uuid::Uuid::new_v4().to_string(),
            error: error.clone(),
            context,
            timestamp: chrono::Utc::now(),
            severity: error.severity(),
        };

        // 记录错误日志
        self.log_error(&error_info).await;

        // 更新错误指标
        self.update_metrics(&error_info).await;

        // 发送通知（如果需要）
        if self.should_notify(&error_info) {
            self.send_notification(&error_info).await;
        }

        // 尝试错误恢复
        if let Some(recovered_error) = self.attempt_recovery(&error_info).await {
            return recovered_error;
        }

        error
    }

    async fn log_error(&self, error_info: &ErrorInfo) {
        self.logger.error(
            &format!("Error occurred: {}", error_info.error),
            Some(json!({
                "error_id": error_info.id,
                "error_code": error_info.error.error_code(),
                "severity": error_info.severity,
                "context": error_info.context,
                "timestamp": error_info.timestamp,
            }))
        ).await;
    }

    async fn update_metrics(&self, error_info: &ErrorInfo) {
        self.metrics.increment_counter(
            "errors_total",
            &[
                ("error_code", error_info.error.error_code()),
                ("severity", &format!("{:?}", error_info.severity)),
            ]
        ).await;
    }

    fn should_notify(&self, error_info: &ErrorInfo) -> bool {
        matches!(error_info.severity, ErrorSeverity::High | ErrorSeverity::Critical)
    }

    async fn send_notification(&self, error_info: &ErrorInfo) {
        if let Err(e) = self.notifier.send_error_notification(error_info).await {
            log::warn!("Failed to send error notification: {}", e);
        }
    }

    async fn attempt_recovery(&self, error_info: &ErrorInfo) -> Option<AppError> {
        match &error_info.error {
            AppError::Database { .. } => {
                // 尝试重新连接数据库
                if let Ok(_) = self.reconnect_database().await {
                    return None; // 恢复成功
                }
            }
            AppError::Network { .. } => {
                // 网络错误通常需要重试
                return None;
            }
            _ => {}
        }
        
        None
    }

    async fn reconnect_database(&self) -> Result<(), AppError> {
        // 数据库重连逻辑
        Ok(())
    }
}

// 错误上下文
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ErrorContext {
    pub operation: String,
    pub user_id: Option<String>,
    pub session_id: Option<String>,
    pub request_id: Option<String>,
    pub additional_data: Option<serde_json::Value>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ErrorInfo {
    pub id: String,
    pub error: AppError,
    pub context: ErrorContext,
    pub timestamp: chrono::DateTime<chrono::Utc>,
    pub severity: ErrorSeverity,
}

// 错误恢复策略
pub trait ErrorRecoveryStrategy {
    async fn can_recover(&self, error: &AppError) -> bool;
    async fn recover(&self, error: &AppError, context: &ErrorContext) -> Result<(), AppError>;
}

// 重试策略
pub struct RetryStrategy {
    max_attempts: u32,
    base_delay: std::time::Duration,
    max_delay: std::time::Duration,
    backoff_multiplier: f64,
}

impl RetryStrategy {
    pub fn new() -> Self {
        Self {
            max_attempts: 3,
            base_delay: std::time::Duration::from_millis(100),
            max_delay: std::time::Duration::from_secs(30),
            backoff_multiplier: 2.0,
        }
    }

    pub async fn execute_with_retry<F, T, E>(&self, mut operation: F) -> Result<T, E>
    where
        F: FnMut() -> std::pin::Pin<Box<dyn std::future::Future<Output = Result<T, E>> + Send>>,
        E: std::fmt::Debug,
    {
        let mut last_error = None;
        
        for attempt in 1..=self.max_attempts {
            match operation().await {
                Ok(result) => return Ok(result),
                Err(error) => {
                    last_error = Some(error);
                    
                    if attempt < self.max_attempts {
                        let delay = self.calculate_delay(attempt);
                        tokio::time::sleep(delay).await;
                    }
                }
            }
        }
        
        Err(last_error.unwrap())
    }

    fn calculate_delay(&self, attempt: u32) -> std::time::Duration {
        let delay = self.base_delay.as_millis() as f64 * self.backoff_multiplier.powi(attempt as i32 - 1);
        let delay = std::time::Duration::from_millis(delay as u64);
        std::cmp::min(delay, self.max_delay)
    }
}
```

### 9.2 用户提示系统

#### 9.2.1 通知组件设计

**统一通知系统**
```vue
<!-- components/common/NotificationSystem.vue -->
<template>
  <Teleport to="body">
    <div class="notification-container">
      <TransitionGroup
        name="notification"
        tag="div"
        class="notification-list"
      >
        <div
          v-for="notification in notifications"
          :key="notification.id"
          :class="getNotificationClasses(notification)"
          class="notification-item"
        >
          <div class="notification-icon">
            <Icon :name="getNotificationIcon(notification.type)" />
          </div>
          
          <div class="notification-content">
            <h4 v-if="notification.title" class="notification-title">
              {{ notification.title }}
            </h4>
            <p class="notification-message">
              {{ notification.message }}
            </p>
            
            <div v-if="notification.actions?.length" class="notification-actions">
              <Button
                v-for="action in notification.actions"
                :key="action.label"
                size="sm"
                variant="ghost"
                @click="handleAction(notification, action)"
              >
                {{ action.label }}
              </Button>
            </div>
          </div>
          
          <Button
            v-if="notification.closable !== false"
            variant="ghost"
            size="sm"
            icon="x"
            class="notification-close"
            @click="removeNotification(notification.id)"
          />
        </div>
      </TransitionGroup>
    </div>
  </Teleport>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useNotificationStore } from '@/stores/notification'

const notificationStore = useNotificationStore()

const notifications = computed(() => notificationStore.notifications)

const getNotificationClasses = (notification: Notification) => {
  const baseClasses = ['notification']
  
  const typeClasses = {
    success: 'notification--success',
    error: 'notification--error',
    warning: 'notification--warning',
    info: 'notification--info',
  }
  
  return [
    ...baseClasses,
    typeClasses[notification.type] || typeClasses.info
  ]
}

const getNotificationIcon = (type: NotificationType) => {
  const iconMap = {
    success: 'check-circle',
    error: 'x-circle',
    warning: 'alert-triangle',
    info: 'info-circle',
  }
  
  return iconMap[type] || iconMap.info
}

const handleAction = (notification: Notification, action: NotificationAction) => {
  action.action()
  
  if (action.closeAfterAction !== false) {
    removeNotification(notification.id)
  }
}

const removeNotification = (id: string) => {
  notificationStore.removeNotification(id)
}
</script>
```

## 10. 性能优化策略

### 10.1 内存管理优化

#### 10.1.1 前端内存优化

**组件生命周期管理**
```typescript
// composables/useMemoryOptimization.ts
export function useMemoryOptimization() {
  const cleanupTasks = new Set<() => void>()

  // 注册清理任务
  const registerCleanup = (cleanup: () => void) => {
    cleanupTasks.add(cleanup)
  }

  // 执行所有清理任务
  const cleanup = () => {
    cleanupTasks.forEach(task => {
      try {
        task()
      } catch (error) {
        console.warn('Cleanup task failed:', error)
      }
    })
    cleanupTasks.clear()
  }

  // 组件卸载时自动清理
  onUnmounted(() => {
    cleanup()
  })

  return {
    registerCleanup,
    cleanup
  }
}

// 大数据列表优化
export function useVirtualList<T>(
  items: Ref<T[]>,
  itemHeight: number,
  containerHeight: number
) {
  const scrollTop = ref(0)
  const visibleRange = computed(() => {
    const start = Math.floor(scrollTop.value / itemHeight)
    const visibleCount = Math.ceil(containerHeight / itemHeight)
    const end = Math.min(start + visibleCount + 5, items.value.length) // 预渲染5个
    
    return { start, end }
  })

  const visibleItems = computed(() => {
    const { start, end } = visibleRange.value
    return items.value.slice(start, end).map((item, index) => ({
      item,
      index: start + index,
      top: (start + index) * itemHeight
    }))
  })

  const totalHeight = computed(() => items.value.length * itemHeight)

  const handleScroll = (event: Event) => {
    const target = event.target as HTMLElement
    scrollTop.value = target.scrollTop
  }

  return {
    visibleItems,
    totalHeight,
    handleScroll
  }
}
```

#### 10.1.2 后端内存优化

**Rust内存管理策略**
```rust
// src/core/memory.rs
use std::sync::Arc;
use tokio::sync::RwLock;
use std::collections::HashMap;

// 内存池管理器
pub struct MemoryPool<T> {
    pool: Arc<RwLock<Vec<T>>>,
    factory: Box<dyn Fn() -> T + Send + Sync>,
    max_size: usize,
}

impl<T> MemoryPool<T> 
where 
    T: Send + Sync + 'static,
{
    pub fn new<F>(factory: F, max_size: usize) -> Self 
    where 
        F: Fn() -> T + Send + Sync + 'static,
    {
        Self {
            pool: Arc::new(RwLock::new(Vec::with_capacity(max_size))),
            factory: Box::new(factory),
            max_size,
        }
    }

    pub async fn acquire(&self) -> T {
        let mut pool = self.pool.write().await;
        
        if let Some(item) = pool.pop() {
            item
        } else {
            (self.factory)()
        }
    }

    pub async fn release(&self, item: T) {
        let mut pool = self.pool.write().await;
        
        if pool.len() < self.max_size {
            pool.push(item);
        }
        // 如果池已满，让item自然drop
    }
}

// 缓存管理器
pub struct CacheManager {
    caches: HashMap<String, Arc<dyn Cache + Send + Sync>>,
    memory_limit: usize,
    current_usage: Arc<AtomicUsize>,
}

impl CacheManager {
    pub fn new(memory_limit: usize) -> Self {
        Self {
            caches: HashMap::new(),
            memory_limit,
            current_usage: Arc::new(AtomicUsize::new(0)),
        }
    }

    pub async fn evict_if_needed(&self) {
        let current = self.current_usage.load(Ordering::Relaxed);
        
        if current > self.memory_limit {
            let target = self.memory_limit * 80 / 100; // 清理到80%
            let to_evict = current - target;
            
            // LRU清理策略
            self.evict_lru(to_evict).await;
        }
    }

    async fn evict_lru(&self, target_bytes: usize) {
        // 实现LRU清理逻辑
    }
}
```
