{"name": "ai-studio", "private": true, "version": "1.0.0", "type": "module", "description": "AI Studio - 基于 Vue3 + TypeScript + Vite + Tauri 2.x 的中文AI助手桌面应用", "author": "AI Studio Team", "license": "MIT", "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "preview": "vite preview", "tauri": "tauri", "tauri:dev": "tauri dev", "tauri:build": "tauri build", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "format": "prettier --write src/", "type-check": "vue-tsc --noEmit"}, "dependencies": {"@tauri-apps/api": "^2.0.0", "@tauri-apps/plugin-shell": "^2.0.0", "@tauri-apps/plugin-fs": "^2.0.0", "@tauri-apps/plugin-http": "^2.0.0", "@tauri-apps/plugin-dialog": "^2.0.0", "@tauri-apps/plugin-notification": "^2.0.0", "vue": "^3.4.0", "vue-router": "^4.2.5", "pinia": "^2.1.7", "naive-ui": "^2.38.1", "@vicons/ionicons5": "^0.12.0", "@vicons/material": "^0.12.0", "@vicons/tabler": "^0.12.0", "vue-i18n": "^9.8.0", "markdown-it": "^14.0.0", "highlight.js": "^11.9.0", "axios": "^1.6.2", "dayjs": "^1.11.10", "lodash-es": "^4.17.21", "uuid": "^9.0.1"}, "devDependencies": {"@tauri-apps/cli": "^2.0.0", "@types/node": "^20.10.0", "@types/uuid": "^9.0.7", "@types/lodash-es": "^4.17.12", "@types/markdown-it": "^13.0.7", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-vue": "^4.5.2", "@vue/eslint-config-prettier": "^8.0.0", "@vue/eslint-config-typescript": "^12.0.0", "autoprefixer": "^10.4.16", "eslint": "^8.55.0", "eslint-plugin-vue": "^9.19.2", "postcss": "^8.4.32", "prettier": "^3.1.1", "tailwindcss": "^3.3.6", "typescript": "^5.3.3", "unplugin-auto-import": "^0.17.2", "unplugin-vue-components": "^0.25.2", "vite": "^5.0.8", "vue-tsc": "^1.8.25"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}