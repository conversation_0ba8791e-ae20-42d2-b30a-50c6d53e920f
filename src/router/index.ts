import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'

// 导入布局组件
import MainLayout from '@/layouts/MainLayout.vue'

// 导入页面组件
import ChatView from '@/views/ChatView.vue'
import KnowledgeView from '@/views/KnowledgeView.vue'
import ModelView from '@/views/ModelView.vue'
import RemoteView from '@/views/RemoteView.vue'
import NetworkView from '@/views/NetworkView.vue'
import MultimodalView from '@/views/MultimodalView.vue'
import SettingsView from '@/views/SettingsView.vue'

// 路由配置
const routes: RouteRecordRaw[] = [
  {
    path: '/',
    component: MainLayout,
    redirect: '/chat',
    children: [
      {
        path: 'chat',
        name: 'Chat',
        component: ChatView,
        meta: {
          title: '聊天',
          icon: 'ChatBubbleOutline',
          order: 1
        }
      },
      {
        path: 'knowledge',
        name: 'Knowledge',
        component: KnowledgeView,
        meta: {
          title: '知识库',
          icon: 'LibraryOutline',
          order: 2
        }
      },
      {
        path: 'model',
        name: 'Model',
        component: ModelView,
        meta: {
          title: '模型管理',
          icon: 'CubeOutline',
          order: 3
        }
      },
      {
        path: 'remote',
        name: 'Remote',
        component: RemoteView,
        meta: {
          title: '远程配置',
          icon: 'CloudOutline',
          order: 4
        }
      },
      {
        path: 'network',
        name: 'Network',
        component: NetworkView,
        meta: {
          title: '局域网共享',
          icon: 'ShareSocialOutline',
          order: 5
        }
      },
      {
        path: 'multimodal',
        name: 'Multimodal',
        component: MultimodalView,
        meta: {
          title: '多模态',
          icon: 'ExtensionPuzzleOutline',
          order: 6
        }
      },
      {
        path: 'settings',
        name: 'Settings',
        component: SettingsView,
        meta: {
          title: '设置',
          icon: 'SettingsOutline',
          order: 7,
          hidden: true // 在主导航中隐藏，通过用户下拉菜单访问
        }
      }
    ]
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    redirect: '/chat'
  }
]

// 创建路由实例
const router = createRouter({
  history: createWebHistory(),
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

// 全局前置守卫
router.beforeEach((to, from, next) => {
  // 设置页面标题
  if (to.meta?.title) {
    document.title = `${to.meta.title} - AI Studio`
  } else {
    document.title = 'AI Studio - 智能AI助手'
  }

  // 这里可以添加权限检查逻辑
  // 例如：检查用户是否已登录，是否有访问权限等

  next()
})

// 全局后置钩子
router.afterEach((to, from) => {
  // 这里可以添加页面访问统计、埋点等逻辑
  console.log(`导航到: ${to.path}`)
})

// 路由错误处理
router.onError((error) => {
  console.error('路由错误:', error)
})

export default router

// 导出路由相关的工具函数
export function getMainRoutes() {
  return routes[0].children?.filter(route => !route.meta?.hidden) || []
}

export function getRouteByName(name: string) {
  const findRoute = (routes: RouteRecordRaw[]): RouteRecordRaw | undefined => {
    for (const route of routes) {
      if (route.name === name) {
        return route
      }
      if (route.children) {
        const found = findRoute(route.children)
        if (found) return found
      }
    }
  }
  return findRoute(routes)
}

export function isActiveRoute(routeName: string, currentRouteName?: string) {
  return currentRouteName === routeName
}

// 导航菜单项类型
export interface NavigationItem {
  name: string
  path: string
  title: string
  icon: string
  order: number
  hidden?: boolean
}

// 获取导航菜单项
export function getNavigationItems(): NavigationItem[] {
  const mainRoutes = getMainRoutes()
  return mainRoutes
    .map(route => ({
      name: route.name as string,
      path: route.path,
      title: route.meta?.title as string,
      icon: route.meta?.icon as string,
      order: route.meta?.order as number,
      hidden: route.meta?.hidden as boolean
    }))
    .sort((a, b) => a.order - b.order)
}
