import type { ThemeMode } from '@/types'

// 主题配置
export interface ThemeConfig {
  mode: ThemeMode
  primaryColor: string
  borderRadius: number
  fontSize: number
  fontFamily: string
}

// 默认主题配置
export const defaultThemeConfig: ThemeConfig = {
  mode: 'auto',
  primaryColor: '#3b82f6',
  borderRadius: 8,
  fontSize: 14,
  fontFamily: 'Inter, -apple-system, BlinkMacSystemFont, "Segoe UI", sans-serif'
}

// 主题管理器
export class ThemeManager {
  private config: ThemeConfig
  private mediaQuery: MediaQueryList
  private listeners: Set<(isDark: boolean) => void> = new Set()

  constructor(config: ThemeConfig = defaultThemeConfig) {
    this.config = { ...config }
    this.mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
    this.setupMediaQueryListener()
  }

  // 设置媒体查询监听器
  private setupMediaQueryListener() {
    this.mediaQuery.addEventListener('change', (e) => {
      if (this.config.mode === 'auto') {
        this.applyTheme(e.matches)
      }
    })
  }

  // 获取当前主题模式
  get mode(): ThemeMode {
    return this.config.mode
  }

  // 设置主题模式
  setMode(mode: ThemeMode) {
    this.config.mode = mode
    this.updateTheme()
    this.saveConfig()
  }

  // 获取是否为深色模式
  get isDark(): boolean {
    switch (this.config.mode) {
      case 'dark':
        return true
      case 'light':
        return false
      case 'auto':
        return this.mediaQuery.matches
      default:
        return false
    }
  }

  // 切换主题模式
  toggle() {
    const currentMode = this.config.mode
    if (currentMode === 'light') {
      this.setMode('dark')
    } else if (currentMode === 'dark') {
      this.setMode('light')
    } else {
      // auto模式下切换到相反的固定模式
      this.setMode(this.isDark ? 'light' : 'dark')
    }
  }

  // 更新主题
  private updateTheme() {
    const isDark = this.isDark
    this.applyTheme(isDark)
    this.notifyListeners(isDark)
  }

  // 应用主题
  private applyTheme(isDark: boolean) {
    const html = document.documentElement
    
    if (isDark) {
      html.classList.add('dark')
    } else {
      html.classList.remove('dark')
    }

    // 设置CSS变量
    html.style.setProperty('--primary-color', this.config.primaryColor)
    html.style.setProperty('--border-radius', `${this.config.borderRadius}px`)
    html.style.setProperty('--font-size', `${this.config.fontSize}px`)
    html.style.setProperty('--font-family', this.config.fontFamily)

    // 更新meta标签颜色
    this.updateMetaThemeColor(isDark)
  }

  // 更新meta主题颜色
  private updateMetaThemeColor(isDark: boolean) {
    let metaThemeColor = document.querySelector('meta[name="theme-color"]')
    if (!metaThemeColor) {
      metaThemeColor = document.createElement('meta')
      metaThemeColor.setAttribute('name', 'theme-color')
      document.head.appendChild(metaThemeColor)
    }
    
    const color = isDark ? '#0f172a' : '#ffffff'
    metaThemeColor.setAttribute('content', color)
  }

  // 添加主题变化监听器
  addListener(listener: (isDark: boolean) => void) {
    this.listeners.add(listener)
  }

  // 移除主题变化监听器
  removeListener(listener: (isDark: boolean) => void) {
    this.listeners.delete(listener)
  }

  // 通知监听器
  private notifyListeners(isDark: boolean) {
    this.listeners.forEach(listener => listener(isDark))
  }

  // 设置主色调
  setPrimaryColor(color: string) {
    this.config.primaryColor = color
    this.updateTheme()
    this.saveConfig()
  }

  // 设置边框圆角
  setBorderRadius(radius: number) {
    this.config.borderRadius = radius
    this.updateTheme()
    this.saveConfig()
  }

  // 设置字体大小
  setFontSize(size: number) {
    this.config.fontSize = size
    this.updateTheme()
    this.saveConfig()
  }

  // 设置字体族
  setFontFamily(family: string) {
    this.config.fontFamily = family
    this.updateTheme()
    this.saveConfig()
  }

  // 获取配置
  getConfig(): ThemeConfig {
    return { ...this.config }
  }

  // 设置配置
  setConfig(config: Partial<ThemeConfig>) {
    this.config = { ...this.config, ...config }
    this.updateTheme()
    this.saveConfig()
  }

  // 保存配置到本地存储
  private saveConfig() {
    try {
      localStorage.setItem('theme-config', JSON.stringify(this.config))
    } catch (error) {
      console.warn('无法保存主题配置:', error)
    }
  }

  // 从本地存储加载配置
  loadConfig(): ThemeConfig {
    try {
      const saved = localStorage.getItem('theme-config')
      if (saved) {
        const config = JSON.parse(saved)
        this.config = { ...defaultThemeConfig, ...config }
      }
    } catch (error) {
      console.warn('无法加载主题配置:', error)
      this.config = { ...defaultThemeConfig }
    }
    return this.config
  }

  // 重置配置
  resetConfig() {
    this.config = { ...defaultThemeConfig }
    this.updateTheme()
    this.saveConfig()
  }

  // 初始化主题
  init() {
    this.loadConfig()
    this.updateTheme()
  }

  // 销毁主题管理器
  destroy() {
    this.listeners.clear()
    this.mediaQuery.removeEventListener('change', this.setupMediaQueryListener)
  }
}

// 全局主题管理器实例
export const themeManager = new ThemeManager()

// 设置主题的便捷函数
export function setupTheme() {
  themeManager.init()
}

// 获取当前主题状态
export function useTheme() {
  return {
    mode: themeManager.mode,
    isDark: themeManager.isDark,
    config: themeManager.getConfig(),
    setMode: themeManager.setMode.bind(themeManager),
    toggle: themeManager.toggle.bind(themeManager),
    setPrimaryColor: themeManager.setPrimaryColor.bind(themeManager),
    setBorderRadius: themeManager.setBorderRadius.bind(themeManager),
    setFontSize: themeManager.setFontSize.bind(themeManager),
    setFontFamily: themeManager.setFontFamily.bind(themeManager),
    setConfig: themeManager.setConfig.bind(themeManager),
    resetConfig: themeManager.resetConfig.bind(themeManager)
  }
}
