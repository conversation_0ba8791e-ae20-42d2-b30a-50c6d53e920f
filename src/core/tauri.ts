import { invoke } from '@tauri-apps/api/core'
import { listen } from '@tauri-apps/api/event'
import { getCurrentWindow } from '@tauri-apps/api/window'
import { message } from '@tauri-apps/plugin-dialog'
import { isPermissionGranted, requestPermission, sendNotification } from '@tauri-apps/plugin-notification'

// Tauri命令类型定义
export interface TauriCommand {
  [key: string]: (...args: any[]) => Promise<any>
}

// Tauri事件类型
export interface TauriEvent<T = any> {
  event: string
  payload: T
  id: number
  windowLabel: string
}

// Tauri配置管理器
export class TauriManager {
  private window = getCurrentWindow()
  private eventListeners = new Map<string, Set<Function>>()

  constructor() {
    this.setupGlobalErrorHandler()
  }

  // 设置全局错误处理
  private setupGlobalErrorHandler() {
    window.addEventListener('unhandledrejection', (event) => {
      console.error('未处理的Promise拒绝:', event.reason)
      this.showErrorNotification('应用发生未知错误', event.reason?.message || '请检查控制台获取详细信息')
    })

    window.addEventListener('error', (event) => {
      console.error('全局错误:', event.error)
      this.showErrorNotification('应用发生错误', event.error?.message || event.message)
    })
  }

  // 调用Tauri命令
  async invokeCommand<T = any>(command: string, args?: any): Promise<T> {
    try {
      const result = await invoke<T>(command, args)
      return result
    } catch (error) {
      console.error(`调用命令 ${command} 失败:`, error)
      throw error
    }
  }

  // 监听Tauri事件
  async listenEvent<T = any>(event: string, handler: (event: TauriEvent<T>) => void): Promise<() => void> {
    try {
      const unlisten = await listen<T>(event, handler)
      
      // 记录监听器
      if (!this.eventListeners.has(event)) {
        this.eventListeners.set(event, new Set())
      }
      this.eventListeners.get(event)!.add(handler)

      // 返回取消监听的函数
      return () => {
        unlisten()
        const listeners = this.eventListeners.get(event)
        if (listeners) {
          listeners.delete(handler)
          if (listeners.size === 0) {
            this.eventListeners.delete(event)
          }
        }
      }
    } catch (error) {
      console.error(`监听事件 ${event} 失败:`, error)
      throw error
    }
  }

  // 发送通知
  async sendNotification(title: string, body?: string, icon?: string) {
    try {
      let permissionGranted = await isPermissionGranted()
      
      if (!permissionGranted) {
        const permission = await requestPermission()
        permissionGranted = permission === 'granted'
      }
      
      if (permissionGranted) {
        await sendNotification({
          title,
          body,
          icon
        })
      } else {
        console.warn('通知权限未授予')
      }
    } catch (error) {
      console.error('发送通知失败:', error)
    }
  }

  // 显示错误通知
  async showErrorNotification(title: string, body: string) {
    await this.sendNotification(`❌ ${title}`, body)
  }

  // 显示成功通知
  async showSuccessNotification(title: string, body?: string) {
    await this.sendNotification(`✅ ${title}`, body)
  }

  // 显示信息通知
  async showInfoNotification(title: string, body?: string) {
    await this.sendNotification(`ℹ️ ${title}`, body)
  }

  // 显示对话框
  async showDialog(title: string, message: string, type: 'info' | 'warning' | 'error' = 'info') {
    try {
      await message(message, { title, kind: type })
    } catch (error) {
      console.error('显示对话框失败:', error)
    }
  }

  // 窗口操作
  async minimizeWindow() {
    try {
      await this.window.minimize()
    } catch (error) {
      console.error('最小化窗口失败:', error)
    }
  }

  async maximizeWindow() {
    try {
      await this.window.toggleMaximized()
    } catch (error) {
      console.error('最大化窗口失败:', error)
    }
  }

  async closeWindow() {
    try {
      await this.window.close()
    } catch (error) {
      console.error('关闭窗口失败:', error)
    }
  }

  async hideWindow() {
    try {
      await this.window.hide()
    } catch (error) {
      console.error('隐藏窗口失败:', error)
    }
  }

  async showWindow() {
    try {
      await this.window.show()
      await this.window.setFocus()
    } catch (error) {
      console.error('显示窗口失败:', error)
    }
  }

  // 获取窗口状态
  async isWindowMaximized(): Promise<boolean> {
    try {
      return await this.window.isMaximized()
    } catch (error) {
      console.error('获取窗口最大化状态失败:', error)
      return false
    }
  }

  async isWindowMinimized(): Promise<boolean> {
    try {
      return await this.window.isMinimized()
    } catch (error) {
      console.error('获取窗口最小化状态失败:', error)
      return false
    }
  }

  async isWindowVisible(): Promise<boolean> {
    try {
      return await this.window.isVisible()
    } catch (error) {
      console.error('获取窗口可见状态失败:', error)
      return true
    }
  }

  // 设置窗口标题
  async setWindowTitle(title: string) {
    try {
      await this.window.setTitle(title)
    } catch (error) {
      console.error('设置窗口标题失败:', error)
    }
  }

  // 清理所有事件监听器
  cleanup() {
    this.eventListeners.clear()
  }

  // 获取应用版本
  async getAppVersion(): Promise<string> {
    try {
      return await this.invokeCommand('get_app_version')
    } catch (error) {
      console.error('获取应用版本失败:', error)
      return '未知版本'
    }
  }

  // 检查应用更新
  async checkForUpdates(): Promise<boolean> {
    try {
      return await this.invokeCommand('check_for_updates')
    } catch (error) {
      console.error('检查更新失败:', error)
      return false
    }
  }

  // 重启应用
  async restartApp() {
    try {
      await this.invokeCommand('restart_app')
    } catch (error) {
      console.error('重启应用失败:', error)
    }
  }

  // 退出应用
  async exitApp() {
    try {
      await this.invokeCommand('exit_app')
    } catch (error) {
      console.error('退出应用失败:', error)
    }
  }
}

// 全局Tauri管理器实例
export const tauriManager = new TauriManager()

// 设置Tauri的便捷函数
export function setupTauri() {
  // 这里可以添加初始化逻辑
  console.log('Tauri 初始化完成')
}

// 便捷的命令调用函数
export function useTauri() {
  return {
    invoke: tauriManager.invokeCommand.bind(tauriManager),
    listen: tauriManager.listenEvent.bind(tauriManager),
    notify: tauriManager.sendNotification.bind(tauriManager),
    showDialog: tauriManager.showDialog.bind(tauriManager),
    window: {
      minimize: tauriManager.minimizeWindow.bind(tauriManager),
      maximize: tauriManager.maximizeWindow.bind(tauriManager),
      close: tauriManager.closeWindow.bind(tauriManager),
      hide: tauriManager.hideWindow.bind(tauriManager),
      show: tauriManager.showWindow.bind(tauriManager),
      setTitle: tauriManager.setWindowTitle.bind(tauriManager),
      isMaximized: tauriManager.isWindowMaximized.bind(tauriManager),
      isMinimized: tauriManager.isWindowMinimized.bind(tauriManager),
      isVisible: tauriManager.isWindowVisible.bind(tauriManager)
    },
    app: {
      getVersion: tauriManager.getAppVersion.bind(tauriManager),
      checkUpdates: tauriManager.checkForUpdates.bind(tauriManager),
      restart: tauriManager.restartApp.bind(tauriManager),
      exit: tauriManager.exitApp.bind(tauriManager)
    }
  }
}
