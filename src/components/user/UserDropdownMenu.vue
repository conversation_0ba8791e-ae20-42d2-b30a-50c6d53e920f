<template>
  <div 
    v-if="visible"
    class="user-dropdown fixed inset-0 z-50"
    @click="handleBackdropClick"
  >
    <!-- 背景遮罩 -->
    <div class="absolute inset-0 bg-black/20 dark:bg-black/40" />
    
    <!-- 下拉菜单 -->
    <div 
      ref="dropdownRef"
      class="absolute bottom-16 left-4 w-64 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 py-2"
      :class="{ 'w-48': collapsed }"
    >
      <!-- 用户信息区域 -->
      <div class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
        <div class="flex items-center space-x-3">
          <!-- 用户头像 -->
          <div class="relative">
            <img
              v-if="userInfo?.avatar"
              :src="userInfo.avatar"
              :alt="userInfo.username"
              class="w-10 h-10 rounded-full object-cover"
            />
            <div
              v-else
              class="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center"
            >
              <span class="text-white font-semibold text-sm">
                {{ getUserInitials() }}
              </span>
            </div>
            <!-- 在线状态指示器 -->
            <div class="absolute -bottom-0.5 -right-0.5 w-3 h-3 bg-green-500 border-2 border-white dark:border-gray-800 rounded-full" />
          </div>
          
          <!-- 用户信息 -->
          <div v-if="!collapsed" class="flex-1 min-w-0">
            <div class="text-sm font-medium text-gray-900 dark:text-white truncate">
              {{ userInfo?.nickname || userInfo?.username || $t('user.guest') }}
            </div>
            <div class="text-xs text-gray-500 dark:text-gray-400 truncate">
              {{ userInfo?.email || $t('user.notLoggedIn') }}
            </div>
          </div>
        </div>
      </div>

      <!-- 菜单项 -->
      <div class="py-1">
        <!-- 用户信息 -->
        <button
          @click="handleUserProfile"
          class="menu-item w-full flex items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
        >
          <UserIcon class="w-4 h-4 mr-3" />
          <span v-if="!collapsed">{{ $t('user.profile') }}</span>
        </button>

        <!-- 设置 -->
        <button
          @click="handleSettings"
          class="menu-item w-full flex items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
        >
          <CogIcon class="w-4 h-4 mr-3" />
          <span v-if="!collapsed">{{ $t('settings.title') }}</span>
        </button>

        <!-- 分隔线 -->
        <div class="border-t border-gray-200 dark:border-gray-700 my-1" />

        <!-- 主题切换 -->
        <div class="px-4 py-2">
          <div v-if="!collapsed" class="text-xs font-medium text-gray-500 dark:text-gray-400 mb-2">
            {{ $t('settings.theme') }}
          </div>
          <div class="flex items-center space-x-1">
            <button
              v-for="theme in themeOptions"
              :key="theme.value"
              @click="handleThemeChange(theme.value)"
              class="theme-btn flex-1 p-2 rounded-md text-xs font-medium transition-colors"
              :class="[
                currentTheme === theme.value
                  ? 'bg-blue-100 text-blue-700 dark:bg-blue-900/50 dark:text-blue-400'
                  : 'text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700'
              ]"
              :title="collapsed ? theme.label : ''"
            >
              <component :is="theme.icon" class="w-4 h-4 mx-auto" />
              <span v-if="!collapsed" class="mt-1 block">{{ theme.label }}</span>
            </button>
          </div>
        </div>

        <!-- 分隔线 -->
        <div class="border-t border-gray-200 dark:border-gray-700 my-1" />

        <!-- 语言切换 -->
        <div class="px-4 py-2">
          <div v-if="!collapsed" class="text-xs font-medium text-gray-500 dark:text-gray-400 mb-2">
            {{ $t('settings.language') }}
          </div>
          <div class="flex items-center space-x-1">
            <button
              v-for="lang in languageOptions"
              :key="lang.code"
              @click="handleLanguageChange(lang.code)"
              class="language-btn flex-1 p-2 rounded-md text-xs font-medium transition-colors"
              :class="[
                currentLanguage === lang.code
                  ? 'bg-blue-100 text-blue-700 dark:bg-blue-900/50 dark:text-blue-400'
                  : 'text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700'
              ]"
              :title="collapsed ? lang.nativeName : ''"
            >
              <span class="text-sm">{{ lang.flag }}</span>
              <span v-if="!collapsed" class="mt-1 block">{{ lang.nativeName }}</span>
            </button>
          </div>
        </div>

        <!-- 分隔线 -->
        <div class="border-t border-gray-200 dark:border-gray-700 my-1" />

        <!-- 帮助 -->
        <button
          @click="handleHelp"
          class="menu-item w-full flex items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
        >
          <QuestionMarkCircleIcon class="w-4 h-4 mr-3" />
          <span v-if="!collapsed">{{ $t('common.help') }}</span>
        </button>

        <!-- 关于 -->
        <button
          @click="handleAbout"
          class="menu-item w-full flex items-center px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
        >
          <InformationCircleIcon class="w-4 h-4 mr-3" />
          <span v-if="!collapsed">{{ $t('common.about') }}</span>
        </button>

        <!-- 分隔线 -->
        <div class="border-t border-gray-200 dark:border-gray-700 my-1" />

        <!-- 退出登录 -->
        <button
          v-if="userInfo"
          @click="handleLogout"
          class="menu-item w-full flex items-center px-4 py-2 text-sm text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20"
        >
          <ArrowRightOnRectangleIcon class="w-4 h-4 mr-3" />
          <span v-if="!collapsed">{{ $t('user.logout') }}</span>
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { useUserStore } from '@/stores/user'
import { useThemeStore } from '@/stores/theme'
import { useLanguageStore } from '@/stores/language'
import {
  UserIcon,
  CogIcon,
  SunIcon,
  MoonIcon,
  ComputerDesktopIcon,
  QuestionMarkCircleIcon,
  InformationCircleIcon,
  ArrowRightOnRectangleIcon
} from '@heroicons/vue/24/outline'

// Props
interface Props {
  collapsed: boolean
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  close: []
}>()

const router = useRouter()
const { t, locale } = useI18n()
const userStore = useUserStore()
const themeStore = useThemeStore()
const languageStore = useLanguageStore()

// 状态
const visible = ref(true)
const dropdownRef = ref<HTMLElement>()

// 计算属性
const userInfo = computed(() => userStore.userInfo)
const currentTheme = computed(() => themeStore.mode)
const currentLanguage = computed(() => languageStore.language)

// 主题选项
const themeOptions = [
  { value: 'light', label: t('settings.lightTheme'), icon: SunIcon },
  { value: 'dark', label: t('settings.darkTheme'), icon: MoonIcon },
  { value: 'auto', label: t('settings.autoTheme'), icon: ComputerDesktopIcon }
]

// 语言选项
const languageOptions = [
  { code: 'zh-CN', name: 'Chinese', nativeName: '中文', flag: '🇨🇳' },
  { code: 'en-US', name: 'English', nativeName: 'English', flag: '🇺🇸' }
]

// 方法
const getUserInitials = () => {
  if (userInfo.value?.nickname) {
    return userInfo.value.nickname.charAt(0).toUpperCase()
  }
  if (userInfo.value?.username) {
    return userInfo.value.username.charAt(0).toUpperCase()
  }
  return 'U'
}

const handleBackdropClick = (event: MouseEvent) => {
  if (event.target === event.currentTarget) {
    emit('close')
  }
}

const handleUserProfile = () => {
  router.push('/settings?tab=profile')
  emit('close')
}

const handleSettings = () => {
  router.push('/settings')
  emit('close')
}

const handleThemeChange = (theme: string) => {
  themeStore.setMode(theme as any)
}

const handleLanguageChange = (language: string) => {
  languageStore.setLanguage(language as any)
  locale.value = language
}

const handleHelp = () => {
  // 打开帮助文档
  window.open('https://docs.ai-studio.com', '_blank')
  emit('close')
}

const handleAbout = () => {
  router.push('/settings?tab=about')
  emit('close')
}

const handleLogout = async () => {
  try {
    await userStore.logout()
    emit('close')
  } catch (error) {
    console.error('退出登录失败:', error)
  }
}

// 键盘事件处理
const handleKeydown = (event: KeyboardEvent) => {
  if (event.key === 'Escape') {
    emit('close')
  }
}

onMounted(() => {
  document.addEventListener('keydown', handleKeydown)
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown)
})
</script>

<style scoped>
.user-dropdown {
  animation: fadeIn 0.2s ease-out;
}

.user-dropdown > div:last-child {
  animation: slideUp 0.2s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.menu-item {
  transition: all 0.15s ease;
}

.theme-btn,
.language-btn {
  min-height: 2.5rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .user-dropdown > div:last-child {
    left: 1rem;
    right: 1rem;
    width: auto;
  }
}
</style>
