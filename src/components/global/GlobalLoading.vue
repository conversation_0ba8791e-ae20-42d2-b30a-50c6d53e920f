<template>
  <div class="global-loading">
    <!-- 背景遮罩 -->
    <div class="loading-backdrop" />
    
    <!-- 加载内容 -->
    <div class="loading-content">
      <!-- Logo -->
      <div class="loading-logo">
        <div class="logo-container">
          <div class="logo-icon">
            <svg viewBox="0 0 100 100" class="w-16 h-16">
              <defs>
                <linearGradient id="logoGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                  <stop offset="0%" style="stop-color:#3b82f6;stop-opacity:1" />
                  <stop offset="100%" style="stop-color:#8b5cf6;stop-opacity:1" />
                </linearGradient>
              </defs>
              <circle cx="50" cy="50" r="45" fill="url(#logoGradient)" class="logo-circle" />
              <text x="50" y="60" text-anchor="middle" fill="white" font-size="32" font-weight="bold" font-family="Inter">
                AI
              </text>
            </svg>
          </div>
          <h1 class="logo-text">AI Studio</h1>
        </div>
      </div>
      
      <!-- 加载动画 -->
      <div class="loading-animation">
        <div class="loading-spinner">
          <div class="spinner-ring"></div>
          <div class="spinner-ring"></div>
          <div class="spinner-ring"></div>
        </div>
      </div>
      
      <!-- 加载文本 -->
      <div class="loading-text">
        <p class="loading-message">{{ currentMessage }}</p>
        <p class="loading-subtitle">{{ subtitle }}</p>
      </div>
      
      <!-- 进度条 -->
      <div class="loading-progress">
        <div class="progress-bar">
          <div 
            class="progress-fill" 
            :style="{ width: `${progress}%` }"
          />
        </div>
        <div class="progress-text">{{ Math.round(progress) }}%</div>
      </div>
      
      <!-- 加载步骤 -->
      <div class="loading-steps">
        <div 
          v-for="(step, index) in steps" 
          :key="index"
          class="step-item"
          :class="{
            'step-completed': index < currentStep,
            'step-active': index === currentStep,
            'step-pending': index > currentStep
          }"
        >
          <div class="step-icon">
            <CheckIcon v-if="index < currentStep" class="w-4 h-4" />
            <div v-else-if="index === currentStep" class="step-spinner" />
            <div v-else class="step-dot" />
          </div>
          <span class="step-text">{{ step }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { CheckIcon } from '@heroicons/vue/24/outline'

// 状态
const progress = ref(0)
const currentStep = ref(0)
const currentMessage = ref('正在启动 AI Studio...')
const subtitle = ref('请稍候，我们正在为您准备最佳体验')

// 加载步骤
const steps = [
  '初始化应用',
  '加载配置',
  '连接服务',
  '准备界面',
  '启动完成'
]

// 加载消息
const messages = [
  '正在启动 AI Studio...',
  '正在加载核心模块...',
  '正在初始化用户界面...',
  '正在连接后端服务...',
  '正在准备工作环境...',
  '即将完成启动...'
]

let progressInterval: number
let messageInterval: number

// 模拟加载进度
const simulateProgress = () => {
  progressInterval = window.setInterval(() => {
    if (progress.value < 95) {
      // 随机增加进度，但确保不会超过95%
      const increment = Math.random() * 15 + 5
      progress.value = Math.min(progress.value + increment, 95)
      
      // 更新当前步骤
      const newStep = Math.floor((progress.value / 100) * steps.length)
      if (newStep !== currentStep.value && newStep < steps.length) {
        currentStep.value = newStep
      }
    }
  }, 300)
}

// 模拟消息变化
const simulateMessages = () => {
  let messageIndex = 0
  
  messageInterval = window.setInterval(() => {
    messageIndex = (messageIndex + 1) % messages.length
    currentMessage.value = messages[messageIndex]
  }, 2000)
}

// 完成加载
const completeLoading = () => {
  progress.value = 100
  currentStep.value = steps.length - 1
  currentMessage.value = '启动完成！'
  subtitle.value = '欢迎使用 AI Studio'
}

// 生命周期
onMounted(() => {
  simulateProgress()
  simulateMessages()
  
  // 监听实际的加载完成事件
  window.addEventListener('app-ready', completeLoading)
})

onUnmounted(() => {
  if (progressInterval) {
    clearInterval(progressInterval)
  }
  if (messageInterval) {
    clearInterval(messageInterval)
  }
  
  window.removeEventListener('app-ready', completeLoading)
})
</script>

<style scoped>
.global-loading {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading-backdrop {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  opacity: 0.95;
}

.loading-content {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  color: white;
  z-index: 1;
}

.loading-logo {
  margin-bottom: 2rem;
}

.logo-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.logo-icon {
  position: relative;
}

.logo-circle {
  animation: logoFloat 3s ease-in-out infinite;
}

.logo-text {
  font-size: 2rem;
  font-weight: 700;
  letter-spacing: 0.1em;
  animation: textGlow 2s ease-in-out infinite alternate;
}

.loading-animation {
  margin-bottom: 2rem;
}

.loading-spinner {
  position: relative;
  width: 60px;
  height: 60px;
}

.spinner-ring {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: 3px solid transparent;
  border-top-color: rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  animation: spin 1.5s linear infinite;
}

.spinner-ring:nth-child(2) {
  width: 80%;
  height: 80%;
  top: 10%;
  left: 10%;
  border-top-color: rgba(255, 255, 255, 0.6);
  animation-duration: 2s;
  animation-direction: reverse;
}

.spinner-ring:nth-child(3) {
  width: 60%;
  height: 60%;
  top: 20%;
  left: 20%;
  border-top-color: rgba(255, 255, 255, 0.4);
  animation-duration: 2.5s;
}

.loading-text {
  margin-bottom: 2rem;
}

.loading-message {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  animation: messageSlide 0.5s ease-out;
}

.loading-subtitle {
  font-size: 0.875rem;
  opacity: 0.8;
}

.loading-progress {
  width: 300px;
  margin-bottom: 2rem;
}

.progress-bar {
  width: 100%;
  height: 4px;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 2px;
  overflow: hidden;
  margin-bottom: 0.5rem;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #3b82f6, #8b5cf6);
  border-radius: 2px;
  transition: width 0.3s ease;
  animation: progressGlow 2s ease-in-out infinite alternate;
}

.progress-text {
  font-size: 0.75rem;
  opacity: 0.8;
  text-align: center;
}

.loading-steps {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  min-width: 200px;
}

.step-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 0.875rem;
  transition: all 0.3s ease;
}

.step-icon {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  flex-shrink: 0;
}

.step-completed .step-icon {
  background-color: rgba(34, 197, 94, 0.8);
  color: white;
}

.step-active .step-icon {
  background-color: rgba(59, 130, 246, 0.8);
}

.step-pending .step-icon {
  background-color: rgba(255, 255, 255, 0.2);
}

.step-dot {
  width: 8px;
  height: 8px;
  background-color: rgba(255, 255, 255, 0.5);
  border-radius: 50%;
}

.step-spinner {
  width: 12px;
  height: 12px;
  border: 2px solid transparent;
  border-top-color: white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.step-text {
  transition: opacity 0.3s ease;
}

.step-completed .step-text {
  opacity: 0.8;
}

.step-active .step-text {
  opacity: 1;
  font-weight: 500;
}

.step-pending .step-text {
  opacity: 0.5;
}

/* 动画定义 */
@keyframes logoFloat {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes textGlow {
  0% {
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
  }
  100% {
    text-shadow: 0 0 20px rgba(255, 255, 255, 0.8), 0 0 30px rgba(59, 130, 246, 0.5);
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes messageSlide {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes progressGlow {
  0% {
    box-shadow: 0 0 5px rgba(59, 130, 246, 0.5);
  }
  100% {
    box-shadow: 0 0 15px rgba(59, 130, 246, 0.8);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .loading-content {
    padding: 1rem;
  }
  
  .logo-text {
    font-size: 1.5rem;
  }
  
  .loading-message {
    font-size: 1rem;
  }
  
  .loading-progress {
    width: 250px;
  }
  
  .loading-steps {
    min-width: 180px;
  }
}
</style>
