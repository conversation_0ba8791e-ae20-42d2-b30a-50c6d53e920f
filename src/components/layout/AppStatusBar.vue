<template>
  <div class="status-bar bg-gray-50 dark:bg-gray-900 border-t border-gray-200 dark:border-gray-700 px-4 py-2">
    <div class="flex items-center justify-between text-xs text-gray-600 dark:text-gray-400">
      <!-- 左侧：应用状态信息 -->
      <div class="flex items-center space-x-4">
        <!-- 当前模型 -->
        <div v-if="currentModel" class="flex items-center space-x-1">
          <CubeIcon class="w-3 h-3" />
          <span>{{ $t('model.current') }}: {{ currentModel }}</span>
        </div>

        <!-- 知识库状态 -->
        <div v-if="knowledgeBaseCount > 0" class="flex items-center space-x-1">
          <BookOpenIcon class="w-3 h-3" />
          <span>{{ $t('knowledge.title') }}: {{ knowledgeBaseCount }}</span>
        </div>

        <!-- 网络节点数量 -->
        <div v-if="networkNodeCount > 0" class="flex items-center space-x-1">
          <ShareIcon class="w-3 h-3" />
          <span>{{ $t('network.nodes') }}: {{ networkNodeCount }}</span>
        </div>

        <!-- 处理任务数量 -->
        <div v-if="processingTaskCount > 0" class="flex items-center space-x-1">
          <ClockIcon class="w-3 h-3" />
          <span>{{ $t('multimodal.processing') }}: {{ processingTaskCount }}</span>
        </div>
      </div>

      <!-- 右侧：系统信息 -->
      <div class="flex items-center space-x-4">
        <!-- 内存使用情况 -->
        <div v-if="memoryUsage" class="flex items-center space-x-1">
          <span>{{ $t('system.memory') }}:</span>
          <div class="w-16 h-2 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden">
            <div
              class="h-full transition-all duration-300"
              :class="[
                memoryUsage.percentage > 80 ? 'bg-red-500' :
                memoryUsage.percentage > 60 ? 'bg-yellow-500' : 'bg-green-500'
              ]"
              :style="{ width: `${memoryUsage.percentage}%` }"
            />
          </div>
          <span>{{ memoryUsage.used }}/{{ memoryUsage.total }}</span>
        </div>

        <!-- CPU使用情况 -->
        <div v-if="cpuUsage !== null" class="flex items-center space-x-1">
          <CpuChipIcon class="w-3 h-3" />
          <span>CPU: {{ cpuUsage }}%</span>
        </div>

        <!-- GPU使用情况 -->
        <div v-if="gpuUsage !== null" class="flex items-center space-x-1">
          <span>GPU: {{ gpuUsage }}%</span>
        </div>

        <!-- 网络状态 -->
        <div class="flex items-center space-x-1">
          <div
            class="w-2 h-2 rounded-full"
            :class="[
              isOnline ? 'bg-green-500' : 'bg-red-500'
            ]"
          />
          <span>{{ isOnline ? $t('common.online') : $t('common.offline') }}</span>
        </div>

        <!-- 应用版本 -->
        <div class="flex items-center space-x-1">
          <span>v{{ appVersion }}</span>
        </div>

        <!-- 当前时间 -->
        <div class="flex items-center space-x-1">
          <ClockIcon class="w-3 h-3" />
          <span>{{ currentTime }}</span>
        </div>
      </div>
    </div>

    <!-- 进度条（用于显示后台任务进度） -->
    <div v-if="showProgress" class="mt-2">
      <div class="flex items-center justify-between text-xs mb-1">
        <span class="text-gray-600 dark:text-gray-400">{{ progressText }}</span>
        <span class="text-gray-600 dark:text-gray-400">{{ Math.round(progressValue) }}%</span>
      </div>
      <div class="w-full h-1 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden">
        <div
          class="h-full bg-blue-500 transition-all duration-300 ease-out"
          :style="{ width: `${progressValue}%` }"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useI18n } from 'vue-i18n'
import { useSystemStore } from '@/stores/system'
import { useModelStore } from '@/stores/model'
import { useKnowledgeStore } from '@/stores/knowledge'
import { useNetworkStore } from '@/stores/network'
import { useMultimodalStore } from '@/stores/multimodal'
import {
  CubeIcon,
  BookOpenIcon,
  ShareIcon,
  ClockIcon,
  CpuChipIcon
} from '@heroicons/vue/24/outline'

const { t } = useI18n()
const systemStore = useSystemStore()
const modelStore = useModelStore()
const knowledgeStore = useKnowledgeStore()
const networkStore = useNetworkStore()
const multimodalStore = useMultimodalStore()

// 状态
const currentTime = ref('')
const updateTimeInterval = ref<number>()

// 计算属性
const isOnline = computed(() => systemStore.isOnline)
const appVersion = computed(() => '1.0.0') // 从配置或环境变量获取

const currentModel = computed(() => {
  return modelStore.currentModel || ''
})

const knowledgeBaseCount = computed(() => {
  return knowledgeStore.knowledgeBases.length
})

const networkNodeCount = computed(() => {
  return networkStore.nodes.filter(node => node.status === 'online').length
})

const processingTaskCount = computed(() => {
  return multimodalStore.processingQueue.filter(task => 
    task.status === 'processing'
  ).length
})

const memoryUsage = computed(() => {
  const info = systemStore.systemInfo
  if (!info) return null

  const used = info.totalMemory - info.availableMemory
  const percentage = (used / info.totalMemory) * 100

  return {
    used: formatBytes(used),
    total: formatBytes(info.totalMemory),
    percentage: Math.round(percentage)
  }
})

const cpuUsage = computed(() => {
  const info = systemStore.systemInfo
  return info ? Math.round(info.cpuUsage || 0) : null
})

const gpuUsage = computed(() => {
  const info = systemStore.systemInfo
  if (!info?.gpuInfo || info.gpuInfo.length === 0) return null
  
  // 返回第一个GPU的使用率
  return Math.round(info.gpuInfo[0].utilization || 0)
})

// 进度相关
const showProgress = computed(() => {
  // 检查是否有正在进行的任务
  const hasDownloading = modelStore.downloadTasks.some(task => 
    task.status === 'downloading'
  )
  const hasUploading = knowledgeStore.uploadTasks.some(task => 
    task.status === 'uploading' || task.status === 'processing'
  )
  const hasTransferring = networkStore.transfers.some(task => 
    task.status === 'transferring'
  )
  const hasProcessing = multimodalStore.processingQueue.some(task => 
    task.status === 'processing'
  )

  return hasDownloading || hasUploading || hasTransferring || hasProcessing
})

const progressText = computed(() => {
  // 获取当前进行中的任务描述
  const downloadingTask = modelStore.downloadTasks.find(task => 
    task.status === 'downloading'
  )
  if (downloadingTask) {
    return t('model.downloading') + ': ' + downloadingTask.modelId
  }

  const uploadingTask = knowledgeStore.uploadTasks.find(task => 
    task.status === 'uploading' || task.status === 'processing'
  )
  if (uploadingTask) {
    return t('knowledge.uploading') + ': ' + uploadingTask.files[0]?.name
  }

  const transferringTask = networkStore.transfers.find(task => 
    task.status === 'transferring'
  )
  if (transferringTask) {
    return t('network.transferring') + ': ' + transferringTask.fileName
  }

  const processingTask = multimodalStore.processingQueue.find(task => 
    task.status === 'processing'
  )
  if (processingTask) {
    return t('multimodal.processing') + ': ' + processingTask.type
  }

  return ''
})

const progressValue = computed(() => {
  // 获取当前进行中的任务进度
  const downloadingTask = modelStore.downloadTasks.find(task => 
    task.status === 'downloading'
  )
  if (downloadingTask) {
    return downloadingTask.progress
  }

  const uploadingTask = knowledgeStore.uploadTasks.find(task => 
    task.status === 'uploading' || task.status === 'processing'
  )
  if (uploadingTask) {
    return uploadingTask.progress
  }

  const transferringTask = networkStore.transfers.find(task => 
    task.status === 'transferring'
  )
  if (transferringTask) {
    return transferringTask.progress
  }

  const processingTask = multimodalStore.processingQueue.find(task => 
    task.status === 'processing'
  )
  if (processingTask) {
    return processingTask.progress
  }

  return 0
})

// 方法
const updateTime = () => {
  const now = new Date()
  currentTime.value = now.toLocaleTimeString('zh-CN', {
    hour12: false,
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

const formatBytes = (bytes: number): string => {
  if (bytes === 0) return '0 B'
  
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
}

// 生命周期
onMounted(() => {
  updateTime()
  updateTimeInterval.value = window.setInterval(updateTime, 1000)
})

onUnmounted(() => {
  if (updateTimeInterval.value) {
    clearInterval(updateTimeInterval.value)
  }
})
</script>

<style scoped>
.status-bar {
  /* 确保状态栏始终在底部 */
  flex-shrink: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .status-bar {
    padding: 0.5rem 1rem;
  }
  
  .status-bar .flex.space-x-4 {
    gap: 0.5rem;
  }
  
  /* 在小屏幕上隐藏一些不重要的信息 */
  .status-bar .flex.space-x-4 > div:nth-child(n+3) {
    display: none;
  }
}

@media (max-width: 480px) {
  .status-bar .flex.space-x-4 > div:nth-child(n+2) {
    display: none;
  }
}
</style>
