<template>
  <aside 
    class="sidebar transition-all duration-300 ease-in-out border-r border-gray-200 dark:border-gray-700"
    :class="[
      collapsed ? 'w-16' : 'w-64',
      'bg-white dark:bg-gray-800'
    ]"
  >
    <!-- 侧边栏头部 -->
    <div class="sidebar-header h-14 flex items-center justify-between px-4 border-b border-gray-200 dark:border-gray-700">
      <!-- Logo 和标题 -->
      <div class="flex items-center space-x-3">
        <div class="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
          <span class="text-white font-bold text-sm">AI</span>
        </div>
        <span 
          v-show="!collapsed" 
          class="font-semibold text-gray-900 dark:text-white transition-opacity duration-200"
        >
          AI Studio
        </span>
      </div>
      
      <!-- 折叠按钮 -->
      <button
        @click="$emit('toggle')"
        class="p-1.5 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
        :title="collapsed ? $t('common.expand') : $t('common.collapse')"
      >
        <ChevronLeftIcon 
          class="w-4 h-4 text-gray-500 dark:text-gray-400 transition-transform duration-200"
          :class="{ 'rotate-180': collapsed }"
        />
      </button>
    </div>

    <!-- 导航菜单 -->
    <nav class="sidebar-nav flex-1 py-4">
      <ul class="space-y-1 px-3">
        <li v-for="item in navigationItems" :key="item.name">
          <router-link
            :to="item.path"
            class="nav-item group flex items-center px-3 py-2.5 text-sm font-medium rounded-lg transition-all duration-200"
            :class="[
              isActiveRoute(item.name) 
                ? 'bg-blue-50 text-blue-700 dark:bg-blue-900/50 dark:text-blue-400' 
                : 'text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700'
            ]"
            :title="collapsed ? item.title : ''"
          >
            <!-- 图标 -->
            <component 
              :is="getIcon(item.icon)"
              class="w-5 h-5 flex-shrink-0"
              :class="[
                isActiveRoute(item.name) 
                  ? 'text-blue-600 dark:text-blue-400' 
                  : 'text-gray-500 group-hover:text-gray-700 dark:text-gray-400 dark:group-hover:text-gray-300'
              ]"
            />
            
            <!-- 标题 -->
            <span 
              v-show="!collapsed"
              class="ml-3 transition-opacity duration-200"
            >
              {{ item.title }}
            </span>
            
            <!-- 活动指示器 -->
            <div 
              v-if="isActiveRoute(item.name)"
              class="ml-auto w-2 h-2 bg-blue-600 dark:bg-blue-400 rounded-full"
              :class="{ 'hidden': collapsed }"
            />
          </router-link>
        </li>
      </ul>
    </nav>

    <!-- 侧边栏底部 -->
    <div class="sidebar-footer border-t border-gray-200 dark:border-gray-700 p-3">
      <!-- 用户信息/设置按钮 -->
      <div class="flex items-center justify-between">
        <!-- 用户头像 -->
        <button
          @click="showUserMenu = !showUserMenu"
          class="flex items-center space-x-3 p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors w-full"
          :title="collapsed ? $t('user.profile') : ''"
        >
          <div class="w-8 h-8 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center">
            <UserIcon class="w-4 h-4 text-gray-600 dark:text-gray-400" />
          </div>
          <div v-show="!collapsed" class="flex-1 text-left">
            <div class="text-sm font-medium text-gray-900 dark:text-white">
              {{ userInfo?.nickname || userInfo?.username || $t('user.guest') }}
            </div>
            <div class="text-xs text-gray-500 dark:text-gray-400">
              {{ userInfo?.email || $t('user.notLoggedIn') }}
            </div>
          </div>
        </button>
      </div>
    </div>

    <!-- 用户下拉菜单 -->
    <UserDropdownMenu 
      v-if="showUserMenu"
      :collapsed="collapsed"
      @close="showUserMenu = false"
    />
  </aside>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRoute } from 'vue-router'
import { getNavigationItems } from '@/router'
import { useUserStore } from '@/stores/user'
import { 
  ChevronLeftIcon,
  UserIcon,
  ChatBubbleLeftIcon,
  BookOpenIcon,
  CubeIcon,
  CloudIcon,
  ShareIcon,
  PuzzlePieceIcon
} from '@heroicons/vue/24/outline'
import UserDropdownMenu from '@/components/user/UserDropdownMenu.vue'

// Props
interface Props {
  collapsed: boolean
}

defineProps<Props>()

// Emits
defineEmits<{
  toggle: []
}>()

const route = useRoute()
const userStore = useUserStore()

// 状态
const showUserMenu = ref(false)

// 计算属性
const navigationItems = computed(() => getNavigationItems())
const userInfo = computed(() => userStore.userInfo)

// 图标映射
const iconMap = {
  'ChatBubbleOutline': ChatBubbleLeftIcon,
  'LibraryOutline': BookOpenIcon,
  'CubeOutline': CubeIcon,
  'CloudOutline': CloudIcon,
  'ShareSocialOutline': ShareIcon,
  'ExtensionPuzzleOutline': PuzzlePieceIcon
}

// 方法
const getIcon = (iconName: string) => {
  return iconMap[iconName as keyof typeof iconMap] || ChatBubbleLeftIcon
}

const isActiveRoute = (routeName: string) => {
  return route.name === routeName
}
</script>

<style scoped>
.sidebar {
  /* 确保侧边栏高度正确 */
  height: calc(100vh - 2rem); /* 减去标题栏高度 */
  display: flex;
  flex-direction: column;
}

.sidebar-nav {
  /* 导航区域可滚动 */
  overflow-y: auto;
  overflow-x: hidden;
}

.nav-item {
  /* 导航项样式 */
  position: relative;
}

.nav-item.router-link-active {
  /* 活动状态样式 */
  @apply bg-blue-50 text-blue-700 dark:bg-blue-900/50 dark:text-blue-400;
}

.nav-item.router-link-active::before {
  /* 活动指示器 */
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 3px;
  background-color: currentColor;
  border-radius: 0 2px 2px 0;
}

/* 滚动条样式 */
.sidebar-nav::-webkit-scrollbar {
  width: 4px;
}

.sidebar-nav::-webkit-scrollbar-track {
  background: transparent;
}

.sidebar-nav::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 2px;
}

.dark .sidebar-nav::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.1);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .sidebar {
    /* 移动端隐藏侧边栏或使用抽屉模式 */
    position: fixed;
    left: 0;
    top: 0;
    z-index: 50;
    height: 100vh;
  }
}

/* 动画效果 */
.sidebar * {
  transition: all 0.2s ease;
}
</style>
