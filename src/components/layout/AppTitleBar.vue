<template>
  <div class="title-bar drag-region flex items-center justify-between h-8 bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 px-4">
    <!-- 左侧：应用图标和标题 -->
    <div class="flex items-center space-x-2">
      <img 
        src="/favicon.ico" 
        alt="AI Studio" 
        class="w-4 h-4"
      />
      <span class="text-sm font-medium text-gray-700 dark:text-gray-300">
        AI Studio
      </span>
      <span 
        v-if="currentPageTitle" 
        class="text-xs text-gray-500 dark:text-gray-400"
      >
        - {{ currentPageTitle }}
      </span>
    </div>

    <!-- 右侧：窗口控制按钮 -->
    <div class="flex items-center space-x-1 no-drag">
      <!-- 最小化按钮 -->
      <button
        @click="minimizeWindow"
        class="window-control-btn hover:bg-gray-100 dark:hover:bg-gray-700"
        :title="$t('common.minimize')"
      >
        <MinimizeIcon class="w-3 h-3" />
      </button>

      <!-- 最大化/还原按钮 -->
      <button
        @click="toggleMaximize"
        class="window-control-btn hover:bg-gray-100 dark:hover:bg-gray-700"
        :title="isMaximized ? $t('common.restore') : $t('common.maximize')"
      >
        <MaximizeIcon v-if="!isMaximized" class="w-3 h-3" />
        <RestoreIcon v-else class="w-3 h-3" />
      </button>

      <!-- 关闭按钮 -->
      <button
        @click="closeWindow"
        class="window-control-btn hover:bg-red-500 hover:text-white"
        :title="$t('common.close')"
      >
        <CloseIcon class="w-3 h-3" />
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRoute } from 'vue-router'
import { useTauri } from '@/core/tauri'
import { 
  MinusIcon as MinimizeIcon,
  SquareIcon as MaximizeIcon,
  XMarkIcon as CloseIcon,
  Square2StackIcon as RestoreIcon
} from '@heroicons/vue/24/outline'

const route = useRoute()
const { window: windowAPI } = useTauri()

// 窗口状态
const isMaximized = ref(false)

// 当前页面标题
const currentPageTitle = computed(() => {
  return route.meta?.title as string || ''
})

// 窗口操作
const minimizeWindow = async () => {
  try {
    await windowAPI.minimize()
  } catch (error) {
    console.error('最小化窗口失败:', error)
  }
}

const toggleMaximize = async () => {
  try {
    await windowAPI.maximize()
    // 切换状态
    isMaximized.value = !isMaximized.value
  } catch (error) {
    console.error('切换窗口最大化状态失败:', error)
  }
}

const closeWindow = async () => {
  try {
    // 可以在这里添加关闭前的确认逻辑
    const shouldClose = await confirmClose()
    if (shouldClose) {
      await windowAPI.close()
    }
  } catch (error) {
    console.error('关闭窗口失败:', error)
  }
}

// 关闭确认
const confirmClose = async (): Promise<boolean> => {
  // 这里可以检查是否有未保存的数据
  // 如果有，显示确认对话框
  return true
}

// 检查窗口状态
const checkWindowState = async () => {
  try {
    isMaximized.value = await windowAPI.isMaximized()
  } catch (error) {
    console.error('检查窗口状态失败:', error)
  }
}

// 双击标题栏切换最大化
const handleDoubleClick = () => {
  toggleMaximize()
}

onMounted(() => {
  checkWindowState()
  
  // 监听窗口状态变化
  const titleBarElement = document.querySelector('.title-bar')
  if (titleBarElement) {
    titleBarElement.addEventListener('dblclick', handleDoubleClick)
  }
})

onUnmounted(() => {
  const titleBarElement = document.querySelector('.title-bar')
  if (titleBarElement) {
    titleBarElement.removeEventListener('dblclick', handleDoubleClick)
  }
})
</script>

<style scoped>
.title-bar {
  /* 确保标题栏可以拖拽窗口 */
  -webkit-app-region: drag;
  user-select: none;
}

.window-control-btn {
  @apply w-8 h-6 flex items-center justify-center rounded text-gray-600 dark:text-gray-400 transition-colors duration-200;
  -webkit-app-region: no-drag;
}

.window-control-btn:hover {
  @apply text-gray-800 dark:text-gray-200;
}

/* 关闭按钮特殊样式 */
.window-control-btn:last-child:hover {
  @apply bg-red-500 text-white;
}

/* macOS 样式适配 */
@media (platform: macos) {
  .title-bar {
    /* macOS 特定样式 */
    padding-left: 80px; /* 为 macOS 的红绿黄按钮留出空间 */
  }
}

/* Windows 样式适配 */
@media (platform: windows) {
  .title-bar {
    /* Windows 特定样式 */
  }
}
</style>
