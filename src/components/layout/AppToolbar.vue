<template>
  <div class="toolbar bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 px-4 py-3">
    <div class="flex items-center justify-between">
      <!-- 左侧：页面标题和面包屑 -->
      <div class="flex items-center space-x-4">
        <!-- 页面标题 -->
        <h1 class="text-lg font-semibold text-gray-900 dark:text-white">
          {{ currentPageTitle }}
        </h1>
        
        <!-- 面包屑导航 -->
        <nav v-if="breadcrumbs.length > 1" class="flex items-center space-x-2 text-sm">
          <template v-for="(item, index) in breadcrumbs" :key="item.path">
            <ChevronRightIcon 
              v-if="index > 0" 
              class="w-4 h-4 text-gray-400 dark:text-gray-500" 
            />
            <router-link
              v-if="index < breadcrumbs.length - 1"
              :to="item.path"
              class="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 transition-colors"
            >
              {{ item.title }}
            </router-link>
            <span
              v-else
              class="text-gray-900 dark:text-white font-medium"
            >
              {{ item.title }}
            </span>
          </template>
        </nav>
      </div>

      <!-- 右侧：工具按钮 -->
      <div class="flex items-center space-x-2">
        <!-- 搜索按钮 -->
        <button
          @click="toggleSearch"
          class="toolbar-btn"
          :title="$t('common.search')"
        >
          <MagnifyingGlassIcon class="w-5 h-5" />
        </button>

        <!-- 通知按钮 -->
        <button
          @click="toggleNotifications"
          class="toolbar-btn relative"
          :title="$t('settings.notifications')"
        >
          <BellIcon class="w-5 h-5" />
          <!-- 未读通知徽章 -->
          <span
            v-if="unreadNotificationCount > 0"
            class="absolute -top-1 -right-1 w-4 h-4 bg-red-500 text-white text-xs rounded-full flex items-center justify-center"
          >
            {{ unreadNotificationCount > 9 ? '9+' : unreadNotificationCount }}
          </span>
        </button>

        <!-- 系统状态指示器 -->
        <div class="flex items-center space-x-2">
          <!-- 网络状态 -->
          <div
            class="flex items-center space-x-1 px-2 py-1 rounded-md text-xs"
            :class="[
              isOnline 
                ? 'bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400' 
                : 'bg-red-100 text-red-700 dark:bg-red-900/30 dark:text-red-400'
            ]"
          >
            <div
              class="w-2 h-2 rounded-full"
              :class="[
                isOnline ? 'bg-green-500' : 'bg-red-500'
              ]"
            />
            <span>{{ isOnline ? $t('common.online') : $t('common.offline') }}</span>
          </div>

          <!-- 系统性能指示器 -->
          <div
            v-if="systemPerformance"
            class="flex items-center space-x-1 px-2 py-1 rounded-md text-xs bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300"
            :title="`CPU: ${systemPerformance.cpu}% | 内存: ${systemPerformance.memory}%`"
          >
            <CpuChipIcon class="w-3 h-3" />
            <span>{{ systemPerformance.cpu }}%</span>
            <span class="text-gray-400">|</span>
            <span>{{ systemPerformance.memory }}%</span>
          </div>
        </div>

        <!-- 更多操作按钮 -->
        <button
          @click="toggleMoreMenu"
          class="toolbar-btn"
          :title="$t('common.more')"
        >
          <EllipsisVerticalIcon class="w-5 h-5" />
        </button>
      </div>
    </div>

    <!-- 搜索栏 -->
    <div
      v-if="showSearch"
      class="mt-3 transition-all duration-200 ease-in-out"
    >
      <div class="relative">
        <MagnifyingGlassIcon class="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
        <input
          ref="searchInput"
          v-model="searchQuery"
          type="text"
          :placeholder="getSearchPlaceholder()"
          class="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          @keydown.enter="handleSearch"
          @keydown.escape="toggleSearch"
        />
        <button
          v-if="searchQuery"
          @click="clearSearch"
          class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
        >
          <XMarkIcon class="w-4 h-4" />
        </button>
      </div>
    </div>
  </div>

  <!-- 通知面板 -->
  <NotificationPanel
    v-if="showNotifications"
    @close="toggleNotifications"
  />

  <!-- 更多菜单 -->
  <MoreMenu
    v-if="showMoreMenu"
    @close="toggleMoreMenu"
  />
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { useSystemStore } from '@/stores/system'
import {
  ChevronRightIcon,
  MagnifyingGlassIcon,
  BellIcon,
  CpuChipIcon,
  EllipsisVerticalIcon,
  XMarkIcon
} from '@heroicons/vue/24/outline'
import NotificationPanel from '@/components/notification/NotificationPanel.vue'
import MoreMenu from '@/components/layout/MoreMenu.vue'

const route = useRoute()
const router = useRouter()
const { t } = useI18n()
const systemStore = useSystemStore()

// 状态
const showSearch = ref(false)
const showNotifications = ref(false)
const showMoreMenu = ref(false)
const searchQuery = ref('')
const searchInput = ref<HTMLInputElement>()

// 计算属性
const currentPageTitle = computed(() => {
  return route.meta?.title as string || t('app.name')
})

const breadcrumbs = computed(() => {
  const crumbs = []
  
  // 添加首页
  crumbs.push({
    title: t('app.name'),
    path: '/'
  })
  
  // 添加当前页面
  if (route.meta?.title) {
    crumbs.push({
      title: route.meta.title as string,
      path: route.path
    })
  }
  
  return crumbs
})

const isOnline = computed(() => systemStore.isOnline)
const unreadNotificationCount = computed(() => systemStore.notificationCount)

const systemPerformance = computed(() => {
  const info = systemStore.systemInfo
  if (!info) return null
  
  return {
    cpu: Math.round(info.cpuUsage || 0),
    memory: Math.round(((info.totalMemory - info.availableMemory) / info.totalMemory) * 100)
  }
})

// 方法
const toggleSearch = async () => {
  showSearch.value = !showSearch.value
  
  if (showSearch.value) {
    await nextTick()
    searchInput.value?.focus()
  } else {
    searchQuery.value = ''
  }
}

const toggleNotifications = () => {
  showNotifications.value = !showNotifications.value
  // 关闭其他面板
  showMoreMenu.value = false
}

const toggleMoreMenu = () => {
  showMoreMenu.value = !showMoreMenu.value
  // 关闭其他面板
  showNotifications.value = false
}

const getSearchPlaceholder = () => {
  const routeName = route.name as string
  
  switch (routeName) {
    case 'Chat':
      return t('chat.searchMessages')
    case 'Knowledge':
      return t('knowledge.search')
    case 'Model':
      return t('model.search')
    default:
      return t('common.search')
  }
}

const handleSearch = () => {
  if (!searchQuery.value.trim()) return
  
  const routeName = route.name as string
  
  // 根据当前页面执行不同的搜索逻辑
  switch (routeName) {
    case 'Chat':
      // 搜索聊天消息
      router.push({
        name: 'Chat',
        query: { search: searchQuery.value }
      })
      break
    case 'Knowledge':
      // 搜索知识库
      router.push({
        name: 'Knowledge',
        query: { search: searchQuery.value }
      })
      break
    case 'Model':
      // 搜索模型
      router.push({
        name: 'Model',
        query: { search: searchQuery.value }
      })
      break
    default:
      // 全局搜索
      router.push({
        path: '/search',
        query: { q: searchQuery.value }
      })
  }
  
  toggleSearch()
}

const clearSearch = () => {
  searchQuery.value = ''
  searchInput.value?.focus()
}

// 监听路由变化，关闭面板
watch(route, () => {
  showSearch.value = false
  showNotifications.value = false
  showMoreMenu.value = false
  searchQuery.value = ''
})

// 监听搜索查询参数
watch(() => route.query.search, (newSearch) => {
  if (newSearch && typeof newSearch === 'string') {
    searchQuery.value = newSearch
  }
})
</script>

<style scoped>
.toolbar-btn {
  @apply p-2 rounded-md text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-white transition-colors duration-200;
}

.toolbar-btn:focus {
  @apply outline-none ring-2 ring-blue-500 ring-offset-2 dark:ring-offset-gray-800;
}

/* 搜索栏动画 */
.search-enter-active,
.search-leave-active {
  transition: all 0.2s ease;
}

.search-enter-from,
.search-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .toolbar {
    padding: 0.75rem 1rem;
  }
  
  .toolbar h1 {
    font-size: 1rem;
  }
  
  .toolbar .flex.space-x-2 {
    gap: 0.25rem;
  }
}
</style>
