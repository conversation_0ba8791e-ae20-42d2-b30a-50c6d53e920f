import type { Language } from '@/types'
import zhCN from './zh-CN'
import enUS from './en-US'

// 支持的语言列表
export const supportedLanguages: Array<{ code: Language; name: string; nativeName: string }> = [
  { code: 'zh-CN', name: 'Chinese (Simplified)', nativeName: '简体中文' },
  { code: 'en-US', name: 'English (United States)', nativeName: 'English' }
]

// 消息对象
export const messages = {
  'zh-CN': zhCN,
  'en-US': enUS
}

// 默认语言
export const defaultLocale: Language = 'zh-CN'

// 获取浏览器语言
export function getBrowserLanguage(): Language {
  const browserLang = navigator.language || navigator.languages[0]
  
  // 匹配支持的语言
  for (const lang of supportedLanguages) {
    if (browserLang.startsWith(lang.code.split('-')[0])) {
      return lang.code
    }
  }
  
  return defaultLocale
}

// 获取保存的语言设置
export function getSavedLanguage(): Language {
  try {
    const saved = localStorage.getItem('app-language')
    if (saved && supportedLanguages.some(lang => lang.code === saved)) {
      return saved as Language
    }
  } catch (error) {
    console.warn('无法获取保存的语言设置:', error)
  }
  
  return getBrowserLanguage()
}

// 保存语言设置
export function saveLanguage(language: Language) {
  try {
    localStorage.setItem('app-language', language)
  } catch (error) {
    console.warn('无法保存语言设置:', error)
  }
}

// 语言管理器
export class LanguageManager {
  private currentLanguage: Language
  private listeners: Set<(language: Language) => void> = new Set()

  constructor() {
    this.currentLanguage = getSavedLanguage()
  }

  // 获取当前语言
  get language(): Language {
    return this.currentLanguage
  }

  // 设置语言
  setLanguage(language: Language) {
    if (this.currentLanguage !== language) {
      this.currentLanguage = language
      saveLanguage(language)
      this.notifyListeners(language)
    }
  }

  // 切换语言
  toggleLanguage() {
    const currentIndex = supportedLanguages.findIndex(lang => lang.code === this.currentLanguage)
    const nextIndex = (currentIndex + 1) % supportedLanguages.length
    this.setLanguage(supportedLanguages[nextIndex].code)
  }

  // 添加语言变化监听器
  addListener(listener: (language: Language) => void) {
    this.listeners.add(listener)
  }

  // 移除语言变化监听器
  removeListener(listener: (language: Language) => void) {
    this.listeners.delete(listener)
  }

  // 通知监听器
  private notifyListeners(language: Language) {
    this.listeners.forEach(listener => listener(language))
  }

  // 获取语言信息
  getLanguageInfo(code?: Language) {
    const targetCode = code || this.currentLanguage
    return supportedLanguages.find(lang => lang.code === targetCode)
  }

  // 获取所有支持的语言
  getSupportedLanguages() {
    return [...supportedLanguages]
  }

  // 检查是否支持某种语言
  isSupported(language: string): boolean {
    return supportedLanguages.some(lang => lang.code === language)
  }

  // 清理监听器
  cleanup() {
    this.listeners.clear()
  }
}

// 全局语言管理器实例
export const languageManager = new LanguageManager()

// 便捷的语言管理函数
export function useLanguage() {
  return {
    language: languageManager.language,
    setLanguage: languageManager.setLanguage.bind(languageManager),
    toggleLanguage: languageManager.toggleLanguage.bind(languageManager),
    getLanguageInfo: languageManager.getLanguageInfo.bind(languageManager),
    getSupportedLanguages: languageManager.getSupportedLanguages.bind(languageManager),
    isSupported: languageManager.isSupported.bind(languageManager),
    addListener: languageManager.addListener.bind(languageManager),
    removeListener: languageManager.removeListener.bind(languageManager)
  }
}
