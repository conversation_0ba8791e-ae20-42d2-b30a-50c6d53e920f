export default {
  // Common
  common: {
    confirm: 'Confirm',
    cancel: 'Cancel',
    save: 'Save',
    delete: 'Delete',
    edit: 'Edit',
    add: 'Add',
    create: 'Create',
    update: 'Update',
    search: 'Search',
    filter: 'Filter',
    sort: 'Sort',
    refresh: 'Refresh',
    loading: 'Loading...',
    success: 'Success',
    error: 'Error',
    warning: 'Warning',
    info: 'Info',
    yes: 'Yes',
    no: 'No',
    ok: 'OK',
    close: 'Close',
    back: 'Back',
    next: 'Next',
    previous: 'Previous',
    finish: 'Finish',
    submit: 'Submit',
    reset: 'Reset',
    clear: 'Clear',
    select: 'Select',
    upload: 'Upload',
    download: 'Download',
    export: 'Export',
    import: 'Import',
    copy: 'Copy',
    paste: 'Paste',
    cut: 'Cut',
    undo: 'Undo',
    redo: 'Redo',
    settings: 'Settings',
    help: 'Help',
    about: 'About',
    version: 'Version',
    more: 'More',
    less: 'Less',
    expand: 'Expand',
    collapse: 'Collapse',
    enable: 'Enable',
    disable: 'Disable',
    online: 'Online',
    offline: 'Offline',
    connecting: 'Connecting',
    connected: 'Connected',
    disconnected: 'Disconnected',
    retry: 'Retry',
    skip: 'Skip',
    ignore: 'Ignore',
    apply: 'Apply',
    preview: 'Preview',
    view: 'View',
    details: 'Details',
    name: 'Name',
    description: 'Description',
    type: 'Type',
    size: 'Size',
    status: 'Status',
    date: 'Date',
    time: 'Time',
    author: 'Author',
    category: 'Category',
    tags: 'Tags',
    priority: 'Priority',
    progress: 'Progress',
    total: 'Total',
    count: 'Count',
    page: 'Page',
    items: 'Items',
    all: 'All',
    none: 'None',
    empty: 'Empty',
    unknown: 'Unknown',
    other: 'Other'
  },

  // App
  app: {
    name: 'AI Studio',
    title: 'AI Studio - Intelligent AI Assistant',
    description: 'Chinese AI Assistant Desktop Application based on Vue3 + TypeScript + Vite + Tauri 2.x',
    loading: 'AI Studio is starting...',
    ready: 'AI Studio is ready',
    error: 'AI Studio failed to start',
    version: 'Version {version}',
    copyright: '© 2024 AI Studio Team. All rights reserved.'
  },

  // Navigation
  nav: {
    chat: 'Chat',
    knowledge: 'Knowledge Base',
    model: 'Model Management',
    remote: 'Remote Config',
    network: 'Network Sharing',
    multimodal: 'Multimodal',
    settings: 'Settings',
    user: 'User',
    theme: 'Theme',
    language: 'Language'
  },

  // Chat
  chat: {
    title: 'Chat',
    newChat: 'New Chat',
    sessions: 'Sessions',
    messages: 'Messages',
    input: 'Type a message...',
    send: 'Send',
    clear: 'Clear Chat',
    export: 'Export Chat',
    delete: 'Delete Session',
    rename: 'Rename',
    archive: 'Archive',
    unarchive: 'Unarchive',
    copy: 'Copy Message',
    regenerate: 'Regenerate',
    stop: 'Stop Generation',
    thinking: 'Thinking...',
    typing: 'Typing...',
    model: 'Model',
    temperature: 'Temperature',
    maxTokens: 'Max Tokens',
    systemPrompt: 'System Prompt',
    attachments: 'Attachments',
    addAttachment: 'Add Attachment',
    removeAttachment: 'Remove Attachment',
    supportedFormats: 'Supported Formats',
    dragToUpload: 'Drag files here to upload',
    messageCount: 'Message Count',
    tokenCount: 'Token Count',
    searchMessages: 'Search Messages',
    noMessages: 'No messages',
    noSessions: 'No sessions',
    sessionCreated: 'Session created',
    sessionDeleted: 'Session deleted',
    sessionRenamed: 'Session renamed',
    messageDeleted: 'Message deleted',
    messageCopied: 'Message copied',
    exportSuccess: 'Chat exported successfully',
    exportFailed: 'Failed to export chat'
  },

  // Knowledge Base
  knowledge: {
    title: 'Knowledge Base',
    create: 'Create Knowledge Base',
    list: 'Knowledge Base List',
    documents: 'Documents',
    upload: 'Upload Documents',
    search: 'Search Knowledge Base',
    analyze: 'Analyze Document',
    vectorize: 'Vectorize',
    embedding: 'Embedding Model',
    chunkSize: 'Chunk Size',
    chunkOverlap: 'Chunk Overlap',
    processing: 'Processing',
    processed: 'Processed',
    failed: 'Failed',
    documentCount: 'Document Count',
    vectorCount: 'Vector Count',
    totalSize: 'Total Size',
    supportedTypes: 'Supported File Types',
    dragToUpload: 'Drag files here to upload',
    batchUpload: 'Batch Upload',
    uploadProgress: 'Upload Progress',
    parseOptions: 'Parse Options',
    preserveFormatting: 'Preserve Formatting',
    extractImages: 'Extract Images',
    extractTables: 'Extract Tables',
    noDocuments: 'No documents',
    noKnowledgeBases: 'No knowledge bases',
    knowledgeBaseCreated: 'Knowledge base created',
    knowledgeBaseDeleted: 'Knowledge base deleted',
    documentUploaded: 'Document uploaded',
    documentDeleted: 'Document deleted',
    searchResults: 'Search Results',
    noResults: 'No search results',
    similarity: 'Similarity',
    source: 'Source',
    backup: 'Backup',
    restore: 'Restore',
    sync: 'Sync',
    export: 'Export',
    import: 'Import'
  },

  // Model Management
  model: {
    title: 'Model Management',
    local: 'Local Models',
    remote: 'Remote Models',
    download: 'Download Model',
    upload: 'Upload Model',
    load: 'Load Model',
    unload: 'Unload Model',
    delete: 'Delete Model',
    search: 'Search Models',
    filter: 'Filter Models',
    sort: 'Sort',
    info: 'Model Info',
    config: 'Model Config',
    performance: 'Performance Monitor',
    quantization: 'Quantization',
    deployment: 'Deployment',
    provider: 'Provider',
    architecture: 'Architecture',
    parameters: 'Parameters',
    contextLength: 'Context Length',
    languages: 'Supported Languages',
    capabilities: 'Capabilities',
    requirements: 'System Requirements',
    minMemory: 'Minimum Memory',
    recommendedMemory: 'Recommended Memory',
    gpuSupport: 'GPU Support',
    downloadProgress: 'Download Progress',
    downloadSpeed: 'Download Speed',
    eta: 'ETA',
    downloaded: 'Downloaded',
    installing: 'Installing',
    installed: 'Installed',
    loading: 'Loading',
    loaded: 'Loaded',
    unloading: 'Unloading',
    available: 'Available',
    unavailable: 'Unavailable',
    error: 'Error',
    noModels: 'No models',
    modelDownloaded: 'Model downloaded',
    modelLoaded: 'Model loaded',
    modelUnloaded: 'Model unloaded',
    modelDeleted: 'Model deleted',
    downloadFailed: 'Download failed',
    loadFailed: 'Load failed',
    tokensPerSecond: 'Tokens/sec',
    latency: 'Latency',
    memoryUsage: 'Memory Usage',
    cpuUsage: 'CPU Usage',
    gpuUsage: 'GPU Usage',
    temperature: 'Temperature'
  },

  // Remote Config
  remote: {
    title: 'Remote Config',
    apiKeys: 'API Keys',
    endpoints: 'Endpoints',
    proxy: 'Proxy Settings',
    cloudModels: 'Cloud Models',
    providers: 'Providers',
    openai: 'OpenAI',
    anthropic: 'Anthropic',
    google: 'Google',
    azure: 'Azure',
    custom: 'Custom',
    apiKey: 'API Key',
    baseUrl: 'Base URL',
    organization: 'Organization',
    project: 'Project',
    model: 'Model',
    maxTokens: 'Max Tokens',
    temperature: 'Temperature',
    topP: 'Top P',
    frequencyPenalty: 'Frequency Penalty',
    presencePenalty: 'Presence Penalty',
    timeout: 'Timeout',
    retries: 'Retries',
    proxyType: 'Proxy Type',
    proxyHost: 'Proxy Host',
    proxyPort: 'Proxy Port',
    proxyUsername: 'Proxy Username',
    proxyPassword: 'Proxy Password',
    testConnection: 'Test Connection',
    connectionSuccess: 'Connection successful',
    connectionFailed: 'Connection failed',
    invalidApiKey: 'Invalid API key',
    rateLimitExceeded: 'Rate limit exceeded',
    quotaExceeded: 'Quota exceeded',
    serviceUnavailable: 'Service unavailable',
    configSaved: 'Config saved',
    configDeleted: 'Config deleted'
  },

  // Network Sharing
  network: {
    title: 'Network Sharing',
    discovery: 'Device Discovery',
    connections: 'Connection Management',
    sharing: 'Resource Sharing',
    transfers: 'File Transfers',
    nodes: 'Network Nodes',
    connect: 'Connect',
    disconnect: 'Disconnect',
    trust: 'Trust Device',
    untrust: 'Untrust Device',
    share: 'Share',
    unshare: 'Unshare',
    download: 'Download',
    upload: 'Upload',
    sync: 'Sync',
    hostname: 'Hostname',
    ipAddress: 'IP Address',
    macAddress: 'MAC Address',
    platform: 'Platform',
    version: 'Version',
    status: 'Status',
    lastSeen: 'Last Seen',
    capabilities: 'Capabilities',
    resources: 'Resources',
    bandwidth: 'Bandwidth',
    latency: 'Latency',
    online: 'Online',
    offline: 'Offline',
    connecting: 'Connecting',
    connected: 'Connected',
    disconnected: 'Disconnected',
    trusted: 'Trusted',
    untrusted: 'Untrusted',
    transferring: 'Transferring',
    completed: 'Completed',
    failed: 'Failed',
    cancelled: 'Cancelled',
    paused: 'Paused',
    resume: 'Resume',
    pause: 'Pause',
    cancel: 'Cancel',
    retry: 'Retry',
    speed: 'Speed',
    progress: 'Progress',
    eta: 'ETA',
    noNodes: 'No devices found',
    noTransfers: 'No transfer tasks',
    deviceConnected: 'Device connected',
    deviceDisconnected: 'Device disconnected',
    transferStarted: 'Transfer started',
    transferCompleted: 'Transfer completed',
    transferFailed: 'Transfer failed',
    sharingEnabled: 'Sharing enabled',
    sharingDisabled: 'Sharing disabled'
  },

  // Multimodal
  multimodal: {
    title: 'Multimodal',
    ocr: 'OCR',
    tts: 'Text to Speech',
    stt: 'Speech to Text',
    vision: 'Vision',
    audio: 'Audio Processing',
    video: 'Video Processing',
    image: 'Image Processing',
    document: 'Document Processing',
    upload: 'Upload File',
    process: 'Start Processing',
    result: 'Processing Result',
    download: 'Download Result',
    history: 'Processing History',
    settings: 'Processing Settings',
    model: 'Processing Model',
    language: 'Language',
    quality: 'Quality',
    format: 'Format',
    confidence: 'Confidence',
    processing: 'Processing',
    completed: 'Completed',
    failed: 'Failed',
    pending: 'Pending',
    text: 'Text',
    objects: 'Objects',
    faces: 'Faces',
    scenes: 'Scenes',
    emotions: 'Emotions',
    transcription: 'Transcription',
    translation: 'Translation',
    synthesis: 'Synthesis',
    recognition: 'Recognition',
    detection: 'Detection',
    classification: 'Classification',
    segmentation: 'Segmentation',
    enhancement: 'Enhancement',
    restoration: 'Restoration',
    compression: 'Compression',
    conversion: 'Conversion',
    extraction: 'Extraction',
    analysis: 'Analysis',
    noTasks: 'No processing tasks',
    taskCreated: 'Task created',
    taskCompleted: 'Task completed',
    taskFailed: 'Task failed',
    unsupportedFormat: 'Unsupported format',
    fileTooLarge: 'File too large',
    processingTime: 'Processing Time',
    fileSize: 'File Size',
    dimensions: 'Dimensions',
    duration: 'Duration',
    sampleRate: 'Sample Rate',
    bitRate: 'Bit Rate',
    channels: 'Channels',
    frameRate: 'Frame Rate',
    codec: 'Codec'
  },

  // Settings
  settings: {
    title: 'Settings',
    general: 'General',
    appearance: 'Appearance',
    language: 'Language',
    theme: 'Theme',
    notifications: 'Notifications',
    privacy: 'Privacy',
    security: 'Security',
    advanced: 'Advanced',
    about: 'About',
    autoStart: 'Auto Start',
    minimizeToTray: 'Minimize to Tray',
    enableNotifications: 'Enable Notifications',
    enableSounds: 'Enable Sounds',
    fontSize: 'Font Size',
    fontFamily: 'Font Family',
    primaryColor: 'Primary Color',
    borderRadius: 'Border Radius',
    lightTheme: 'Light Theme',
    darkTheme: 'Dark Theme',
    autoTheme: 'Follow System',
    chinese: '中文',
    english: 'English',
    dataCollection: 'Data Collection',
    analytics: 'Analytics',
    crashReports: 'Crash Reports',
    errorReporting: 'Error Reporting',
    automaticUpdates: 'Automatic Updates',
    betaUpdates: 'Beta Updates',
    hardwareAcceleration: 'Hardware Acceleration',
    debugMode: 'Debug Mode',
    developerTools: 'Developer Tools',
    clearCache: 'Clear Cache',
    resetSettings: 'Reset Settings',
    exportSettings: 'Export Settings',
    importSettings: 'Import Settings',
    version: 'Version',
    buildDate: 'Build Date',
    platform: 'Platform',
    architecture: 'Architecture',
    license: 'License',
    thirdPartyLicenses: 'Third Party Licenses',
    checkForUpdates: 'Check for Updates',
    updateAvailable: 'Update Available',
    updateDownloading: 'Downloading Update',
    updateReady: 'Update Ready',
    restartToUpdate: 'Restart to Update',
    noUpdatesAvailable: 'No Updates Available',
    settingsSaved: 'Settings saved',
    settingsReset: 'Settings reset',
    cacheCleared: 'Cache cleared'
  },

  // User
  user: {
    title: 'User Info',
    profile: 'Profile',
    account: 'Account',
    avatar: 'Avatar',
    username: 'Username',
    email: 'Email',
    nickname: 'Nickname',
    bio: 'Bio',
    location: 'Location',
    website: 'Website',
    joinDate: 'Join Date',
    lastLogin: 'Last Login',
    changeAvatar: 'Change Avatar',
    changePassword: 'Change Password',
    currentPassword: 'Current Password',
    newPassword: 'New Password',
    confirmPassword: 'Confirm Password',
    logout: 'Logout',
    deleteAccount: 'Delete Account',
    profileUpdated: 'Profile updated',
    passwordChanged: 'Password changed',
    avatarChanged: 'Avatar changed',
    invalidPassword: 'Invalid password',
    passwordMismatch: 'Password mismatch',
    weakPassword: 'Weak password',
    emailInvalid: 'Invalid email',
    usernameExists: 'Username exists',
    emailExists: 'Email exists'
  },

  // Error Messages
  error: {
    networkError: 'Network error',
    serverError: 'Server error',
    unauthorized: 'Unauthorized',
    forbidden: 'Forbidden',
    notFound: 'Not found',
    timeout: 'Timeout',
    invalidInput: 'Invalid input',
    validationFailed: 'Validation failed',
    operationFailed: 'Operation failed',
    fileNotFound: 'File not found',
    fileTooBig: 'File too big',
    unsupportedFormat: 'Unsupported format',
    insufficientSpace: 'Insufficient space',
    permissionDenied: 'Permission denied',
    deviceNotFound: 'Device not found',
    connectionLost: 'Connection lost',
    syncFailed: 'Sync failed',
    backupFailed: 'Backup failed',
    restoreFailed: 'Restore failed',
    updateFailed: 'Update failed',
    installFailed: 'Install failed',
    uninstallFailed: 'Uninstall failed',
    configError: 'Config error',
    databaseError: 'Database error',
    unknownError: 'Unknown error'
  },

  // Success Messages
  success: {
    operationCompleted: 'Operation completed',
    fileSaved: 'File saved',
    fileUploaded: 'File uploaded',
    fileDownloaded: 'File downloaded',
    dataExported: 'Data exported',
    dataImported: 'Data imported',
    settingsSaved: 'Settings saved',
    configUpdated: 'Config updated',
    connectionEstablished: 'Connection established',
    syncCompleted: 'Sync completed',
    backupCreated: 'Backup created',
    restoreCompleted: 'Restore completed',
    updateInstalled: 'Update installed',
    installCompleted: 'Install completed',
    uninstallCompleted: 'Uninstall completed'
  },

  // Plugin
  plugin: {
    title: 'Plugin Management',
    market: 'Plugin Market',
    installed: 'Installed',
    available: 'Available',
    updates: 'Updates',
    search: 'Search Plugins',
    install: 'Install',
    uninstall: 'Uninstall',
    enable: 'Enable',
    disable: 'Disable',
    update: 'Update',
    configure: 'Configure',
    details: 'Details',
    permissions: 'Permissions',
    dependencies: 'Dependencies',
    version: 'Version',
    author: 'Author',
    description: 'Description',
    category: 'Category',
    tags: 'Tags',
    rating: 'Rating',
    downloads: 'Downloads',
    reviews: 'Reviews',
    changelog: 'Changelog',
    license: 'License',
    homepage: 'Homepage',
    repository: 'Repository',
    support: 'Support',
    verified: 'Verified',
    official: 'Official',
    community: 'Community',
    experimental: 'Experimental',
    deprecated: 'Deprecated',
    installing: 'Installing',
    uninstalling: 'Uninstalling',
    updating: 'Updating',
    enabled: 'Enabled',
    disabled: 'Disabled',
    error: 'Error',
    noPlugins: 'No plugins',
    installSuccess: 'Plugin installed successfully',
    installFailed: 'Plugin installation failed',
    uninstallSuccess: 'Plugin uninstalled successfully',
    uninstallFailed: 'Plugin uninstallation failed',
    updateSuccess: 'Plugin updated successfully',
    updateFailed: 'Plugin update failed',
    enableSuccess: 'Plugin enabled successfully',
    disableSuccess: 'Plugin disabled successfully',
    configSaved: 'Plugin config saved',
    permissionRequired: 'Permission required',
    dependencyMissing: 'Dependency missing',
    incompatibleVersion: 'Incompatible version',
    networkSearch: 'Network Search',
    networkTools: 'Network Tools',
    fileAccess: 'File Access',
    customApi: 'Custom API',
    jsScript: 'JavaScript Script'
  }
}
