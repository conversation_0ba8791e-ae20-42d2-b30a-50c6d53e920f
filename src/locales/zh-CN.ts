export default {
  // 通用
  common: {
    confirm: '确认',
    cancel: '取消',
    save: '保存',
    delete: '删除',
    edit: '编辑',
    add: '添加',
    create: '创建',
    update: '更新',
    search: '搜索',
    filter: '筛选',
    sort: '排序',
    refresh: '刷新',
    loading: '加载中...',
    success: '成功',
    error: '错误',
    warning: '警告',
    info: '信息',
    yes: '是',
    no: '否',
    ok: '确定',
    close: '关闭',
    back: '返回',
    next: '下一步',
    previous: '上一步',
    finish: '完成',
    submit: '提交',
    reset: '重置',
    clear: '清空',
    select: '选择',
    upload: '上传',
    download: '下载',
    export: '导出',
    import: '导入',
    copy: '复制',
    paste: '粘贴',
    cut: '剪切',
    undo: '撤销',
    redo: '重做',
    settings: '设置',
    help: '帮助',
    about: '关于',
    version: '版本',
    more: '更多',
    less: '收起',
    expand: '展开',
    collapse: '折叠',
    enable: '启用',
    disable: '禁用',
    online: '在线',
    offline: '离线',
    connecting: '连接中',
    connected: '已连接',
    disconnected: '已断开',
    retry: '重试',
    skip: '跳过',
    ignore: '忽略',
    apply: '应用',
    preview: '预览',
    view: '查看',
    details: '详情',
    name: '名称',
    description: '描述',
    type: '类型',
    size: '大小',
    status: '状态',
    date: '日期',
    time: '时间',
    author: '作者',
    category: '分类',
    tags: '标签',
    priority: '优先级',
    progress: '进度',
    total: '总计',
    count: '数量',
    page: '页面',
    items: '项目',
    all: '全部',
    none: '无',
    empty: '空',
    unknown: '未知',
    other: '其他'
  },

  // 应用
  app: {
    name: 'AI Studio',
    title: 'AI Studio - 智能AI助手',
    description: '基于 Vue3 + TypeScript + Vite + Tauri 2.x 的中文AI助手桌面应用',
    loading: 'AI Studio 正在启动...',
    ready: 'AI Studio 已就绪',
    error: 'AI Studio 启动失败',
    version: '版本 {version}',
    copyright: '© 2024 AI Studio Team. 保留所有权利。'
  },

  // 导航
  nav: {
    chat: '聊天',
    knowledge: '知识库',
    model: '模型管理',
    remote: '远程配置',
    network: '局域网共享',
    multimodal: '多模态',
    settings: '设置',
    user: '用户',
    theme: '主题',
    language: '语言'
  },

  // 聊天
  chat: {
    title: '聊天',
    newChat: '新建聊天',
    sessions: '会话列表',
    messages: '消息',
    input: '输入消息...',
    send: '发送',
    clear: '清空对话',
    export: '导出对话',
    delete: '删除会话',
    rename: '重命名',
    archive: '归档',
    unarchive: '取消归档',
    copy: '复制消息',
    regenerate: '重新生成',
    stop: '停止生成',
    thinking: '思考中...',
    typing: '正在输入...',
    model: '模型',
    temperature: '温度',
    maxTokens: '最大令牌数',
    systemPrompt: '系统提示词',
    attachments: '附件',
    addAttachment: '添加附件',
    removeAttachment: '移除附件',
    supportedFormats: '支持的格式',
    dragToUpload: '拖拽文件到此处上传',
    messageCount: '消息数量',
    tokenCount: '令牌数量',
    searchMessages: '搜索消息',
    noMessages: '暂无消息',
    noSessions: '暂无会话',
    sessionCreated: '会话已创建',
    sessionDeleted: '会话已删除',
    sessionRenamed: '会话已重命名',
    messageDeleted: '消息已删除',
    messageCopied: '消息已复制',
    exportSuccess: '对话导出成功',
    exportFailed: '对话导出失败'
  },

  // 知识库
  knowledge: {
    title: '知识库',
    create: '创建知识库',
    list: '知识库列表',
    documents: '文档',
    upload: '上传文档',
    search: '搜索知识库',
    analyze: '分析文档',
    vectorize: '向量化',
    embedding: '嵌入模型',
    chunkSize: '分块大小',
    chunkOverlap: '分块重叠',
    processing: '处理中',
    processed: '已处理',
    failed: '处理失败',
    documentCount: '文档数量',
    vectorCount: '向量数量',
    totalSize: '总大小',
    supportedTypes: '支持的文件类型',
    dragToUpload: '拖拽文件到此处上传',
    batchUpload: '批量上传',
    uploadProgress: '上传进度',
    parseOptions: '解析选项',
    preserveFormatting: '保留格式',
    extractImages: '提取图片',
    extractTables: '提取表格',
    noDocuments: '暂无文档',
    noKnowledgeBases: '暂无知识库',
    knowledgeBaseCreated: '知识库已创建',
    knowledgeBaseDeleted: '知识库已删除',
    documentUploaded: '文档已上传',
    documentDeleted: '文档已删除',
    searchResults: '搜索结果',
    noResults: '无搜索结果',
    similarity: '相似度',
    source: '来源',
    backup: '备份',
    restore: '恢复',
    sync: '同步',
    export: '导出',
    import: '导入'
  },

  // 模型管理
  model: {
    title: '模型管理',
    local: '本地模型',
    remote: '远程模型',
    download: '下载模型',
    upload: '上传模型',
    load: '加载模型',
    unload: '卸载模型',
    delete: '删除模型',
    search: '搜索模型',
    filter: '筛选模型',
    sort: '排序',
    info: '模型信息',
    config: '模型配置',
    performance: '性能监控',
    quantization: '量化',
    deployment: '部署',
    provider: '提供商',
    architecture: '架构',
    parameters: '参数量',
    contextLength: '上下文长度',
    languages: '支持语言',
    capabilities: '能力',
    requirements: '系统要求',
    minMemory: '最小内存',
    recommendedMemory: '推荐内存',
    gpuSupport: 'GPU支持',
    downloadProgress: '下载进度',
    downloadSpeed: '下载速度',
    eta: '预计剩余时间',
    downloaded: '已下载',
    installing: '安装中',
    installed: '已安装',
    loading: '加载中',
    loaded: '已加载',
    unloading: '卸载中',
    available: '可用',
    unavailable: '不可用',
    error: '错误',
    noModels: '暂无模型',
    modelDownloaded: '模型下载完成',
    modelLoaded: '模型加载完成',
    modelUnloaded: '模型卸载完成',
    modelDeleted: '模型删除完成',
    downloadFailed: '下载失败',
    loadFailed: '加载失败',
    tokensPerSecond: '令牌/秒',
    latency: '延迟',
    memoryUsage: '内存使用',
    cpuUsage: 'CPU使用',
    gpuUsage: 'GPU使用',
    temperature: '温度'
  },

  // 远程配置
  remote: {
    title: '远程配置',
    apiKeys: 'API密钥',
    endpoints: '端点配置',
    proxy: '代理设置',
    cloudModels: '云端模型',
    providers: '服务提供商',
    openai: 'OpenAI',
    anthropic: 'Anthropic',
    google: 'Google',
    azure: 'Azure',
    custom: '自定义',
    apiKey: 'API密钥',
    baseUrl: '基础URL',
    organization: '组织',
    project: '项目',
    model: '模型',
    maxTokens: '最大令牌数',
    temperature: '温度',
    topP: 'Top P',
    frequencyPenalty: '频率惩罚',
    presencePenalty: '存在惩罚',
    timeout: '超时时间',
    retries: '重试次数',
    proxyType: '代理类型',
    proxyHost: '代理主机',
    proxyPort: '代理端口',
    proxyUsername: '代理用户名',
    proxyPassword: '代理密码',
    testConnection: '测试连接',
    connectionSuccess: '连接成功',
    connectionFailed: '连接失败',
    invalidApiKey: 'API密钥无效',
    rateLimitExceeded: '请求频率超限',
    quotaExceeded: '配额已用完',
    serviceUnavailable: '服务不可用',
    configSaved: '配置已保存',
    configDeleted: '配置已删除'
  },

  // 局域网共享
  network: {
    title: '局域网共享',
    discovery: '设备发现',
    connections: '连接管理',
    sharing: '资源共享',
    transfers: '文件传输',
    nodes: '网络节点',
    connect: '连接',
    disconnect: '断开连接',
    trust: '信任设备',
    untrust: '取消信任',
    share: '共享',
    unshare: '取消共享',
    download: '下载',
    upload: '上传',
    sync: '同步',
    hostname: '主机名',
    ipAddress: 'IP地址',
    macAddress: 'MAC地址',
    platform: '平台',
    version: '版本',
    status: '状态',
    lastSeen: '最后在线',
    capabilities: '能力',
    resources: '资源',
    bandwidth: '带宽',
    latency: '延迟',
    online: '在线',
    offline: '离线',
    connecting: '连接中',
    connected: '已连接',
    disconnected: '已断开',
    trusted: '已信任',
    untrusted: '未信任',
    transferring: '传输中',
    completed: '已完成',
    failed: '失败',
    cancelled: '已取消',
    paused: '已暂停',
    resume: '恢复',
    pause: '暂停',
    cancel: '取消',
    retry: '重试',
    speed: '速度',
    progress: '进度',
    eta: '预计剩余时间',
    noNodes: '未发现设备',
    noTransfers: '暂无传输任务',
    deviceConnected: '设备已连接',
    deviceDisconnected: '设备已断开',
    transferStarted: '传输已开始',
    transferCompleted: '传输已完成',
    transferFailed: '传输失败',
    sharingEnabled: '共享已启用',
    sharingDisabled: '共享已禁用'
  },

  // 多模态
  multimodal: {
    title: '多模态',
    ocr: '文字识别',
    tts: '语音合成',
    stt: '语音识别',
    vision: '图像理解',
    audio: '音频处理',
    video: '视频处理',
    image: '图像处理',
    document: '文档处理',
    upload: '上传文件',
    process: '开始处理',
    result: '处理结果',
    download: '下载结果',
    history: '处理历史',
    settings: '处理设置',
    model: '处理模型',
    language: '语言',
    quality: '质量',
    format: '格式',
    confidence: '置信度',
    processing: '处理中',
    completed: '处理完成',
    failed: '处理失败',
    pending: '等待处理',
    text: '文本',
    objects: '对象',
    faces: '人脸',
    scenes: '场景',
    emotions: '情感',
    transcription: '转录',
    translation: '翻译',
    synthesis: '合成',
    recognition: '识别',
    detection: '检测',
    classification: '分类',
    segmentation: '分割',
    enhancement: '增强',
    restoration: '修复',
    compression: '压缩',
    conversion: '转换',
    extraction: '提取',
    analysis: '分析',
    noTasks: '暂无处理任务',
    taskCreated: '任务已创建',
    taskCompleted: '任务已完成',
    taskFailed: '任务失败',
    unsupportedFormat: '不支持的格式',
    fileTooLarge: '文件过大',
    processingTime: '处理时间',
    fileSize: '文件大小',
    dimensions: '尺寸',
    duration: '时长',
    sampleRate: '采样率',
    bitRate: '比特率',
    channels: '声道数',
    frameRate: '帧率',
    codec: '编码器'
  },

  // 设置
  settings: {
    title: '设置',
    general: '通用',
    appearance: '外观',
    language: '语言',
    theme: '主题',
    notifications: '通知',
    privacy: '隐私',
    security: '安全',
    advanced: '高级',
    about: '关于',
    autoStart: '开机自启',
    minimizeToTray: '最小化到托盘',
    enableNotifications: '启用通知',
    enableSounds: '启用声音',
    fontSize: '字体大小',
    fontFamily: '字体族',
    primaryColor: '主色调',
    borderRadius: '圆角大小',
    lightTheme: '浅色主题',
    darkTheme: '深色主题',
    autoTheme: '跟随系统',
    chinese: '中文',
    english: 'English',
    dataCollection: '数据收集',
    analytics: '分析统计',
    crashReports: '崩溃报告',
    errorReporting: '错误报告',
    automaticUpdates: '自动更新',
    betaUpdates: '测试版更新',
    hardwareAcceleration: '硬件加速',
    debugMode: '调试模式',
    developerTools: '开发者工具',
    clearCache: '清除缓存',
    resetSettings: '重置设置',
    exportSettings: '导出设置',
    importSettings: '导入设置',
    version: '版本',
    buildDate: '构建日期',
    platform: '平台',
    architecture: '架构',
    license: '许可证',
    thirdPartyLicenses: '第三方许可证',
    checkForUpdates: '检查更新',
    updateAvailable: '有可用更新',
    updateDownloading: '正在下载更新',
    updateReady: '更新已就绪',
    restartToUpdate: '重启以更新',
    noUpdatesAvailable: '已是最新版本',
    settingsSaved: '设置已保存',
    settingsReset: '设置已重置',
    cacheCleared: '缓存已清除'
  },

  // 用户
  user: {
    title: '用户信息',
    profile: '个人资料',
    account: '账户',
    avatar: '头像',
    username: '用户名',
    email: '邮箱',
    nickname: '昵称',
    bio: '个人简介',
    location: '位置',
    website: '网站',
    joinDate: '加入日期',
    lastLogin: '最后登录',
    changeAvatar: '更换头像',
    changePassword: '修改密码',
    currentPassword: '当前密码',
    newPassword: '新密码',
    confirmPassword: '确认密码',
    logout: '退出登录',
    deleteAccount: '删除账户',
    profileUpdated: '资料已更新',
    passwordChanged: '密码已修改',
    avatarChanged: '头像已更换',
    invalidPassword: '密码错误',
    passwordMismatch: '密码不匹配',
    weakPassword: '密码强度不够',
    emailInvalid: '邮箱格式错误',
    usernameExists: '用户名已存在',
    emailExists: '邮箱已存在'
  },

  // 错误消息
  error: {
    networkError: '网络连接错误',
    serverError: '服务器错误',
    unauthorized: '未授权访问',
    forbidden: '访问被禁止',
    notFound: '资源未找到',
    timeout: '请求超时',
    invalidInput: '输入无效',
    validationFailed: '验证失败',
    operationFailed: '操作失败',
    fileNotFound: '文件未找到',
    fileTooBig: '文件过大',
    unsupportedFormat: '不支持的格式',
    insufficientSpace: '存储空间不足',
    permissionDenied: '权限不足',
    deviceNotFound: '设备未找到',
    connectionLost: '连接丢失',
    syncFailed: '同步失败',
    backupFailed: '备份失败',
    restoreFailed: '恢复失败',
    updateFailed: '更新失败',
    installFailed: '安装失败',
    uninstallFailed: '卸载失败',
    configError: '配置错误',
    databaseError: '数据库错误',
    unknownError: '未知错误'
  },

  // 成功消息
  success: {
    operationCompleted: '操作完成',
    fileSaved: '文件已保存',
    fileUploaded: '文件已上传',
    fileDownloaded: '文件已下载',
    dataExported: '数据已导出',
    dataImported: '数据已导入',
    settingsSaved: '设置已保存',
    configUpdated: '配置已更新',
    connectionEstablished: '连接已建立',
    syncCompleted: '同步完成',
    backupCreated: '备份已创建',
    restoreCompleted: '恢复完成',
    updateInstalled: '更新已安装',
    installCompleted: '安装完成',
    uninstallCompleted: '卸载完成'
  },

  // 插件
  plugin: {
    title: '插件管理',
    market: '插件市场',
    installed: '已安装',
    available: '可用插件',
    updates: '更新',
    search: '搜索插件',
    install: '安装',
    uninstall: '卸载',
    enable: '启用',
    disable: '禁用',
    update: '更新',
    configure: '配置',
    details: '详情',
    permissions: '权限',
    dependencies: '依赖',
    version: '版本',
    author: '作者',
    description: '描述',
    category: '分类',
    tags: '标签',
    rating: '评分',
    downloads: '下载量',
    reviews: '评价',
    changelog: '更新日志',
    license: '许可证',
    homepage: '主页',
    repository: '仓库',
    support: '支持',
    verified: '已验证',
    official: '官方',
    community: '社区',
    experimental: '实验性',
    deprecated: '已弃用',
    installing: '安装中',
    uninstalling: '卸载中',
    updating: '更新中',
    enabled: '已启用',
    disabled: '已禁用',
    error: '错误',
    noPlugins: '暂无插件',
    installSuccess: '插件安装成功',
    installFailed: '插件安装失败',
    uninstallSuccess: '插件卸载成功',
    uninstallFailed: '插件卸载失败',
    updateSuccess: '插件更新成功',
    updateFailed: '插件更新失败',
    enableSuccess: '插件启用成功',
    disableSuccess: '插件禁用成功',
    configSaved: '插件配置已保存',
    permissionRequired: '需要权限',
    dependencyMissing: '缺少依赖',
    incompatibleVersion: '版本不兼容',
    networkSearch: '联网搜索',
    networkTools: '联网工具',
    fileAccess: '文件访问',
    customApi: '自定义API',
    jsScript: 'JavaScript脚本'
  }
}
