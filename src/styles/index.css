/* Tailwind CSS 基础样式 */
@tailwind base;
@tailwind components;
@tailwind utilities;

/* 自定义CSS变量 - 亮色主题 */
:root {
  /* 主色调 */
  --color-primary: #3b82f6;
  --color-primary-hover: #2563eb;
  --color-primary-active: #1d4ed8;
  
  /* 背景色 */
  --color-bg-primary: #ffffff;
  --color-bg-secondary: #f8fafc;
  --color-bg-tertiary: #f1f5f9;
  --color-bg-hover: #f8fafc;
  
  /* 文字颜色 */
  --color-text-primary: #1e293b;
  --color-text-secondary: #64748b;
  --color-text-tertiary: #94a3b8;
  --color-text-inverse: #ffffff;
  
  /* 边框颜色 */
  --color-border-primary: #e2e8f0;
  --color-border-secondary: #cbd5e1;
  --color-border-hover: #94a3b8;
  
  /* 阴影 */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  
  /* 圆角 */
  --radius-sm: 0.25rem;
  --radius-md: 0.375rem;
  --radius-lg: 0.5rem;
  --radius-xl: 0.75rem;
  
  /* 间距 */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  
  /* 动画时长 */
  --duration-fast: 0.15s;
  --duration-normal: 0.3s;
  --duration-slow: 0.5s;
}

/* 深色主题CSS变量 */
.dark {
  /* 主色调 */
  --color-primary: #60a5fa;
  --color-primary-hover: #3b82f6;
  --color-primary-active: #2563eb;
  
  /* 背景色 */
  --color-bg-primary: #0f172a;
  --color-bg-secondary: #1e293b;
  --color-bg-tertiary: #334155;
  --color-bg-hover: #475569;
  
  /* 文字颜色 */
  --color-text-primary: #f8fafc;
  --color-text-secondary: #cbd5e1;
  --color-text-tertiary: #94a3b8;
  --color-text-inverse: #1e293b;
  
  /* 边框颜色 */
  --color-border-primary: #334155;
  --color-border-secondary: #475569;
  --color-border-hover: #64748b;
  
  /* 阴影 */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.4), 0 2px 4px -1px rgba(0, 0, 0, 0.3);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.4), 0 4px 6px -2px rgba(0, 0, 0, 0.3);
}

/* 基础组件样式 */
@layer components {
  /* 按钮基础样式 */
  .btn {
    @apply inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-md transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2;
  }
  
  .btn-primary {
    @apply bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500;
  }
  
  .btn-secondary {
    @apply bg-gray-200 text-gray-900 hover:bg-gray-300 focus:ring-gray-500;
  }
  
  .btn-ghost {
    @apply bg-transparent text-gray-700 hover:bg-gray-100 focus:ring-gray-500;
  }
  
  /* 输入框基础样式 */
  .input {
    @apply block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500;
  }
  
  /* 卡片基础样式 */
  .card {
    @apply bg-white rounded-lg shadow-sm border border-gray-200 p-6;
  }
  
  .dark .card {
    @apply bg-gray-800 border-gray-700;
  }
  
  /* 侧边栏样式 */
  .sidebar {
    @apply bg-white border-r border-gray-200 flex flex-col;
  }
  
  .dark .sidebar {
    @apply bg-gray-900 border-gray-700;
  }
  
  /* 导航项样式 */
  .nav-item {
    @apply flex items-center px-4 py-3 text-sm font-medium text-gray-700 hover:bg-gray-100 hover:text-gray-900 transition-colors duration-200;
  }
  
  .dark .nav-item {
    @apply text-gray-300 hover:bg-gray-800 hover:text-white;
  }
  
  .nav-item.active {
    @apply bg-blue-50 text-blue-700 border-r-2 border-blue-600;
  }
  
  .dark .nav-item.active {
    @apply bg-blue-900/50 text-blue-400 border-blue-400;
  }
  
  /* 工具栏样式 */
  .toolbar {
    @apply bg-white border-b border-gray-200 px-4 py-3 flex items-center justify-between;
  }
  
  .dark .toolbar {
    @apply bg-gray-900 border-gray-700;
  }
}

/* 工具类 */
@layer utilities {
  /* 文本省略 */
  .text-ellipsis {
    @apply truncate;
  }
  
  /* 滚动区域 */
  .scrollable {
    @apply overflow-auto;
  }
  
  /* 无滚动条 */
  .no-scrollbar {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  
  .no-scrollbar::-webkit-scrollbar {
    display: none;
  }
  
  /* 拖拽区域 */
  .drag-region {
    -webkit-app-region: drag;
  }
  
  .no-drag {
    -webkit-app-region: no-drag;
  }
  
  /* 玻璃效果 */
  .glass {
    @apply backdrop-blur-sm bg-white/80 border border-white/20;
  }
  
  .dark .glass {
    @apply bg-gray-900/80 border-gray-700/50;
  }
}
