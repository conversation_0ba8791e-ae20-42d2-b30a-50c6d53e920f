<template>
  <div class="knowledge-view h-full flex">
    <!-- 知识库列表侧边栏 -->
    <div class="knowledge-sidebar w-80 bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 flex flex-col">
      <!-- 侧边栏头部 -->
      <div class="sidebar-header p-4 border-b border-gray-200 dark:border-gray-700">
        <div class="flex items-center justify-between mb-4">
          <h2 class="text-lg font-semibold text-gray-900 dark:text-white">
            {{ $t('knowledge.list') }}
          </h2>
          <button
            @click="showCreateModal = true"
            class="btn-primary p-2 rounded-lg"
            :title="$t('knowledge.create')"
          >
            <PlusIcon class="w-5 h-5" />
          </button>
        </div>
        
        <!-- 搜索框 -->
        <div class="relative">
          <MagnifyingGlassIcon class="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
          <input
            v-model="knowledgeSearchQuery"
            type="text"
            :placeholder="$t('knowledge.search')"
            class="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>
      </div>

      <!-- 知识库列表 -->
      <div class="knowledge-list flex-1 overflow-y-auto">
        <div v-if="filteredKnowledgeBases.length === 0" class="p-4 text-center text-gray-500 dark:text-gray-400">
          {{ $t('knowledge.noKnowledgeBases') }}
        </div>
        
        <div v-else class="p-2 space-y-1">
          <div
            v-for="kb in filteredKnowledgeBases"
            :key="kb.id"
            @click="selectKnowledgeBase(kb.id)"
            class="knowledge-item p-3 rounded-lg cursor-pointer transition-colors"
            :class="[
              currentKnowledgeBaseId === kb.id
                ? 'bg-blue-50 text-blue-700 dark:bg-blue-900/50 dark:text-blue-400'
                : 'hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-900 dark:text-white'
            ]"
          >
            <div class="flex items-start justify-between">
              <div class="flex-1 min-w-0">
                <h3 class="font-medium truncate">{{ kb.name }}</h3>
                <p class="text-sm text-gray-500 dark:text-gray-400 truncate mt-1">
                  {{ kb.description || $t('knowledge.noDescription') }}
                </p>
                <div class="flex items-center justify-between mt-2 text-xs text-gray-400">
                  <span>{{ formatDate(kb.updatedAt) }}</span>
                  <div class="flex items-center space-x-2">
                    <span>{{ kb.documentCount }} {{ $t('knowledge.documents') }}</span>
                    <span>{{ formatSize(kb.size) }}</span>
                  </div>
                </div>
              </div>
              
              <!-- 知识库操作按钮 -->
              <div class="flex items-center space-x-1 ml-2">
                <button
                  @click.stop="toggleKnowledgeBaseMenu(kb.id)"
                  class="p-1 rounded hover:bg-gray-200 dark:hover:bg-gray-600"
                >
                  <EllipsisVerticalIcon class="w-4 h-4" />
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 知识库主区域 -->
    <div class="knowledge-main flex-1 flex flex-col">
      <!-- 知识库头部 -->
      <div v-if="currentKnowledgeBase" class="knowledge-header p-4 border-b border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-3">
            <h1 class="text-xl font-semibold text-gray-900 dark:text-white">
              {{ currentKnowledgeBase.name }}
            </h1>
            <span class="px-2 py-1 text-xs bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 rounded">
              {{ currentKnowledgeBase.embeddingModel }}
            </span>
          </div>
          
          <div class="flex items-center space-x-2">
            <!-- 上传文档按钮 -->
            <button
              @click="showUploadModal = true"
              class="btn-primary px-4 py-2 rounded-lg"
            >
              <DocumentPlusIcon class="w-5 h-5 mr-2" />
              {{ $t('knowledge.upload') }}
            </button>
            
            <!-- 搜索按钮 -->
            <button
              @click="toggleSearch"
              class="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700"
              :title="$t('knowledge.search')"
            >
              <MagnifyingGlassIcon class="w-5 h-5 text-gray-600 dark:text-gray-400" />
            </button>
            
            <!-- 设置按钮 -->
            <button
              @click="showKnowledgeBaseConfig = true"
              class="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700"
              :title="$t('common.settings')"
            >
              <CogIcon class="w-5 h-5 text-gray-600 dark:text-gray-400" />
            </button>
          </div>
        </div>

        <!-- 搜索栏 -->
        <div v-if="showSearchBar" class="mt-4">
          <div class="relative">
            <MagnifyingGlassIcon class="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
            <input
              ref="searchInput"
              v-model="searchQuery"
              type="text"
              :placeholder="$t('knowledge.search')"
              class="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500"
              @keydown.enter="performSearch"
              @keydown.escape="toggleSearch"
            />
          </div>
        </div>
      </div>

      <!-- 内容区域 -->
      <div class="knowledge-content flex-1 overflow-hidden">
        <!-- 欢迎页面 -->
        <div v-if="!currentKnowledgeBase" class="h-full flex items-center justify-center bg-gray-50 dark:bg-gray-900">
          <div class="text-center">
            <BookOpenIcon class="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">
              {{ $t('knowledge.welcome') }}
            </h3>
            <p class="text-gray-500 dark:text-gray-400 mb-4">
              {{ $t('knowledge.welcomeMessage') }}
            </p>
            <button
              @click="showCreateModal = true"
              class="btn-primary px-4 py-2 rounded-lg"
            >
              {{ $t('knowledge.create') }}
            </button>
          </div>
        </div>

        <!-- 知识库内容 -->
        <div v-else class="h-full flex">
          <!-- 文档列表 -->
          <div class="documents-panel flex-1 bg-white dark:bg-gray-800">
            <!-- 文档列表头部 -->
            <div class="documents-header p-4 border-b border-gray-200 dark:border-gray-700">
              <div class="flex items-center justify-between">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white">
                  {{ $t('knowledge.documents') }}
                </h3>
                <div class="flex items-center space-x-2">
                  <!-- 视图切换 -->
                  <div class="flex items-center bg-gray-100 dark:bg-gray-700 rounded-lg p-1">
                    <button
                      @click="viewMode = 'list'"
                      class="p-1 rounded"
                      :class="[
                        viewMode === 'list'
                          ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white'
                          : 'text-gray-600 dark:text-gray-400'
                      ]"
                    >
                      <ListBulletIcon class="w-4 h-4" />
                    </button>
                    <button
                      @click="viewMode = 'grid'"
                      class="p-1 rounded"
                      :class="[
                        viewMode === 'grid'
                          ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white'
                          : 'text-gray-600 dark:text-gray-400'
                      ]"
                    >
                      <Squares2X2Icon class="w-4 h-4" />
                    </button>
                  </div>
                </div>
              </div>
            </div>

            <!-- 文档列表内容 -->
            <div class="documents-content flex-1 overflow-y-auto p-4">
              <div v-if="currentDocuments.length === 0" class="text-center text-gray-500 dark:text-gray-400 py-8">
                {{ $t('knowledge.noDocuments') }}
              </div>
              
              <!-- 列表视图 -->
              <div v-else-if="viewMode === 'list'" class="space-y-2">
                <DocumentListItem
                  v-for="document in currentDocuments"
                  :key="document.id"
                  :document="document"
                  @select="selectDocument"
                  @delete="deleteDocument"
                  @analyze="analyzeDocument"
                />
              </div>
              
              <!-- 网格视图 -->
              <div v-else class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                <DocumentGridItem
                  v-for="document in currentDocuments"
                  :key="document.id"
                  :document="document"
                  @select="selectDocument"
                  @delete="deleteDocument"
                  @analyze="analyzeDocument"
                />
              </div>
            </div>
          </div>

          <!-- 搜索结果面板 -->
          <div v-if="showSearchResults" class="search-results-panel w-96 bg-gray-50 dark:bg-gray-900 border-l border-gray-200 dark:border-gray-700">
            <SearchResultsPanel
              :results="searchResults"
              :loading="searchLoading"
              @close="showSearchResults = false"
              @select="selectSearchResult"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 创建知识库弹窗 -->
    <CreateKnowledgeBaseModal
      v-if="showCreateModal"
      @close="showCreateModal = false"
      @create="createKnowledgeBase"
    />

    <!-- 上传文档弹窗 -->
    <UploadDocumentModal
      v-if="showUploadModal && currentKnowledgeBase"
      :knowledge-base="currentKnowledgeBase"
      @close="showUploadModal = false"
      @upload="uploadDocuments"
    />

    <!-- 知识库配置弹窗 -->
    <KnowledgeBaseConfigModal
      v-if="showKnowledgeBaseConfig && currentKnowledgeBase"
      :knowledge-base="currentKnowledgeBase"
      @close="showKnowledgeBaseConfig = false"
      @save="updateKnowledgeBaseConfig"
    />

    <!-- 知识库菜单 -->
    <KnowledgeBaseContextMenu
      v-if="activeKnowledgeBaseMenu"
      :knowledge-base-id="activeKnowledgeBaseMenu"
      @close="activeKnowledgeBaseMenu = null"
      @rename="renameKnowledgeBase"
      @delete="deleteKnowledgeBase"
      @export="exportKnowledgeBase"
      @backup="backupKnowledgeBase"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch, nextTick } from 'vue'
import { useI18n } from 'vue-i18n'
import { useKnowledgeStore } from '@/stores/knowledge'
import type { KnowledgeBase, Document } from '@/types'
import {
  PlusIcon,
  MagnifyingGlassIcon,
  EllipsisVerticalIcon,
  CogIcon,
  DocumentPlusIcon,
  BookOpenIcon,
  ListBulletIcon,
  Squares2X2Icon
} from '@heroicons/vue/24/outline'
import DocumentListItem from '@/components/knowledge/DocumentListItem.vue'
import DocumentGridItem from '@/components/knowledge/DocumentGridItem.vue'
import SearchResultsPanel from '@/components/knowledge/SearchResultsPanel.vue'
import CreateKnowledgeBaseModal from '@/components/knowledge/CreateKnowledgeBaseModal.vue'
import UploadDocumentModal from '@/components/knowledge/UploadDocumentModal.vue'
import KnowledgeBaseConfigModal from '@/components/knowledge/KnowledgeBaseConfigModal.vue'
import KnowledgeBaseContextMenu from '@/components/knowledge/KnowledgeBaseContextMenu.vue'

const { t } = useI18n()
const knowledgeStore = useKnowledgeStore()

// 状态
const knowledgeSearchQuery = ref('')
const searchQuery = ref('')
const showSearchBar = ref(false)
const showSearchResults = ref(false)
const searchResults = ref([])
const searchLoading = ref(false)
const viewMode = ref<'list' | 'grid'>('list')
const showCreateModal = ref(false)
const showUploadModal = ref(false)
const showKnowledgeBaseConfig = ref(false)
const activeKnowledgeBaseMenu = ref<string | null>(null)
const searchInput = ref<HTMLInputElement>()

// 计算属性
const knowledgeBases = computed(() => knowledgeStore.knowledgeBases)
const currentKnowledgeBaseId = computed(() => knowledgeStore.currentKnowledgeBaseId)
const currentKnowledgeBase = computed(() => 
  knowledgeBases.value.find(kb => kb.id === currentKnowledgeBaseId.value)
)
const currentDocuments = computed(() => 
  currentKnowledgeBaseId.value ? knowledgeStore.documents[currentKnowledgeBaseId.value] || [] : []
)

const filteredKnowledgeBases = computed(() => {
  if (!knowledgeSearchQuery.value) return knowledgeBases.value
  
  const query = knowledgeSearchQuery.value.toLowerCase()
  return knowledgeBases.value.filter(kb =>
    kb.name.toLowerCase().includes(query) ||
    kb.description?.toLowerCase().includes(query)
  )
})

// 方法
const selectKnowledgeBase = (knowledgeBaseId: string) => {
  knowledgeStore.setCurrentKnowledgeBase(knowledgeBaseId)
}

const toggleSearch = async () => {
  showSearchBar.value = !showSearchBar.value
  
  if (showSearchBar.value) {
    await nextTick()
    searchInput.value?.focus()
  } else {
    searchQuery.value = ''
    showSearchResults.value = false
  }
}

const performSearch = async () => {
  if (!searchQuery.value.trim() || !currentKnowledgeBaseId.value) return
  
  searchLoading.value = true
  showSearchResults.value = true
  
  try {
    const results = await knowledgeStore.searchKnowledge({
      query: searchQuery.value,
      knowledgeBaseIds: [currentKnowledgeBaseId.value],
      limit: 20
    })
    searchResults.value = results
  } catch (error) {
    console.error('搜索失败:', error)
  } finally {
    searchLoading.value = false
  }
}

const selectDocument = (document: Document) => {
  // 选择文档，可以打开文档详情或预览
  console.log('选择文档:', document)
}

const deleteDocument = async (documentId: string) => {
  try {
    await knowledgeStore.deleteDocument(documentId)
  } catch (error) {
    console.error('删除文档失败:', error)
  }
}

const analyzeDocument = async (documentId: string) => {
  try {
    await knowledgeStore.analyzeDocument(documentId)
  } catch (error) {
    console.error('分析文档失败:', error)
  }
}

const selectSearchResult = (result: any) => {
  // 处理搜索结果选择
  console.log('选择搜索结果:', result)
}

const createKnowledgeBase = async (data: Partial<KnowledgeBase>) => {
  try {
    await knowledgeStore.createKnowledgeBase(data)
    showCreateModal.value = false
  } catch (error) {
    console.error('创建知识库失败:', error)
  }
}

const uploadDocuments = async (files: File[], options: any) => {
  if (!currentKnowledgeBaseId.value) return
  
  try {
    await knowledgeStore.uploadDocuments(currentKnowledgeBaseId.value, files, options)
    showUploadModal.value = false
  } catch (error) {
    console.error('上传文档失败:', error)
  }
}

const updateKnowledgeBaseConfig = async (config: any) => {
  if (!currentKnowledgeBaseId.value) return
  
  try {
    await knowledgeStore.updateKnowledgeBase(currentKnowledgeBaseId.value, config)
    showKnowledgeBaseConfig.value = false
  } catch (error) {
    console.error('更新知识库配置失败:', error)
  }
}

const toggleKnowledgeBaseMenu = (knowledgeBaseId: string) => {
  activeKnowledgeBaseMenu.value = activeKnowledgeBaseMenu.value === knowledgeBaseId ? null : knowledgeBaseId
}

const renameKnowledgeBase = async (knowledgeBaseId: string, newName: string) => {
  try {
    await knowledgeStore.updateKnowledgeBase(knowledgeBaseId, { name: newName })
    activeKnowledgeBaseMenu.value = null
  } catch (error) {
    console.error('重命名知识库失败:', error)
  }
}

const deleteKnowledgeBase = async (knowledgeBaseId: string) => {
  try {
    await knowledgeStore.deleteKnowledgeBase(knowledgeBaseId)
    activeKnowledgeBaseMenu.value = null
  } catch (error) {
    console.error('删除知识库失败:', error)
  }
}

const exportKnowledgeBase = async (knowledgeBaseId: string) => {
  try {
    await knowledgeStore.exportKnowledgeBase(knowledgeBaseId, { format: 'json' })
    activeKnowledgeBaseMenu.value = null
  } catch (error) {
    console.error('导出知识库失败:', error)
  }
}

const backupKnowledgeBase = async (knowledgeBaseId: string) => {
  try {
    await knowledgeStore.backupKnowledgeBase(knowledgeBaseId)
    activeKnowledgeBaseMenu.value = null
  } catch (error) {
    console.error('备份知识库失败:', error)
  }
}

const formatDate = (timestamp: number) => {
  const date = new Date(timestamp)
  return date.toLocaleDateString('zh-CN', { 
    year: 'numeric', 
    month: 'short', 
    day: 'numeric' 
  })
}

const formatSize = (bytes: number) => {
  if (bytes === 0) return '0 B'
  
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
}

// 生命周期
onMounted(async () => {
  await knowledgeStore.loadKnowledgeBases()
  
  // 如果没有当前知识库且有知识库列表，选择第一个
  if (!currentKnowledgeBaseId.value && knowledgeBases.value.length > 0) {
    knowledgeStore.setCurrentKnowledgeBase(knowledgeBases.value[0].id)
  }
})

// 监听知识库变化，加载文档
watch(currentKnowledgeBaseId, async (newKnowledgeBaseId) => {
  if (newKnowledgeBaseId) {
    await knowledgeStore.loadDocuments(newKnowledgeBaseId)
  }
})
</script>

<style scoped>
.knowledge-view {
  height: calc(100vh - 8rem);
}

.knowledge-item {
  transition: all 0.2s ease;
}

.knowledge-item:hover {
  transform: translateY(-1px);
}

/* 滚动条样式 */
.knowledge-list::-webkit-scrollbar,
.documents-content::-webkit-scrollbar {
  width: 6px;
}

.knowledge-list::-webkit-scrollbar-track,
.documents-content::-webkit-scrollbar-track {
  background: transparent;
}

.knowledge-list::-webkit-scrollbar-thumb,
.documents-content::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}

.dark .knowledge-list::-webkit-scrollbar-thumb,
.dark .documents-content::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .knowledge-sidebar {
    width: 100%;
    position: absolute;
    left: 0;
    top: 0;
    z-index: 10;
    transform: translateX(-100%);
    transition: transform 0.3s ease;
  }
  
  .knowledge-sidebar.open {
    transform: translateX(0);
  }
  
  .search-results-panel {
    width: 100%;
    position: absolute;
    right: 0;
    top: 0;
    z-index: 20;
  }
}
</style>
