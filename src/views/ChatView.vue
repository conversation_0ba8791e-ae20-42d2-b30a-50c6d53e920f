<template>
  <div class="chat-view h-full flex">
    <!-- 会话列表侧边栏 -->
    <div class="chat-sidebar w-80 bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 flex flex-col">
      <!-- 侧边栏头部 -->
      <div class="sidebar-header p-4 border-b border-gray-200 dark:border-gray-700">
        <div class="flex items-center justify-between mb-4">
          <h2 class="text-lg font-semibold text-gray-900 dark:text-white">
            {{ $t('chat.sessions') }}
          </h2>
          <button
            @click="createNewSession"
            class="btn-primary p-2 rounded-lg"
            :title="$t('chat.newChat')"
          >
            <PlusIcon class="w-5 h-5" />
          </button>
        </div>
        
        <!-- 搜索框 -->
        <div class="relative">
          <MagnifyingGlassIcon class="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
          <input
            v-model="sessionSearchQuery"
            type="text"
            :placeholder="$t('chat.searchMessages')"
            class="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>
      </div>

      <!-- 会话列表 -->
      <div class="session-list flex-1 overflow-y-auto">
        <div v-if="filteredSessions.length === 0" class="p-4 text-center text-gray-500 dark:text-gray-400">
          {{ $t('chat.noSessions') }}
        </div>
        
        <div v-else class="p-2 space-y-1">
          <div
            v-for="session in filteredSessions"
            :key="session.id"
            @click="selectSession(session.id)"
            class="session-item p-3 rounded-lg cursor-pointer transition-colors"
            :class="[
              currentSessionId === session.id
                ? 'bg-blue-50 text-blue-700 dark:bg-blue-900/50 dark:text-blue-400'
                : 'hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-900 dark:text-white'
            ]"
          >
            <div class="flex items-start justify-between">
              <div class="flex-1 min-w-0">
                <h3 class="font-medium truncate">{{ session.title }}</h3>
                <p class="text-sm text-gray-500 dark:text-gray-400 truncate mt-1">
                  {{ session.description || $t('chat.noMessages') }}
                </p>
                <div class="flex items-center justify-between mt-2 text-xs text-gray-400">
                  <span>{{ formatDate(session.updatedAt) }}</span>
                  <span>{{ session.messageCount }} {{ $t('chat.messages') }}</span>
                </div>
              </div>
              
              <!-- 会话操作按钮 -->
              <div class="flex items-center space-x-1 ml-2">
                <button
                  @click.stop="toggleSessionMenu(session.id)"
                  class="p-1 rounded hover:bg-gray-200 dark:hover:bg-gray-600"
                >
                  <EllipsisVerticalIcon class="w-4 h-4" />
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 聊天主区域 -->
    <div class="chat-main flex-1 flex flex-col">
      <!-- 聊天头部 -->
      <div v-if="currentSession" class="chat-header p-4 border-b border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-3">
            <h1 class="text-xl font-semibold text-gray-900 dark:text-white">
              {{ currentSession.title }}
            </h1>
            <span class="px-2 py-1 text-xs bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 rounded">
              {{ currentSession.model }}
            </span>
          </div>
          
          <div class="flex items-center space-x-2">
            <!-- 配置按钮 -->
            <button
              @click="showSessionConfig = true"
              class="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700"
              :title="$t('chat.config')"
            >
              <CogIcon class="w-5 h-5 text-gray-600 dark:text-gray-400" />
            </button>
            
            <!-- 清空对话按钮 -->
            <button
              @click="clearSession"
              class="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700"
              :title="$t('chat.clear')"
            >
              <TrashIcon class="w-5 h-5 text-gray-600 dark:text-gray-400" />
            </button>
          </div>
        </div>
      </div>

      <!-- 消息列表 -->
      <div class="messages-container flex-1 overflow-y-auto p-4 bg-gray-50 dark:bg-gray-900">
        <div v-if="!currentSession" class="h-full flex items-center justify-center">
          <div class="text-center">
            <ChatBubbleLeftIcon class="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">
              {{ $t('chat.welcome') }}
            </h3>
            <p class="text-gray-500 dark:text-gray-400 mb-4">
              {{ $t('chat.welcomeMessage') }}
            </p>
            <button
              @click="createNewSession"
              class="btn-primary px-4 py-2 rounded-lg"
            >
              {{ $t('chat.newChat') }}
            </button>
          </div>
        </div>

        <div v-else-if="currentMessages.length === 0" class="h-full flex items-center justify-center">
          <div class="text-center">
            <p class="text-gray-500 dark:text-gray-400">
              {{ $t('chat.noMessages') }}
            </p>
          </div>
        </div>

        <div v-else class="space-y-4 max-w-4xl mx-auto">
          <ChatMessage
            v-for="message in currentMessages"
            :key="message.id"
            :message="message"
            @regenerate="regenerateMessage"
            @delete="deleteMessage"
            @copy="copyMessage"
          />
          
          <!-- 正在生成指示器 -->
          <div v-if="isGenerating" class="flex items-center space-x-2 text-gray-500 dark:text-gray-400">
            <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500"></div>
            <span>{{ $t('chat.thinking') }}</span>
          </div>
        </div>
      </div>

      <!-- 输入区域 -->
      <div class="chat-input-area p-4 bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700">
        <ChatInput
          v-if="currentSession"
          :disabled="isGenerating"
          @send="sendMessage"
          @stop="stopGeneration"
        />
      </div>
    </div>

    <!-- 会话配置弹窗 -->
    <SessionConfigModal
      v-if="showSessionConfig && currentSession"
      :session="currentSession"
      @close="showSessionConfig = false"
      @save="updateSessionConfig"
    />

    <!-- 会话菜单 -->
    <SessionContextMenu
      v-if="activeSessionMenu"
      :session-id="activeSessionMenu"
      @close="activeSessionMenu = null"
      @rename="renameSession"
      @delete="deleteSession"
      @export="exportSession"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { useChatStore } from '@/stores/chat'
import { useModelStore } from '@/stores/model'
import type { ChatSession, ChatMessage as ChatMessageType, ChatInput as ChatInputType } from '@/types'
import {
  PlusIcon,
  MagnifyingGlassIcon,
  EllipsisVerticalIcon,
  CogIcon,
  TrashIcon,
  ChatBubbleLeftIcon
} from '@heroicons/vue/24/outline'
import ChatMessage from '@/components/chat/ChatMessage.vue'
import ChatInput from '@/components/chat/ChatInput.vue'
import SessionConfigModal from '@/components/chat/SessionConfigModal.vue'
import SessionContextMenu from '@/components/chat/SessionContextMenu.vue'

const { t } = useI18n()
const chatStore = useChatStore()
const modelStore = useModelStore()

// 状态
const sessionSearchQuery = ref('')
const showSessionConfig = ref(false)
const activeSessionMenu = ref<string | null>(null)

// 计算属性
const sessions = computed(() => chatStore.sessions)
const currentSessionId = computed(() => chatStore.currentSessionId)
const currentSession = computed(() => 
  sessions.value.find(s => s.id === currentSessionId.value)
)
const currentMessages = computed(() => 
  currentSessionId.value ? chatStore.messages[currentSessionId.value] || [] : []
)
const isGenerating = computed(() => chatStore.isGenerating)

const filteredSessions = computed(() => {
  if (!sessionSearchQuery.value) return sessions.value
  
  const query = sessionSearchQuery.value.toLowerCase()
  return sessions.value.filter(session =>
    session.title.toLowerCase().includes(query) ||
    session.description?.toLowerCase().includes(query)
  )
})

// 方法
const createNewSession = async () => {
  try {
    const defaultModel = modelStore.localModels[0]?.id || 'gpt-3.5-turbo'
    await chatStore.createSession({ model: defaultModel })
  } catch (error) {
    console.error('创建会话失败:', error)
  }
}

const selectSession = (sessionId: string) => {
  chatStore.setCurrentSession(sessionId)
}

const sendMessage = async (input: ChatInputType) => {
  if (!currentSessionId.value) return
  
  try {
    await chatStore.sendMessage(currentSessionId.value, input)
  } catch (error) {
    console.error('发送消息失败:', error)
  }
}

const stopGeneration = () => {
  if (currentSessionId.value) {
    chatStore.stopGeneration(currentSessionId.value)
  }
}

const regenerateMessage = async (messageId: string) => {
  try {
    await chatStore.regenerateMessage(messageId)
  } catch (error) {
    console.error('重新生成消息失败:', error)
  }
}

const deleteMessage = async (messageId: string) => {
  try {
    await chatStore.deleteMessage(messageId)
  } catch (error) {
    console.error('删除消息失败:', error)
  }
}

const copyMessage = (message: ChatMessageType) => {
  navigator.clipboard.writeText(message.content)
}

const clearSession = async () => {
  if (!currentSessionId.value) return
  
  try {
    await chatStore.clearSession(currentSessionId.value)
  } catch (error) {
    console.error('清空会话失败:', error)
  }
}

const toggleSessionMenu = (sessionId: string) => {
  activeSessionMenu.value = activeSessionMenu.value === sessionId ? null : sessionId
}

const renameSession = async (sessionId: string, newTitle: string) => {
  try {
    await chatStore.updateSession(sessionId, { title: newTitle })
    activeSessionMenu.value = null
  } catch (error) {
    console.error('重命名会话失败:', error)
  }
}

const deleteSession = async (sessionId: string) => {
  try {
    await chatStore.deleteSession(sessionId)
    activeSessionMenu.value = null
  } catch (error) {
    console.error('删除会话失败:', error)
  }
}

const exportSession = async (sessionId: string) => {
  try {
    await chatStore.exportSession(sessionId, { format: 'json', includeAttachments: true })
    activeSessionMenu.value = null
  } catch (error) {
    console.error('导出会话失败:', error)
  }
}

const updateSessionConfig = async (config: any) => {
  if (!currentSessionId.value) return
  
  try {
    await chatStore.updateSessionConfig(currentSessionId.value, config)
    showSessionConfig.value = false
  } catch (error) {
    console.error('更新会话配置失败:', error)
  }
}

const formatDate = (timestamp: number) => {
  const date = new Date(timestamp)
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  
  if (diff < 24 * 60 * 60 * 1000) {
    return date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })
  } else {
    return date.toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' })
  }
}

// 生命周期
onMounted(async () => {
  await chatStore.loadSessions()
  
  // 如果没有当前会话且有会话列表，选择第一个
  if (!currentSessionId.value && sessions.value.length > 0) {
    chatStore.setCurrentSession(sessions.value[0].id)
  }
})

// 监听会话变化，加载消息
watch(currentSessionId, async (newSessionId) => {
  if (newSessionId) {
    await chatStore.loadMessages(newSessionId)
  }
})
</script>

<style scoped>
.chat-view {
  height: calc(100vh - 8rem); /* 减去标题栏和状态栏高度 */
}

.session-item {
  transition: all 0.2s ease;
}

.session-item:hover {
  transform: translateY(-1px);
}

.messages-container {
  scroll-behavior: smooth;
}

/* 滚动条样式 */
.session-list::-webkit-scrollbar,
.messages-container::-webkit-scrollbar {
  width: 6px;
}

.session-list::-webkit-scrollbar-track,
.messages-container::-webkit-scrollbar-track {
  background: transparent;
}

.session-list::-webkit-scrollbar-thumb,
.messages-container::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}

.dark .session-list::-webkit-scrollbar-thumb,
.dark .messages-container::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .chat-sidebar {
    width: 100%;
    position: absolute;
    left: 0;
    top: 0;
    z-index: 10;
    transform: translateX(-100%);
    transition: transform 0.3s ease;
  }
  
  .chat-sidebar.open {
    transform: translateX(0);
  }
}
</style>
