<template>
  <div class="main-layout h-screen flex flex-col bg-gray-50 dark:bg-gray-900">
    <!-- 标题栏 -->
    <AppTitleBar />
    
    <!-- 主要内容区域 -->
    <div class="flex flex-1 overflow-hidden">
      <!-- 侧边栏 -->
      <AppSidebar 
        :collapsed="sidebarCollapsed"
        @toggle="toggleSidebar"
      />
      
      <!-- 内容区域 -->
      <main class="flex-1 flex flex-col overflow-hidden">
        <!-- 顶部工具栏 -->
        <AppToolbar />
        
        <!-- 页面内容 -->
        <div class="flex-1 overflow-auto">
          <router-view v-slot="{ Component, route }">
            <transition
              name="page"
              mode="out-in"
              appear
            >
              <component :is="Component" :key="route.path" />
            </transition>
          </router-view>
        </div>
      </main>
    </div>
    
    <!-- 状态栏 -->
    <AppStatusBar />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import AppTitleBar from '@/components/layout/AppTitleBar.vue'
import AppSidebar from '@/components/layout/AppSidebar.vue'
import AppToolbar from '@/components/layout/AppToolbar.vue'
import AppStatusBar from '@/components/layout/AppStatusBar.vue'

// 侧边栏状态
const sidebarCollapsed = ref(false)

// 切换侧边栏
const toggleSidebar = () => {
  sidebarCollapsed.value = !sidebarCollapsed.value
  // 保存状态到本地存储
  localStorage.setItem('sidebar-collapsed', sidebarCollapsed.value.toString())
}

// 键盘快捷键处理
const handleKeydown = (event: KeyboardEvent) => {
  // Ctrl/Cmd + B 切换侧边栏
  if ((event.ctrlKey || event.metaKey) && event.key === 'b') {
    event.preventDefault()
    toggleSidebar()
  }
  
  // Ctrl/Cmd + , 打开设置
  if ((event.ctrlKey || event.metaKey) && event.key === ',') {
    event.preventDefault()
    // 导航到设置页面
    // router.push('/settings')
  }
  
  // Ctrl/Cmd + N 新建聊天
  if ((event.ctrlKey || event.metaKey) && event.key === 'n') {
    event.preventDefault()
    // 创建新聊天
    // chatStore.createNewSession()
  }
}

// 组件挂载时初始化
onMounted(() => {
  // 恢复侧边栏状态
  const savedState = localStorage.getItem('sidebar-collapsed')
  if (savedState !== null) {
    sidebarCollapsed.value = savedState === 'true'
  }
  
  // 添加键盘事件监听
  document.addEventListener('keydown', handleKeydown)
})

// 组件卸载时清理
onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown)
})
</script>

<style scoped>
/* 页面切换动画 */
.page-enter-active,
.page-leave-active {
  transition: all 0.3s ease;
}

.page-enter-from {
  opacity: 0;
  transform: translateX(20px);
}

.page-leave-to {
  opacity: 0;
  transform: translateX(-20px);
}

/* 主布局样式 */
.main-layout {
  /* 防止内容溢出 */
  min-height: 100vh;
  max-height: 100vh;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .main-layout {
    /* 移动端适配 */
  }
}

/* 深色模式适配 */
.dark .main-layout {
  /* 深色模式特定样式 */
}

/* 无拖拽区域 */
.main-layout :deep(.no-drag) {
  -webkit-app-region: no-drag;
}

/* 拖拽区域 */
.main-layout :deep(.drag-region) {
  -webkit-app-region: drag;
}
</style>
