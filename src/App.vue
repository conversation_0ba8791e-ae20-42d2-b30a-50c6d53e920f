<template>
  <div id="app" :class="{ dark: isDark }">
    <!-- 全局消息提供者 -->
    <n-message-provider>
      <!-- 全局对话框提供者 -->
      <n-dialog-provider>
        <!-- 全局通知提供者 -->
        <n-notification-provider>
          <!-- 全局加载条提供者 -->
          <n-loading-bar-provider>
            <!-- 主应用内容 -->
            <div class="app-container">
              <!-- 应用布局 -->
              <router-view />
            </div>
          </n-loading-bar-provider>
        </n-notification-provider>
      </n-dialog-provider>
    </n-message-provider>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted } from 'vue'
import { useThemeStore } from '@/stores/theme'
import { useSystemStore } from '@/stores/system'

// 主题状态管理
const themeStore = useThemeStore()
const systemStore = useSystemStore()

// 计算属性：是否为深色模式
const isDark = computed(() => themeStore.isDark)

// 组件挂载时初始化
onMounted(async () => {
  try {
    // 初始化主题
    await themeStore.initTheme()
    
    // 初始化系统信息
    await systemStore.initSystem()
    
    // 检查应用更新
    await systemStore.checkForUpdates()
    
    console.log('AI Studio 应用初始化完成')
  } catch (error) {
    console.error('应用初始化失败:', error)
  }
})
</script>

<style>
/* 全局样式重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html,
body {
  height: 100%;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

#app {
  height: 100vh;
  overflow: hidden;
  transition: background-color 0.3s ease;
}

.app-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}

.dark ::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
}

/* 选择文本样式 */
::selection {
  background: rgba(59, 130, 246, 0.3);
}

.dark ::selection {
  background: rgba(59, 130, 246, 0.5);
}

/* 禁用文本选择的工具栏区域 */
.no-select {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* 动画类 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.slide-up-enter-active,
.slide-up-leave-active {
  transition: all 0.3s ease;
}

.slide-up-enter-from {
  transform: translateY(20px);
  opacity: 0;
}

.slide-up-leave-to {
  transform: translateY(-20px);
  opacity: 0;
}
</style>
