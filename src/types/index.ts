// 基础类型定义

// 通用响应类型
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  message?: string
  code?: number
  timestamp?: number
}

// 分页参数
export interface PaginationParams {
  page: number
  pageSize: number
  total?: number
}

// 分页响应
export interface PaginatedResponse<T> {
  items: T[]
  pagination: {
    page: number
    pageSize: number
    total: number
    totalPages: number
  }
}

// 用户信息
export interface UserInfo {
  id: string
  username: string
  email?: string
  avatar?: string
  nickname?: string
  createdAt: string
  updatedAt: string
}

// 主题类型
export type ThemeMode = 'light' | 'dark' | 'auto'

// 语言类型
export type Language = 'zh-CN' | 'en-US'

// 应用设置
export interface AppSettings {
  theme: ThemeMode
  language: Language
  autoStart: boolean
  minimizeToTray: boolean
  enableNotifications: boolean
  enableSounds: boolean
  fontSize: number
  fontFamily: string
}

// 系统信息
export interface SystemInfo {
  platform: string
  arch: string
  version: string
  totalMemory: number
  availableMemory: number
  cpuUsage: number
  gpuInfo?: GpuInfo[]
}

// GPU信息
export interface GpuInfo {
  id: string
  name: string
  vendor: string
  memory: number
  utilization: number
  temperature: number
}

// 文件信息
export interface FileInfo {
  name: string
  path: string
  size: number
  type: string
  lastModified: number
  isDirectory: boolean
}

// 错误类型
export interface AppError {
  code: string
  message: string
  details?: any
  timestamp: number
}

// 事件类型
export interface AppEvent<T = any> {
  type: string
  payload: T
  timestamp: number
}

// 组件属性基础类型
export interface BaseComponentProps {
  class?: string
  style?: string | Record<string, any>
}

// 表单验证规则
export interface ValidationRule {
  required?: boolean
  min?: number
  max?: number
  pattern?: RegExp
  validator?: (value: any) => boolean | string
  message?: string
}

// 表单字段
export interface FormField {
  name: string
  label: string
  type: 'text' | 'password' | 'email' | 'number' | 'textarea' | 'select' | 'checkbox' | 'radio'
  value?: any
  placeholder?: string
  options?: Array<{ label: string; value: any }>
  rules?: ValidationRule[]
  disabled?: boolean
  readonly?: boolean
}

// 菜单项
export interface MenuItem {
  key: string
  label: string
  icon?: string
  path?: string
  children?: MenuItem[]
  disabled?: boolean
  hidden?: boolean
  badge?: string | number
}

// 通知类型
export interface NotificationItem {
  id: string
  type: 'info' | 'success' | 'warning' | 'error'
  title: string
  content?: string
  duration?: number
  closable?: boolean
  timestamp: number
}

// 模态框配置
export interface ModalConfig {
  title?: string
  content?: string
  width?: number | string
  height?: number | string
  closable?: boolean
  maskClosable?: boolean
  keyboard?: boolean
  centered?: boolean
}

// 加载状态
export interface LoadingState {
  loading: boolean
  text?: string
  progress?: number
}

// 搜索参数
export interface SearchParams {
  keyword?: string
  filters?: Record<string, any>
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

// 导出所有类型
export * from './chat'
export * from './knowledge'
export * from './model'
export * from './network'
export * from './multimodal'
export * from './plugin'
