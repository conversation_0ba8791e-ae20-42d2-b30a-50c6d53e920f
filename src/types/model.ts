// 模型相关类型定义

// 模型类型
export type ModelType = 'llm' | 'embedding' | 'vision' | 'audio' | 'multimodal'

// 模型状态
export type ModelStatus = 'available' | 'downloading' | 'loading' | 'loaded' | 'unloading' | 'error' | 'unavailable'

// 量化类型
export type QuantizationType = 'none' | 'q4_0' | 'q4_1' | 'q5_0' | 'q5_1' | 'q8_0' | 'f16' | 'f32'

// 模型信息
export interface ModelInfo {
  id: string
  name: string
  displayName: string
  description?: string
  type: ModelType
  provider: string
  version: string
  size: number
  quantization: QuantizationType
  architecture: string
  contextLength: number
  parameters: number
  languages: string[]
  capabilities: ModelCapability[]
  requirements: ModelRequirements
  metadata: ModelMetadata
  isLocal: boolean
  isDownloaded: boolean
  status: ModelStatus
  downloadProgress?: number
  loadProgress?: number
  createdAt: number
  updatedAt: number
}

// 模型能力
export interface ModelCapability {
  type: 'text_generation' | 'text_embedding' | 'image_understanding' | 'code_generation' | 'function_calling'
  supported: boolean
  quality?: 'low' | 'medium' | 'high' | 'excellent'
}

// 模型要求
export interface ModelRequirements {
  minMemory: number
  recommendedMemory: number
  minVram?: number
  recommendedVram?: number
  cpuCores: number
  gpuSupport: boolean
  platforms: string[]
}

// 模型元数据
export interface ModelMetadata {
  huggingfaceId?: string
  license: string
  author: string
  tags: string[]
  paperUrl?: string
  repositoryUrl?: string
  demoUrl?: string
  downloadUrl?: string
  configUrl?: string
  tokenizerUrl?: string
}

// 下载任务
export interface DownloadTask {
  id: string
  modelId: string
  status: 'pending' | 'downloading' | 'paused' | 'completed' | 'failed' | 'cancelled'
  progress: number
  downloadedBytes: number
  totalBytes: number
  speed: number
  eta: number
  error?: string
  createdAt: number
  updatedAt: number
  completedAt?: number
}

// 模型配置
export interface ModelConfig {
  temperature: number
  topP: number
  topK: number
  maxTokens: number
  frequencyPenalty: number
  presencePenalty: number
  stopSequences: string[]
  seed?: number
  repeatPenalty: number
  contextLength: number
  batchSize: number
  threads: number
  gpuLayers: number
  useGpu: boolean
  useMmap: boolean
  useMlock: boolean
}

// 模型性能指标
export interface ModelPerformance {
  modelId: string
  tokensPerSecond: number
  latency: number
  memoryUsage: number
  vramUsage?: number
  cpuUsage: number
  gpuUsage?: number
  temperature: number
  powerConsumption?: number
  timestamp: number
}

// 模型部署配置
export interface DeploymentConfig {
  modelId: string
  name: string
  config: ModelConfig
  autoStart: boolean
  maxInstances: number
  healthCheck: {
    enabled: boolean
    interval: number
    timeout: number
    retries: number
  }
  scaling: {
    enabled: boolean
    minInstances: number
    maxInstances: number
    targetCpuUsage: number
    targetMemoryUsage: number
  }
}

// 模型实例
export interface ModelInstance {
  id: string
  modelId: string
  deploymentId: string
  status: 'starting' | 'running' | 'stopping' | 'stopped' | 'error'
  config: ModelConfig
  performance: ModelPerformance
  startedAt?: number
  stoppedAt?: number
  error?: string
}

// 模型搜索参数
export interface ModelSearchParams {
  keyword?: string
  type?: ModelType
  provider?: string
  minSize?: number
  maxSize?: number
  quantization?: QuantizationType
  languages?: string[]
  capabilities?: string[]
  isLocal?: boolean
  isDownloaded?: boolean
  sortBy?: 'name' | 'size' | 'parameters' | 'createdAt' | 'popularity'
  sortOrder?: 'asc' | 'desc'
  limit?: number
  offset?: number
}

// 模型比较
export interface ModelComparison {
  models: ModelInfo[]
  metrics: Array<{
    name: string
    unit: string
    values: Record<string, number>
  }>
  benchmarks: Array<{
    name: string
    description: string
    scores: Record<string, number>
  }>
}

// 模型推荐
export interface ModelRecommendation {
  modelId: string
  score: number
  reasons: string[]
  useCase: string
  pros: string[]
  cons: string[]
}

// 模型更新
export interface ModelUpdate {
  modelId: string
  currentVersion: string
  latestVersion: string
  changelog: string
  size: number
  isBreaking: boolean
  releaseDate: number
}

// 模型统计
export interface ModelStats {
  totalModels: number
  downloadedModels: number
  totalSize: number
  downloadedSize: number
  modelsByType: Record<ModelType, number>
  modelsByProvider: Record<string, number>
  recentDownloads: Array<{
    date: string
    count: number
    size: number
  }>
  popularModels: Array<{
    modelId: string
    downloads: number
    rating: number
  }>
}
