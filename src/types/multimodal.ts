// 多模态相关类型定义

// 媒体类型
export type MediaType = 'image' | 'audio' | 'video' | 'document'

// 处理状态
export type ProcessingStatus = 'pending' | 'processing' | 'completed' | 'failed'

// 图像处理任务
export interface ImageTask {
  id: string
  type: 'ocr' | 'object_detection' | 'face_recognition' | 'image_classification' | 'image_generation'
  inputPath: string
  outputPath?: string
  status: ProcessingStatus
  progress: number
  result?: ImageResult
  error?: string
  config: ImageConfig
  createdAt: number
  updatedAt: number
  completedAt?: number
}

// 图像配置
export interface ImageConfig {
  model?: string
  language?: string
  confidence?: number
  maxResults?: number
  outputFormat?: 'json' | 'text' | 'xml'
  preprocessing?: {
    resize?: { width: number; height: number }
    rotate?: number
    contrast?: number
    brightness?: number
    denoise?: boolean
  }
}

// 图像处理结果
export interface ImageResult {
  type: string
  data: any
  confidence: number
  processingTime: number
  metadata: {
    width: number
    height: number
    format: string
    size: number
    colorSpace: string
  }
}

// OCR结果
export interface OCRResult {
  text: string
  blocks: OCRBlock[]
  confidence: number
  language: string
  orientation: number
}

// OCR文本块
export interface OCRBlock {
  text: string
  confidence: number
  boundingBox: BoundingBox
  words: OCRWord[]
}

// OCR单词
export interface OCRWord {
  text: string
  confidence: number
  boundingBox: BoundingBox
}

// 边界框
export interface BoundingBox {
  x: number
  y: number
  width: number
  height: number
}

// 音频处理任务
export interface AudioTask {
  id: string
  type: 'transcription' | 'translation' | 'speaker_identification' | 'noise_reduction' | 'synthesis'
  inputPath: string
  outputPath?: string
  status: ProcessingStatus
  progress: number
  result?: AudioResult
  error?: string
  config: AudioConfig
  createdAt: number
  updatedAt: number
  completedAt?: number
}

// 音频配置
export interface AudioConfig {
  model?: string
  language?: string
  sampleRate?: number
  channels?: number
  bitRate?: number
  format?: 'wav' | 'mp3' | 'flac' | 'ogg'
  enableVAD?: boolean
  enableDiarization?: boolean
  maxSpeakers?: number
}

// 音频处理结果
export interface AudioResult {
  type: string
  data: any
  confidence: number
  processingTime: number
  metadata: {
    duration: number
    sampleRate: number
    channels: number
    bitRate: number
    format: string
    size: number
  }
}

// 语音转文字结果
export interface TranscriptionResult {
  text: string
  segments: TranscriptionSegment[]
  language: string
  confidence: number
  speakers?: SpeakerInfo[]
}

// 转录片段
export interface TranscriptionSegment {
  text: string
  start: number
  end: number
  confidence: number
  speakerId?: string
  words?: Array<{
    word: string
    start: number
    end: number
    confidence: number
  }>
}

// 说话人信息
export interface SpeakerInfo {
  id: string
  name?: string
  confidence: number
  segments: number[]
}

// 视频处理任务
export interface VideoTask {
  id: string
  type: 'frame_extraction' | 'object_tracking' | 'scene_detection' | 'subtitle_generation' | 'compression'
  inputPath: string
  outputPath?: string
  status: ProcessingStatus
  progress: number
  result?: VideoResult
  error?: string
  config: VideoConfig
  createdAt: number
  updatedAt: number
  completedAt?: number
}

// 视频配置
export interface VideoConfig {
  model?: string
  frameRate?: number
  resolution?: { width: number; height: number }
  quality?: 'low' | 'medium' | 'high' | 'ultra'
  codec?: 'h264' | 'h265' | 'vp9' | 'av1'
  extractFrames?: {
    interval: number
    format: 'jpg' | 'png'
    quality: number
  }
}

// 视频处理结果
export interface VideoResult {
  type: string
  data: any
  confidence: number
  processingTime: number
  metadata: {
    duration: number
    width: number
    height: number
    frameRate: number
    bitRate: number
    codec: string
    size: number
  }
}

// 文档处理任务
export interface DocumentTask {
  id: string
  type: 'text_extraction' | 'layout_analysis' | 'table_extraction' | 'form_recognition' | 'translation'
  inputPath: string
  outputPath?: string
  status: ProcessingStatus
  progress: number
  result?: DocumentResult
  error?: string
  config: DocumentConfig
  createdAt: number
  updatedAt: number
  completedAt?: number
}

// 文档配置
export interface DocumentConfig {
  model?: string
  language?: string
  outputFormat?: 'text' | 'markdown' | 'html' | 'json'
  preserveFormatting?: boolean
  extractImages?: boolean
  extractTables?: boolean
  ocrEnabled?: boolean
}

// 文档处理结果
export interface DocumentResult {
  type: string
  data: any
  confidence: number
  processingTime: number
  metadata: {
    pageCount: number
    wordCount: number
    characterCount: number
    language: string
    format: string
    size: number
  }
}

// 多模态统计
export interface MultimodalStats {
  totalTasks: number
  completedTasks: number
  failedTasks: number
  processingTime: number
  tasksByType: Record<string, number>
  tasksByStatus: Record<ProcessingStatus, number>
  averageProcessingTime: Record<string, number>
  recentActivity: Array<{
    date: string
    tasks: number
    processingTime: number
  }>
}

// 批处理任务
export interface BatchTask {
  id: string
  name: string
  type: MediaType
  operation: string
  inputPaths: string[]
  outputDir: string
  status: ProcessingStatus
  progress: number
  completedCount: number
  totalCount: number
  config: any
  results: any[]
  errors: string[]
  createdAt: number
  updatedAt: number
  completedAt?: number
}
