// 聊天相关类型定义

// 消息角色
export type MessageRole = 'user' | 'assistant' | 'system'

// 消息类型
export type MessageType = 'text' | 'image' | 'audio' | 'file' | 'code' | 'markdown'

// 消息状态
export type MessageStatus = 'sending' | 'sent' | 'failed' | 'received'

// 聊天消息
export interface ChatMessage {
  id: string
  sessionId: string
  role: MessageRole
  type: MessageType
  content: string
  attachments?: MessageAttachment[]
  metadata?: MessageMetadata
  status: MessageStatus
  timestamp: number
  tokens?: number
  model?: string
}

// 消息附件
export interface MessageAttachment {
  id: string
  type: 'image' | 'audio' | 'video' | 'file'
  name: string
  url: string
  size: number
  mimeType: string
  thumbnail?: string
}

// 消息元数据
export interface MessageMetadata {
  reasoning?: string
  sources?: string[]
  confidence?: number
  processingTime?: number
  tokenUsage?: {
    prompt: number
    completion: number
    total: number
  }
}

// 聊天会话
export interface ChatSession {
  id: string
  title: string
  description?: string
  model: string
  systemPrompt?: string
  temperature?: number
  maxTokens?: number
  topP?: number
  frequencyPenalty?: number
  presencePenalty?: number
  messageCount: number
  tokenCount: number
  createdAt: number
  updatedAt: number
  isArchived: boolean
  tags?: string[]
}

// 会话配置
export interface SessionConfig {
  model: string
  systemPrompt?: string
  temperature: number
  maxTokens: number
  topP: number
  frequencyPenalty: number
  presencePenalty: number
  enableRAG: boolean
  knowledgeBaseIds?: string[]
  enablePlugins: boolean
  enabledPluginIds?: string[]
}

// 聊天输入
export interface ChatInput {
  content: string
  attachments?: File[]
  replyToMessageId?: string
}

// 流式响应数据
export interface StreamResponse {
  id: string
  type: 'start' | 'chunk' | 'end' | 'error'
  content?: string
  metadata?: any
  error?: string
}

// 聊天统计
export interface ChatStats {
  totalSessions: number
  totalMessages: number
  totalTokens: number
  averageSessionLength: number
  mostUsedModel: string
  dailyUsage: Array<{
    date: string
    messages: number
    tokens: number
  }>
}

// 消息搜索参数
export interface MessageSearchParams {
  keyword?: string
  sessionId?: string
  role?: MessageRole
  type?: MessageType
  dateRange?: {
    start: number
    end: number
  }
  limit?: number
  offset?: number
}

// 消息搜索结果
export interface MessageSearchResult {
  message: ChatMessage
  highlights: string[]
  score: number
}

// 会话导出格式
export type ExportFormat = 'json' | 'markdown' | 'txt' | 'pdf'

// 会话导出选项
export interface ExportOptions {
  format: ExportFormat
  includeAttachments: boolean
  includeMetadata: boolean
  dateRange?: {
    start: number
    end: number
  }
}

// 聊天模板
export interface ChatTemplate {
  id: string
  name: string
  description?: string
  systemPrompt: string
  config: Partial<SessionConfig>
  category: string
  tags: string[]
  isBuiltIn: boolean
  createdAt: number
  updatedAt: number
}

// 快捷回复
export interface QuickReply {
  id: string
  title: string
  content: string
  category?: string
  hotkey?: string
  isGlobal: boolean
  createdAt: number
  updatedAt: number
}

// 聊天事件
export interface ChatEvent {
  type: 'message_sent' | 'message_received' | 'session_created' | 'session_updated' | 'typing_start' | 'typing_stop'
  sessionId: string
  messageId?: string
  data?: any
  timestamp: number
}
