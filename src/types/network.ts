// 网络共享相关类型定义

// 网络节点状态
export type NodeStatus = 'online' | 'offline' | 'connecting' | 'disconnected' | 'error'

// 连接类型
export type ConnectionType = 'wifi' | 'ethernet' | 'bluetooth' | 'usb'

// 网络节点
export interface NetworkNode {
  id: string
  name: string
  hostname: string
  ipAddress: string
  port: number
  macAddress?: string
  platform: string
  version: string
  status: NodeStatus
  connectionType: ConnectionType
  capabilities: NodeCapability[]
  resources: NodeResources
  lastSeen: number
  createdAt: number
  isLocal: boolean
  isTrusted: boolean
}

// 节点能力
export interface NodeCapability {
  type: 'model_sharing' | 'knowledge_sharing' | 'file_transfer' | 'compute_sharing' | 'storage_sharing'
  enabled: boolean
  version: string
  metadata?: Record<string, any>
}

// 节点资源
export interface NodeResources {
  cpu: {
    cores: number
    usage: number
    model: string
  }
  memory: {
    total: number
    available: number
    usage: number
  }
  gpu?: Array<{
    id: string
    name: string
    memory: number
    usage: number
  }>
  storage: {
    total: number
    available: number
    usage: number
  }
  network: {
    bandwidth: number
    latency: number
  }
}

// 文件传输任务
export interface TransferTask {
  id: string
  type: 'upload' | 'download'
  nodeId: string
  fileName: string
  filePath: string
  fileSize: number
  transferredBytes: number
  progress: number
  speed: number
  eta: number
  status: 'pending' | 'transferring' | 'paused' | 'completed' | 'failed' | 'cancelled'
  error?: string
  checksum?: string
  createdAt: number
  updatedAt: number
  completedAt?: number
}

// 共享资源
export interface SharedResource {
  id: string
  nodeId: string
  type: 'model' | 'knowledge_base' | 'file' | 'folder'
  name: string
  description?: string
  path: string
  size: number
  checksum: string
  permissions: ResourcePermissions
  metadata: Record<string, any>
  isPublic: boolean
  downloadCount: number
  createdAt: number
  updatedAt: number
}

// 资源权限
export interface ResourcePermissions {
  read: boolean
  write: boolean
  delete: boolean
  share: boolean
  allowedUsers?: string[]
  allowedGroups?: string[]
}

// P2P连接
export interface P2PConnection {
  id: string
  localNodeId: string
  remoteNodeId: string
  status: 'connecting' | 'connected' | 'disconnected' | 'error'
  protocol: 'webrtc' | 'tcp' | 'udp'
  encryption: boolean
  bandwidth: number
  latency: number
  packetsLost: number
  bytesTransferred: number
  connectedAt?: number
  disconnectedAt?: number
  error?: string
}

// 网络发现配置
export interface DiscoveryConfig {
  enabled: boolean
  protocol: 'mdns' | 'upnp' | 'broadcast'
  port: number
  interval: number
  timeout: number
  serviceName: string
  announceInterval: number
  maxNodes: number
}

// 网络安全配置
export interface SecurityConfig {
  encryption: {
    enabled: boolean
    algorithm: 'aes256' | 'chacha20'
    keyExchange: 'ecdh' | 'rsa'
  }
  authentication: {
    enabled: boolean
    method: 'password' | 'certificate' | 'token'
    credentials?: string
  }
  firewall: {
    enabled: boolean
    allowedPorts: number[]
    blockedIps: string[]
    allowedIps: string[]
  }
}

// 网络统计
export interface NetworkStats {
  totalNodes: number
  onlineNodes: number
  totalConnections: number
  activeTransfers: number
  totalBytesTransferred: number
  averageLatency: number
  averageBandwidth: number
  uptime: number
  errors: number
}

// 网络事件
export interface NetworkEvent {
  id: string
  type: 'node_discovered' | 'node_connected' | 'node_disconnected' | 'transfer_started' | 'transfer_completed' | 'error'
  nodeId?: string
  data?: any
  timestamp: number
  severity: 'info' | 'warning' | 'error'
}

// 同步任务
export interface SyncTask {
  id: string
  type: 'model' | 'knowledge_base' | 'settings' | 'full'
  sourceNodeId: string
  targetNodeId: string
  resourceIds: string[]
  status: 'pending' | 'syncing' | 'completed' | 'failed' | 'cancelled'
  progress: number
  syncedItems: number
  totalItems: number
  conflicts: SyncConflict[]
  createdAt: number
  updatedAt: number
  completedAt?: number
}

// 同步冲突
export interface SyncConflict {
  resourceId: string
  type: 'version' | 'permission' | 'size' | 'checksum'
  localVersion: any
  remoteVersion: any
  resolution: 'local' | 'remote' | 'merge' | 'skip'
  resolvedAt?: number
}

// 网络配置
export interface NetworkConfig {
  discovery: DiscoveryConfig
  security: SecurityConfig
  maxConnections: number
  connectionTimeout: number
  transferChunkSize: number
  compressionEnabled: boolean
  bandwidthLimit: number
  autoAcceptConnections: boolean
  trustedNodes: string[]
}
