// 知识库相关类型定义

// 知识库
export interface KnowledgeBase {
  id: string
  name: string
  description?: string
  embeddingModel: string
  chunkSize: number
  chunkOverlap: number
  documentCount: number
  vectorCount: number
  size: number
  isPublic: boolean
  tags: string[]
  createdAt: number
  updatedAt: number
}

// 文档类型
export type DocumentType = 'pdf' | 'docx' | 'txt' | 'md' | 'html' | 'csv' | 'xlsx' | 'pptx' | 'epub'

// 文档状态
export type DocumentStatus = 'uploading' | 'processing' | 'completed' | 'failed' | 'archived'

// 文档
export interface Document {
  id: string
  knowledgeBaseId: string
  name: string
  type: DocumentType
  size: number
  path: string
  url?: string
  status: DocumentStatus
  progress?: number
  chunkCount: number
  metadata: DocumentMetadata
  tags: string[]
  createdAt: number
  updatedAt: number
  processedAt?: number
}

// 文档元数据
export interface DocumentMetadata {
  title?: string
  author?: string
  subject?: string
  keywords?: string[]
  language?: string
  pageCount?: number
  wordCount?: number
  characterCount?: number
  encoding?: string
  mimeType: string
  checksum: string
}

// 文档块
export interface DocumentChunk {
  id: string
  documentId: string
  content: string
  embedding?: number[]
  metadata: ChunkMetadata
  position: number
  tokens: number
  createdAt: number
}

// 块元数据
export interface ChunkMetadata {
  page?: number
  section?: string
  heading?: string
  startOffset: number
  endOffset: number
  parentChunkId?: string
}

// 向量搜索参数
export interface VectorSearchParams {
  query: string
  knowledgeBaseIds?: string[]
  documentIds?: string[]
  limit?: number
  threshold?: number
  filters?: Record<string, any>
  includeMetadata?: boolean
}

// 搜索结果
export interface SearchResult {
  chunk: DocumentChunk
  document: Document
  score: number
  highlights: string[]
  distance: number
}

// 文档解析选项
export interface ParseOptions {
  chunkSize: number
  chunkOverlap: number
  preserveFormatting: boolean
  extractImages: boolean
  extractTables: boolean
  language?: string
  customSplitter?: string
}

// 批量上传任务
export interface UploadTask {
  id: string
  knowledgeBaseId: string
  files: File[]
  options: ParseOptions
  status: 'pending' | 'processing' | 'completed' | 'failed'
  progress: number
  processedCount: number
  totalCount: number
  errors: string[]
  createdAt: number
  updatedAt: number
}

// 知识库统计
export interface KnowledgeStats {
  totalKnowledgeBases: number
  totalDocuments: number
  totalChunks: number
  totalSize: number
  documentsByType: Record<DocumentType, number>
  recentActivity: Array<{
    date: string
    uploads: number
    searches: number
  }>
}

// 文档分析结果
export interface DocumentAnalysis {
  summary: string
  keyPoints: string[]
  topics: string[]
  entities: Array<{
    text: string
    type: string
    confidence: number
  }>
  sentiment?: {
    score: number
    label: 'positive' | 'negative' | 'neutral'
  }
  readability?: {
    score: number
    level: string
  }
}

// 知识库导出选项
export interface ExportOptions {
  format: 'json' | 'csv' | 'xlsx'
  includeContent: boolean
  includeMetadata: boolean
  includeEmbeddings: boolean
  documentIds?: string[]
}

// 知识库备份
export interface KnowledgeBackup {
  id: string
  knowledgeBaseId: string
  name: string
  description?: string
  size: number
  path: string
  version: string
  createdAt: number
  restoredAt?: number
}

// RAG配置
export interface RAGConfig {
  enabled: boolean
  knowledgeBaseIds: string[]
  searchParams: {
    limit: number
    threshold: number
    hybridSearch: boolean
    rerankResults: boolean
  }
  promptTemplate: string
  citationFormat: 'inline' | 'footnote' | 'none'
}

// 知识库同步状态
export interface SyncStatus {
  knowledgeBaseId: string
  lastSyncAt: number
  status: 'idle' | 'syncing' | 'error'
  progress: number
  error?: string
  changedDocuments: string[]
}

// 文档版本
export interface DocumentVersion {
  id: string
  documentId: string
  version: number
  changes: string
  size: number
  checksum: string
  createdAt: number
  createdBy: string
}
