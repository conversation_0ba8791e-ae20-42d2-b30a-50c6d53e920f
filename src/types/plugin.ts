// 插件相关类型定义

// 插件状态
export type PluginStatus = 'installed' | 'enabled' | 'disabled' | 'updating' | 'error' | 'uninstalled'

// 插件类型
export type PluginType = 'tool' | 'integration' | 'ui' | 'model' | 'data_source' | 'workflow'

// 插件权限
export type PluginPermission = 
  | 'file_system' 
  | 'network' 
  | 'clipboard' 
  | 'notifications' 
  | 'system_info' 
  | 'camera' 
  | 'microphone' 
  | 'location'

// 插件信息
export interface PluginInfo {
  id: string
  name: string
  displayName: string
  description: string
  version: string
  author: string
  homepage?: string
  repository?: string
  license: string
  type: PluginType
  category: string
  tags: string[]
  icon?: string
  screenshots?: string[]
  status: PluginStatus
  permissions: PluginPermission[]
  dependencies: PluginDependency[]
  config: PluginConfig
  manifest: PluginManifest
  stats: PluginStats
  isBuiltIn: boolean
  isVerified: boolean
  installPath?: string
  createdAt: number
  updatedAt: number
  installedAt?: number
}

// 插件依赖
export interface PluginDependency {
  id: string
  name: string
  version: string
  required: boolean
  installed: boolean
}

// 插件配置
export interface PluginConfig {
  enabled: boolean
  autoUpdate: boolean
  settings: Record<string, any>
  hotkeys: Record<string, string>
  ui: {
    showInToolbar: boolean
    showInMenu: boolean
    position: number
  }
}

// 插件清单
export interface PluginManifest {
  id: string
  name: string
  version: string
  description: string
  author: string
  main: string
  permissions: PluginPermission[]
  dependencies?: Record<string, string>
  engines: {
    aiStudio: string
    node?: string
  }
  scripts?: Record<string, string>
  files: string[]
  activationEvents: string[]
  contributes: PluginContributions
}

// 插件贡献点
export interface PluginContributions {
  commands?: PluginCommand[]
  menus?: PluginMenu[]
  keybindings?: PluginKeybinding[]
  settings?: PluginSetting[]
  views?: PluginView[]
  languages?: PluginLanguage[]
  themes?: PluginTheme[]
}

// 插件命令
export interface PluginCommand {
  id: string
  title: string
  description?: string
  category?: string
  icon?: string
  when?: string
}

// 插件菜单
export interface PluginMenu {
  id: string
  label: string
  command: string
  group?: string
  when?: string
  order?: number
}

// 插件快捷键
export interface PluginKeybinding {
  command: string
  key: string
  when?: string
  mac?: string
  linux?: string
  windows?: string
}

// 插件设置
export interface PluginSetting {
  id: string
  title: string
  description?: string
  type: 'string' | 'number' | 'boolean' | 'array' | 'object'
  default: any
  enum?: any[]
  minimum?: number
  maximum?: number
  pattern?: string
  required?: boolean
}

// 插件视图
export interface PluginView {
  id: string
  title: string
  when?: string
  type: 'panel' | 'sidebar' | 'modal' | 'tab'
  icon?: string
  size?: { width: number; height: number }
}

// 插件语言
export interface PluginLanguage {
  id: string
  aliases: string[]
  extensions: string[]
  configuration?: string
}

// 插件主题
export interface PluginTheme {
  id: string
  label: string
  uiTheme: 'light' | 'dark'
  path: string
}

// 插件统计
export interface PluginStats {
  downloads: number
  rating: number
  reviews: number
  lastUsed?: number
  usageCount: number
  errorCount: number
  performance: {
    loadTime: number
    memoryUsage: number
    cpuUsage: number
  }
}

// 插件市场
export interface PluginMarket {
  id: string
  name: string
  url: string
  description?: string
  isOfficial: boolean
  isEnabled: boolean
  lastSync?: number
  pluginCount: number
  categories: string[]
}

// 插件搜索参数
export interface PluginSearchParams {
  keyword?: string
  type?: PluginType
  category?: string
  tags?: string[]
  author?: string
  verified?: boolean
  minRating?: number
  sortBy?: 'name' | 'downloads' | 'rating' | 'updated' | 'created'
  sortOrder?: 'asc' | 'desc'
  limit?: number
  offset?: number
}

// 插件安装任务
export interface PluginInstallTask {
  id: string
  pluginId: string
  version: string
  status: 'pending' | 'downloading' | 'installing' | 'completed' | 'failed' | 'cancelled'
  progress: number
  downloadedBytes: number
  totalBytes: number
  error?: string
  createdAt: number
  updatedAt: number
  completedAt?: number
}

// 插件更新
export interface PluginUpdate {
  pluginId: string
  currentVersion: string
  latestVersion: string
  changelog: string
  size: number
  isBreaking: boolean
  releaseDate: number
  autoUpdate: boolean
}

// 插件事件
export interface PluginEvent {
  type: 'installed' | 'uninstalled' | 'enabled' | 'disabled' | 'updated' | 'error'
  pluginId: string
  version?: string
  data?: any
  timestamp: number
}

// 插件API上下文
export interface PluginContext {
  pluginId: string
  version: string
  installPath: string
  dataPath: string
  configPath: string
  logPath: string
  permissions: PluginPermission[]
  api: PluginAPI
}

// 插件API接口
export interface PluginAPI {
  // 系统API
  system: {
    getInfo(): Promise<any>
    showNotification(message: string, type?: string): Promise<void>
    openExternal(url: string): Promise<void>
  }
  
  // 文件系统API
  fs: {
    readFile(path: string): Promise<string>
    writeFile(path: string, content: string): Promise<void>
    exists(path: string): Promise<boolean>
    mkdir(path: string): Promise<void>
    readdir(path: string): Promise<string[]>
  }
  
  // 网络API
  http: {
    get(url: string, options?: any): Promise<any>
    post(url: string, data?: any, options?: any): Promise<any>
    put(url: string, data?: any, options?: any): Promise<any>
    delete(url: string, options?: any): Promise<any>
  }
  
  // UI API
  ui: {
    showDialog(options: any): Promise<any>
    showMessage(message: string, type?: string): Promise<void>
    createPanel(options: any): Promise<any>
    registerCommand(id: string, handler: Function): void
  }
  
  // 存储API
  storage: {
    get(key: string): Promise<any>
    set(key: string, value: any): Promise<void>
    delete(key: string): Promise<void>
    clear(): Promise<void>
  }
  
  // 事件API
  events: {
    on(event: string, handler: Function): void
    off(event: string, handler: Function): void
    emit(event: string, data?: any): void
  }
}
