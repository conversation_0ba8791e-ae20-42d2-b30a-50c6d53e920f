import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { SystemInfo } from '@/types'
import type { SystemState } from './types'
import { useTauri } from '@/core/tauri'

export const useSystemStore = defineStore('system', () => {
  const { invoke } = useTauri()

  // 状态
  const systemInfo = ref<SystemInfo | null>(null)
  const isOnline = ref(navigator.onLine)
  const updateAvailable = ref(false)
  const updateProgress = ref(0)
  const notifications = ref<SystemState['notifications']>([])

  // 计算属性
  const state = computed<SystemState>(() => ({
    systemInfo: systemInfo.value,
    isOnline: isOnline.value,
    updateAvailable: updateAvailable.value,
    updateProgress: updateProgress.value,
    notifications: notifications.value
  }))

  const unreadNotifications = computed(() => 
    notifications.value.filter(n => !n.read)
  )

  const notificationCount = computed(() => 
    unreadNotifications.value.length
  )

  // 动作
  const initSystem = async () => {
    try {
      // 获取系统信息
      await loadSystemInfo()
      
      // 监听网络状态变化
      window.addEventListener('online', () => {
        isOnline.value = true
        addNotification('info', '网络连接', '网络连接已恢复')
      })
      
      window.addEventListener('offline', () => {
        isOnline.value = false
        addNotification('warning', '网络连接', '网络连接已断开')
      })

      console.log('系统初始化完成')
    } catch (error) {
      console.error('系统初始化失败:', error)
      addNotification('error', '系统错误', '系统初始化失败')
    }
  }

  const loadSystemInfo = async () => {
    try {
      const info = await invoke<SystemInfo>('get_system_info')
      systemInfo.value = info
    } catch (error) {
      console.error('获取系统信息失败:', error)
      throw error
    }
  }

  const checkForUpdates = async () => {
    try {
      const hasUpdate = await invoke<boolean>('check_for_updates')
      updateAvailable.value = hasUpdate
      
      if (hasUpdate) {
        addNotification('info', '应用更新', '发现新版本，可以进行更新')
      }
      
      return hasUpdate
    } catch (error) {
      console.error('检查更新失败:', error)
      addNotification('error', '更新检查', '检查更新失败')
      return false
    }
  }

  const downloadUpdate = async () => {
    try {
      updateProgress.value = 0
      
      // 监听下载进度
      const unlisten = await invoke('download_update', {
        onProgress: (progress: number) => {
          updateProgress.value = progress
        }
      })

      addNotification('success', '应用更新', '更新下载完成，重启应用以应用更新')
      return unlisten
    } catch (error) {
      console.error('下载更新失败:', error)
      addNotification('error', '更新下载', '下载更新失败')
      throw error
    }
  }

  const installUpdate = async () => {
    try {
      await invoke('install_update')
      addNotification('success', '应用更新', '更新安装完成')
    } catch (error) {
      console.error('安装更新失败:', error)
      addNotification('error', '更新安装', '安装更新失败')
      throw error
    }
  }

  const restartApp = async () => {
    try {
      await invoke('restart_app')
    } catch (error) {
      console.error('重启应用失败:', error)
      addNotification('error', '应用重启', '重启应用失败')
      throw error
    }
  }

  const addNotification = (
    type: 'info' | 'success' | 'warning' | 'error',
    title: string,
    message: string
  ) => {
    const notification = {
      id: Date.now().toString(),
      type,
      title,
      message,
      timestamp: Date.now(),
      read: false
    }
    
    notifications.value.unshift(notification)
    
    // 限制通知数量
    if (notifications.value.length > 100) {
      notifications.value = notifications.value.slice(0, 100)
    }
    
    // 自动清理旧通知
    setTimeout(() => {
      removeNotification(notification.id)
    }, 30000) // 30秒后自动移除
  }

  const markNotificationAsRead = (id: string) => {
    const notification = notifications.value.find(n => n.id === id)
    if (notification) {
      notification.read = true
    }
  }

  const markAllNotificationsAsRead = () => {
    notifications.value.forEach(n => {
      n.read = true
    })
  }

  const removeNotification = (id: string) => {
    const index = notifications.value.findIndex(n => n.id === id)
    if (index > -1) {
      notifications.value.splice(index, 1)
    }
  }

  const clearAllNotifications = () => {
    notifications.value = []
  }

  const getPerformanceInfo = async () => {
    try {
      return await invoke('get_performance_info')
    } catch (error) {
      console.error('获取性能信息失败:', error)
      throw error
    }
  }

  const getMemoryUsage = async () => {
    try {
      return await invoke('get_memory_usage')
    } catch (error) {
      console.error('获取内存使用情况失败:', error)
      throw error
    }
  }

  const getCpuUsage = async () => {
    try {
      return await invoke('get_cpu_usage')
    } catch (error) {
      console.error('获取CPU使用情况失败:', error)
      throw error
    }
  }

  const getGpuInfo = async () => {
    try {
      return await invoke('get_gpu_info')
    } catch (error) {
      console.error('获取GPU信息失败:', error)
      throw error
    }
  }

  const clearCache = async () => {
    try {
      await invoke('clear_cache')
      addNotification('success', '缓存清理', '缓存清理完成')
    } catch (error) {
      console.error('清理缓存失败:', error)
      addNotification('error', '缓存清理', '清理缓存失败')
      throw error
    }
  }

  const exportLogs = async () => {
    try {
      const logPath = await invoke<string>('export_logs')
      addNotification('success', '日志导出', `日志已导出到: ${logPath}`)
      return logPath
    } catch (error) {
      console.error('导出日志失败:', error)
      addNotification('error', '日志导出', '导出日志失败')
      throw error
    }
  }

  return {
    // 状态
    systemInfo: readonly(systemInfo),
    isOnline: readonly(isOnline),
    updateAvailable: readonly(updateAvailable),
    updateProgress: readonly(updateProgress),
    notifications: readonly(notifications),
    state,

    // 计算属性
    unreadNotifications,
    notificationCount,

    // 动作
    initSystem,
    loadSystemInfo,
    checkForUpdates,
    downloadUpdate,
    installUpdate,
    restartApp,
    addNotification,
    markNotificationAsRead,
    markAllNotificationsAsRead,
    removeNotification,
    clearAllNotifications,
    getPerformanceInfo,
    getMemoryUsage,
    getCpuUsage,
    getGpuInfo,
    clearCache,
    exportLogs
  }
})
