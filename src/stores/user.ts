import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { UserInfo } from '@/types'
import type { UserState } from './types'
import { useTauri } from '@/core/tauri'

export const useUserStore = defineStore('user', () => {
  const { invoke } = useTauri()

  // 状态
  const isLoggedIn = ref(false)
  const userInfo = ref<UserInfo | null>(null)
  const preferences = ref<Record<string, any>>({})

  // 计算属性
  const state = computed<UserState>(() => ({
    isLoggedIn: isLoggedIn.value,
    userInfo: userInfo.value,
    preferences: preferences.value
  }))

  const userName = computed(() => {
    return userInfo.value?.nickname || userInfo.value?.username || '游客'
  })

  const userAvatar = computed(() => {
    return userInfo.value?.avatar || generateDefaultAvatar()
  })

  const userInitials = computed(() => {
    if (userInfo.value?.nickname) {
      return userInfo.value.nickname.charAt(0).toUpperCase()
    }
    if (userInfo.value?.username) {
      return userInfo.value.username.charAt(0).toUpperCase()
    }
    return 'U'
  })

  const isGuest = computed(() => {
    return !isLoggedIn.value || !userInfo.value
  })

  // 动作
  const login = async (credentials: { username: string; password: string }) => {
    try {
      const response = await invoke('user_login', credentials)
      
      if (response.success) {
        userInfo.value = response.data.user
        isLoggedIn.value = true
        
        // 保存登录状态
        saveAuthState()
        
        // 加载用户偏好设置
        await loadPreferences()
        
        return response.data
      } else {
        throw new Error(response.message || '登录失败')
      }
    } catch (error) {
      console.error('登录失败:', error)
      throw error
    }
  }

  const logout = async () => {
    try {
      await invoke('user_logout')
      
      // 清除状态
      isLoggedIn.value = false
      userInfo.value = null
      preferences.value = {}
      
      // 清除本地存储
      clearAuthState()
      
    } catch (error) {
      console.error('退出登录失败:', error)
      throw error
    }
  }

  const register = async (userData: {
    username: string
    email: string
    password: string
    nickname?: string
  }) => {
    try {
      const response = await invoke('user_register', userData)
      
      if (response.success) {
        userInfo.value = response.data.user
        isLoggedIn.value = true
        
        // 保存登录状态
        saveAuthState()
        
        // 初始化用户偏好设置
        await initializePreferences()
        
        return response.data
      } else {
        throw new Error(response.message || '注册失败')
      }
    } catch (error) {
      console.error('注册失败:', error)
      throw error
    }
  }

  const updateProfile = async (updates: Partial<UserInfo>) => {
    try {
      const response = await invoke('user_update_profile', updates)
      
      if (response.success) {
        userInfo.value = { ...userInfo.value!, ...response.data }
        saveAuthState()
        return response.data
      } else {
        throw new Error(response.message || '更新资料失败')
      }
    } catch (error) {
      console.error('更新用户资料失败:', error)
      throw error
    }
  }

  const changePassword = async (passwordData: {
    currentPassword: string
    newPassword: string
  }) => {
    try {
      const response = await invoke('user_change_password', passwordData)
      
      if (response.success) {
        return response.data
      } else {
        throw new Error(response.message || '修改密码失败')
      }
    } catch (error) {
      console.error('修改密码失败:', error)
      throw error
    }
  }

  const uploadAvatar = async (file: File) => {
    try {
      // 将文件转换为base64
      const base64 = await fileToBase64(file)
      
      const response = await invoke('user_upload_avatar', { avatar: base64 })
      
      if (response.success) {
        if (userInfo.value) {
          userInfo.value.avatar = response.data.avatarUrl
        }
        saveAuthState()
        return response.data.avatarUrl
      } else {
        throw new Error(response.message || '上传头像失败')
      }
    } catch (error) {
      console.error('上传头像失败:', error)
      throw error
    }
  }

  const deleteAccount = async (password: string) => {
    try {
      const response = await invoke('user_delete_account', { password })
      
      if (response.success) {
        // 清除所有状态
        await logout()
        return response.data
      } else {
        throw new Error(response.message || '删除账户失败')
      }
    } catch (error) {
      console.error('删除账户失败:', error)
      throw error
    }
  }

  // 偏好设置相关
  const loadPreferences = async () => {
    try {
      const response = await invoke('user_get_preferences')
      
      if (response.success) {
        preferences.value = response.data || {}
      }
    } catch (error) {
      console.error('加载用户偏好设置失败:', error)
    }
  }

  const updatePreferences = async (updates: Record<string, any>) => {
    try {
      const newPreferences = { ...preferences.value, ...updates }
      
      const response = await invoke('user_update_preferences', newPreferences)
      
      if (response.success) {
        preferences.value = newPreferences
        savePreferences()
        return newPreferences
      } else {
        throw new Error(response.message || '更新偏好设置失败')
      }
    } catch (error) {
      console.error('更新用户偏好设置失败:', error)
      throw error
    }
  }

  const getPreference = (key: string, defaultValue?: any) => {
    return preferences.value[key] ?? defaultValue
  }

  const setPreference = async (key: string, value: any) => {
    await updatePreferences({ [key]: value })
  }

  const initializePreferences = async () => {
    const defaultPreferences = {
      theme: 'auto',
      language: 'zh-CN',
      notifications: true,
      sounds: true,
      autoSave: true,
      fontSize: 14,
      fontFamily: 'Inter'
    }
    
    await updatePreferences(defaultPreferences)
  }

  // 本地存储相关
  const saveAuthState = () => {
    try {
      const authData = {
        isLoggedIn: isLoggedIn.value,
        userInfo: userInfo.value
      }
      localStorage.setItem('auth-state', JSON.stringify(authData))
    } catch (error) {
      console.warn('无法保存认证状态到本地存储:', error)
    }
  }

  const loadAuthState = () => {
    try {
      const saved = localStorage.getItem('auth-state')
      if (saved) {
        const authData = JSON.parse(saved)
        isLoggedIn.value = authData.isLoggedIn || false
        userInfo.value = authData.userInfo || null
      }
    } catch (error) {
      console.warn('无法从本地存储加载认证状态:', error)
    }
  }

  const clearAuthState = () => {
    try {
      localStorage.removeItem('auth-state')
      localStorage.removeItem('user-preferences')
    } catch (error) {
      console.warn('无法清除本地存储的认证状态:', error)
    }
  }

  const savePreferences = () => {
    try {
      localStorage.setItem('user-preferences', JSON.stringify(preferences.value))
    } catch (error) {
      console.warn('无法保存用户偏好设置到本地存储:', error)
    }
  }

  const loadLocalPreferences = () => {
    try {
      const saved = localStorage.getItem('user-preferences')
      if (saved) {
        preferences.value = JSON.parse(saved)
      }
    } catch (error) {
      console.warn('无法从本地存储加载用户偏好设置:', error)
    }
  }

  // 工具方法
  const generateDefaultAvatar = () => {
    const colors = [
      '#3b82f6', '#ef4444', '#10b981', '#f59e0b',
      '#8b5cf6', '#06b6d4', '#84cc16', '#f97316'
    ]
    
    const initials = userInitials.value
    const colorIndex = initials.charCodeAt(0) % colors.length
    const color = colors[colorIndex]
    
    // 生成SVG头像
    const svg = `
      <svg width="40" height="40" viewBox="0 0 40 40" xmlns="http://www.w3.org/2000/svg">
        <rect width="40" height="40" fill="${color}" rx="20"/>
        <text x="20" y="26" text-anchor="middle" fill="white" font-family="Inter, sans-serif" font-size="16" font-weight="600">
          ${initials}
        </text>
      </svg>
    `
    
    return `data:image/svg+xml;base64,${btoa(svg)}`
  }

  const fileToBase64 = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.onload = () => resolve(reader.result as string)
      reader.onerror = reject
      reader.readAsDataURL(file)
    })
  }

  const validateEmail = (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email)
  }

  const validatePassword = (password: string): { valid: boolean; errors: string[] } => {
    const errors: string[] = []
    
    if (password.length < 8) {
      errors.push('密码长度至少8位')
    }
    
    if (!/[A-Z]/.test(password)) {
      errors.push('密码必须包含大写字母')
    }
    
    if (!/[a-z]/.test(password)) {
      errors.push('密码必须包含小写字母')
    }
    
    if (!/\d/.test(password)) {
      errors.push('密码必须包含数字')
    }
    
    if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
      errors.push('密码必须包含特殊字符')
    }
    
    return {
      valid: errors.length === 0,
      errors
    }
  }

  // 初始化
  const initUser = async () => {
    loadAuthState()
    loadLocalPreferences()
    
    // 如果已登录，尝试验证token并加载最新的用户信息
    if (isLoggedIn.value) {
      try {
        await verifyToken()
        await loadPreferences()
      } catch (error) {
        console.warn('验证用户token失败，需要重新登录:', error)
        await logout()
      }
    }
  }

  const verifyToken = async () => {
    try {
      const response = await invoke('user_verify_token')
      
      if (response.success) {
        userInfo.value = response.data.user
        return response.data
      } else {
        throw new Error('Token验证失败')
      }
    } catch (error) {
      console.error('验证token失败:', error)
      throw error
    }
  }

  return {
    // 状态
    isLoggedIn: readonly(isLoggedIn),
    userInfo: readonly(userInfo),
    preferences: readonly(preferences),
    state,

    // 计算属性
    userName,
    userAvatar,
    userInitials,
    isGuest,

    // 动作
    login,
    logout,
    register,
    updateProfile,
    changePassword,
    uploadAvatar,
    deleteAccount,

    // 偏好设置
    loadPreferences,
    updatePreferences,
    getPreference,
    setPreference,

    // 工具方法
    validateEmail,
    validatePassword,
    initUser,
    verifyToken
  }
})
