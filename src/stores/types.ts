// Store状态类型定义
import type {
  ThemeMode,
  Language,
  UserInfo,
  SystemInfo,
  ChatSession,
  ChatMessage,
  KnowledgeBase,
  Document,
  ModelInfo,
  NetworkNode,
  TransferTask,
  ImageTask,
  AudioTask,
  VideoTask,
  PluginInfo,
  AppSettings
} from '@/types'

// 主题状态
export interface ThemeState {
  mode: ThemeMode
  isDark: boolean
  primaryColor: string
  borderRadius: number
  fontSize: number
  fontFamily: string
}

// 语言状态
export interface LanguageState {
  current: Language
  available: Language[]
}

// 用户状态
export interface UserState {
  isLoggedIn: boolean
  userInfo: UserInfo | null
  preferences: Record<string, any>
}

// 系统状态
export interface SystemState {
  systemInfo: SystemInfo | null
  isOnline: boolean
  updateAvailable: boolean
  updateProgress: number
  notifications: Array<{
    id: string
    type: 'info' | 'success' | 'warning' | 'error'
    title: string
    message: string
    timestamp: number
    read: boolean
  }>
}

// 聊天状态
export interface ChatState {
  sessions: ChatSession[]
  currentSessionId: string | null
  messages: Record<string, ChatMessage[]>
  isGenerating: boolean
  streamingMessageId: string | null
  searchQuery: string
  selectedModel: string
  config: {
    temperature: number
    maxTokens: number
    topP: number
    frequencyPenalty: number
    presencePenalty: number
    enableRAG: boolean
    knowledgeBaseIds: string[]
  }
}

// 知识库状态
export interface KnowledgeState {
  knowledgeBases: KnowledgeBase[]
  currentKnowledgeBaseId: string | null
  documents: Record<string, Document[]>
  uploadTasks: Array<{
    id: string
    files: File[]
    progress: number
    status: 'pending' | 'uploading' | 'processing' | 'completed' | 'failed'
  }>
  searchResults: Array<{
    chunk: any
    document: Document
    score: number
  }>
  searchQuery: string
}

// 模型状态
export interface ModelState {
  localModels: ModelInfo[]
  remoteModels: ModelInfo[]
  downloadTasks: Array<{
    id: string
    modelId: string
    progress: number
    status: 'pending' | 'downloading' | 'completed' | 'failed'
    speed: number
    eta: number
  }>
  loadedModels: string[]
  currentModel: string | null
  searchQuery: string
  filters: {
    type: string[]
    provider: string[]
    quantization: string[]
  }
}

// 网络状态
export interface NetworkState {
  nodes: NetworkNode[]
  connections: Array<{
    nodeId: string
    status: 'connecting' | 'connected' | 'disconnected'
    lastConnected: number
  }>
  transfers: TransferTask[]
  sharedResources: Array<{
    id: string
    nodeId: string
    type: string
    name: string
    size: number
    shared: boolean
  }>
  discoveryEnabled: boolean
  sharingEnabled: boolean
}

// 多模态状态
export interface MultimodalState {
  imageTasks: ImageTask[]
  audioTasks: AudioTask[]
  videoTasks: VideoTask[]
  processingQueue: Array<{
    id: string
    type: 'image' | 'audio' | 'video'
    status: 'pending' | 'processing' | 'completed' | 'failed'
    progress: number
  }>
  supportedFormats: {
    image: string[]
    audio: string[]
    video: string[]
  }
  defaultSettings: {
    image: Record<string, any>
    audio: Record<string, any>
    video: Record<string, any>
  }
}

// 插件状态
export interface PluginState {
  installedPlugins: PluginInfo[]
  availablePlugins: PluginInfo[]
  enabledPlugins: string[]
  installTasks: Array<{
    id: string
    pluginId: string
    progress: number
    status: 'pending' | 'downloading' | 'installing' | 'completed' | 'failed'
  }>
  marketUrl: string
  searchQuery: string
  filters: {
    category: string[]
    type: string[]
    verified: boolean
  }
}

// 设置状态
export interface SettingsState {
  general: {
    autoStart: boolean
    minimizeToTray: boolean
    language: Language
    theme: ThemeMode
  }
  appearance: {
    fontSize: number
    fontFamily: string
    primaryColor: string
    borderRadius: number
  }
  notifications: {
    enabled: boolean
    sounds: boolean
    desktop: boolean
    email: boolean
  }
  privacy: {
    analytics: boolean
    crashReports: boolean
    errorReporting: boolean
  }
  advanced: {
    debugMode: boolean
    hardwareAcceleration: boolean
    experimentalFeatures: boolean
  }
}
