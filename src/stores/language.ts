import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { Language } from '@/types'
import type { LanguageState } from './types'
import { languageManager, supportedLanguages } from '@/locales'

export const useLanguageStore = defineStore('language', () => {
  // 状态
  const current = ref<Language>('zh-CN')
  const available = ref<Language[]>(['zh-CN', 'en-US'])

  // 计算属性
  const state = computed<LanguageState>(() => ({
    current: current.value,
    available: available.value
  }))

  const currentLanguageInfo = computed(() => {
    return supportedLanguages.find(lang => lang.code === current.value)
  })

  const isRTL = computed(() => {
    // 检查当前语言是否为从右到左的语言
    const rtlLanguages = ['ar', 'he', 'fa', 'ur']
    return rtlLanguages.some(lang => current.value.startsWith(lang))
  })

  // 动作
  const setLanguage = (language: Language) => {
    if (available.value.includes(language)) {
      current.value = language
      languageManager.setLanguage(language)
      saveToStorage()
      
      // 更新HTML lang属性
      document.documentElement.lang = language
      
      // 更新HTML dir属性
      document.documentElement.dir = isRTL.value ? 'rtl' : 'ltr'
    }
  }

  const toggleLanguage = () => {
    const currentIndex = available.value.indexOf(current.value)
    const nextIndex = (currentIndex + 1) % available.value.length
    setLanguage(available.value[nextIndex])
  }

  const addLanguage = (language: Language) => {
    if (!available.value.includes(language)) {
      available.value.push(language)
      saveToStorage()
    }
  }

  const removeLanguage = (language: Language) => {
    if (language !== current.value && available.value.length > 1) {
      const index = available.value.indexOf(language)
      if (index > -1) {
        available.value.splice(index, 1)
        saveToStorage()
      }
    }
  }

  const isSupported = (language: string): boolean => {
    return supportedLanguages.some(lang => lang.code === language)
  }

  const getLanguageInfo = (language?: Language) => {
    const targetLanguage = language || current.value
    return supportedLanguages.find(lang => lang.code === targetLanguage)
  }

  const getSupportedLanguages = () => {
    return [...supportedLanguages]
  }

  const detectBrowserLanguage = (): Language => {
    const browserLang = navigator.language || navigator.languages[0]
    
    // 精确匹配
    for (const lang of supportedLanguages) {
      if (browserLang === lang.code) {
        return lang.code
      }
    }
    
    // 语言代码匹配（忽略地区）
    const langCode = browserLang.split('-')[0]
    for (const lang of supportedLanguages) {
      if (lang.code.startsWith(langCode)) {
        return lang.code
      }
    }
    
    // 默认语言
    return 'zh-CN'
  }

  const saveToStorage = () => {
    try {
      const languageData = {
        current: current.value,
        available: available.value
      }
      localStorage.setItem('language-store', JSON.stringify(languageData))
    } catch (error) {
      console.warn('无法保存语言设置到本地存储:', error)
    }
  }

  const loadFromStorage = () => {
    try {
      const saved = localStorage.getItem('language-store')
      if (saved) {
        const languageData = JSON.parse(saved)
        current.value = languageData.current || 'zh-CN'
        available.value = languageData.available || ['zh-CN', 'en-US']
      }
    } catch (error) {
      console.warn('无法从本地存储加载语言设置:', error)
    }
  }

  const initLanguage = () => {
    loadFromStorage()
    
    // 如果没有保存的语言设置，尝试检测浏览器语言
    if (!localStorage.getItem('language-store')) {
      const detectedLanguage = detectBrowserLanguage()
      if (isSupported(detectedLanguage)) {
        setLanguage(detectedLanguage)
      }
    }
    
    // 设置HTML属性
    document.documentElement.lang = current.value
    document.documentElement.dir = isRTL.value ? 'rtl' : 'ltr'
    
    // 同步到语言管理器
    languageManager.setLanguage(current.value)
  }

  const resetLanguage = () => {
    current.value = 'zh-CN'
    available.value = ['zh-CN', 'en-US']
    saveToStorage()
    
    // 重置HTML属性
    document.documentElement.lang = current.value
    document.documentElement.dir = 'ltr'
    
    // 同步到语言管理器
    languageManager.setLanguage(current.value)
  }

  // 格式化相关方法
  const formatDate = (date: Date | number, options?: Intl.DateTimeFormatOptions) => {
    const locale = current.value === 'zh-CN' ? 'zh-CN' : 'en-US'
    return new Intl.DateTimeFormat(locale, options).format(new Date(date))
  }

  const formatNumber = (number: number, options?: Intl.NumberFormatOptions) => {
    const locale = current.value === 'zh-CN' ? 'zh-CN' : 'en-US'
    return new Intl.NumberFormat(locale, options).format(number)
  }

  const formatCurrency = (amount: number, currency: string = 'CNY') => {
    const locale = current.value === 'zh-CN' ? 'zh-CN' : 'en-US'
    return new Intl.NumberFormat(locale, {
      style: 'currency',
      currency
    }).format(amount)
  }

  const formatRelativeTime = (date: Date | number) => {
    const locale = current.value === 'zh-CN' ? 'zh-CN' : 'en-US'
    const rtf = new Intl.RelativeTimeFormat(locale, { numeric: 'auto' })
    
    const now = new Date()
    const target = new Date(date)
    const diffInSeconds = (target.getTime() - now.getTime()) / 1000
    
    const units: Array<[string, number]> = [
      ['year', 365 * 24 * 60 * 60],
      ['month', 30 * 24 * 60 * 60],
      ['day', 24 * 60 * 60],
      ['hour', 60 * 60],
      ['minute', 60],
      ['second', 1]
    ]
    
    for (const [unit, secondsInUnit] of units) {
      if (Math.abs(diffInSeconds) >= secondsInUnit) {
        const value = Math.round(diffInSeconds / secondsInUnit)
        return rtf.format(value, unit as Intl.RelativeTimeFormatUnit)
      }
    }
    
    return rtf.format(0, 'second')
  }

  // 文本方向相关方法
  const getTextDirection = () => {
    return isRTL.value ? 'rtl' : 'ltr'
  }

  const getTextAlign = () => {
    return isRTL.value ? 'right' : 'left'
  }

  const getFlexDirection = () => {
    return isRTL.value ? 'row-reverse' : 'row'
  }

  // 语言特定的配置
  const getLanguageConfig = () => {
    const config = {
      'zh-CN': {
        dateFormat: 'YYYY年MM月DD日',
        timeFormat: 'HH:mm:ss',
        numberFormat: '###,###.##',
        currency: 'CNY',
        firstDayOfWeek: 1, // 周一
        decimalSeparator: '.',
        thousandsSeparator: ','
      },
      'en-US': {
        dateFormat: 'MM/DD/YYYY',
        timeFormat: 'h:mm:ss A',
        numberFormat: '###,###.##',
        currency: 'USD',
        firstDayOfWeek: 0, // 周日
        decimalSeparator: '.',
        thousandsSeparator: ','
      }
    }
    
    return config[current.value] || config['zh-CN']
  }

  return {
    // 状态
    language: readonly(current),
    available: readonly(available),
    state,

    // 计算属性
    currentLanguageInfo,
    isRTL,

    // 动作
    setLanguage,
    toggleLanguage,
    addLanguage,
    removeLanguage,
    isSupported,
    getLanguageInfo,
    getSupportedLanguages,
    detectBrowserLanguage,
    initLanguage,
    resetLanguage,

    // 格式化方法
    formatDate,
    formatNumber,
    formatCurrency,
    formatRelativeTime,

    // 文本方向方法
    getTextDirection,
    getTextAlign,
    getFlexDirection,
    getLanguageConfig
  }
})
