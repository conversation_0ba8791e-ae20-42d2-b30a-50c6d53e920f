import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { ThemeMode } from '@/types'
import type { ThemeState } from './types'
import { themeManager } from '@/core/theme'

export const useThemeStore = defineStore('theme', () => {
  // 状态
  const mode = ref<ThemeMode>('auto')
  const primaryColor = ref('#3b82f6')
  const borderRadius = ref(8)
  const fontSize = ref(14)
  const fontFamily = ref('Inter, -apple-system, BlinkMacSystemFont, "Segoe UI", sans-serif')

  // 计算属性
  const isDark = computed(() => {
    switch (mode.value) {
      case 'dark':
        return true
      case 'light':
        return false
      case 'auto':
        return window.matchMedia('(prefers-color-scheme: dark)').matches
      default:
        return false
    }
  })

  const state = computed<ThemeState>(() => ({
    mode: mode.value,
    isDark: isDark.value,
    primaryColor: primaryColor.value,
    borderRadius: borderRadius.value,
    fontSize: fontSize.value,
    fontFamily: fontFamily.value
  }))

  // 动作
  const setMode = (newMode: ThemeMode) => {
    mode.value = newMode
    themeManager.setMode(newMode)
    saveToStorage()
  }

  const toggleMode = () => {
    const currentMode = mode.value
    if (currentMode === 'light') {
      setMode('dark')
    } else if (currentMode === 'dark') {
      setMode('light')
    } else {
      // auto模式下切换到相反的固定模式
      setMode(isDark.value ? 'light' : 'dark')
    }
  }

  const setPrimaryColor = (color: string) => {
    primaryColor.value = color
    themeManager.setPrimaryColor(color)
    saveToStorage()
  }

  const setBorderRadius = (radius: number) => {
    borderRadius.value = radius
    themeManager.setBorderRadius(radius)
    saveToStorage()
  }

  const setFontSize = (size: number) => {
    fontSize.value = size
    themeManager.setFontSize(size)
    saveToStorage()
  }

  const setFontFamily = (family: string) => {
    fontFamily.value = family
    themeManager.setFontFamily(family)
    saveToStorage()
  }

  const resetTheme = () => {
    mode.value = 'auto'
    primaryColor.value = '#3b82f6'
    borderRadius.value = 8
    fontSize.value = 14
    fontFamily.value = 'Inter, -apple-system, BlinkMacSystemFont, "Segoe UI", sans-serif'
    
    themeManager.resetConfig()
    saveToStorage()
  }

  const saveToStorage = () => {
    try {
      const themeData = {
        mode: mode.value,
        primaryColor: primaryColor.value,
        borderRadius: borderRadius.value,
        fontSize: fontSize.value,
        fontFamily: fontFamily.value
      }
      localStorage.setItem('theme-store', JSON.stringify(themeData))
    } catch (error) {
      console.warn('无法保存主题设置到本地存储:', error)
    }
  }

  const loadFromStorage = () => {
    try {
      const saved = localStorage.getItem('theme-store')
      if (saved) {
        const themeData = JSON.parse(saved)
        mode.value = themeData.mode || 'auto'
        primaryColor.value = themeData.primaryColor || '#3b82f6'
        borderRadius.value = themeData.borderRadius || 8
        fontSize.value = themeData.fontSize || 14
        fontFamily.value = themeData.fontFamily || 'Inter, -apple-system, BlinkMacSystemFont, "Segoe UI", sans-serif'
      }
    } catch (error) {
      console.warn('无法从本地存储加载主题设置:', error)
    }
  }

  const initTheme = async () => {
    loadFromStorage()
    
    // 同步到主题管理器
    themeManager.setConfig({
      mode: mode.value,
      primaryColor: primaryColor.value,
      borderRadius: borderRadius.value,
      fontSize: fontSize.value,
      fontFamily: fontFamily.value
    })

    // 监听系统主题变化
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
    mediaQuery.addEventListener('change', () => {
      if (mode.value === 'auto') {
        // 触发响应式更新
        mode.value = 'auto'
      }
    })
  }

  const getThemeConfig = () => {
    return {
      mode: mode.value,
      primaryColor: primaryColor.value,
      borderRadius: borderRadius.value,
      fontSize: fontSize.value,
      fontFamily: fontFamily.value
    }
  }

  const setThemeConfig = (config: Partial<ThemeState>) => {
    if (config.mode !== undefined) {
      setMode(config.mode)
    }
    if (config.primaryColor !== undefined) {
      setPrimaryColor(config.primaryColor)
    }
    if (config.borderRadius !== undefined) {
      setBorderRadius(config.borderRadius)
    }
    if (config.fontSize !== undefined) {
      setFontSize(config.fontSize)
    }
    if (config.fontFamily !== undefined) {
      setFontFamily(config.fontFamily)
    }
  }

  // 预设主题
  const presetThemes = [
    {
      name: '默认蓝色',
      primaryColor: '#3b82f6',
      borderRadius: 8
    },
    {
      name: '优雅紫色',
      primaryColor: '#8b5cf6',
      borderRadius: 12
    },
    {
      name: '活力绿色',
      primaryColor: '#10b981',
      borderRadius: 6
    },
    {
      name: '温暖橙色',
      primaryColor: '#f59e0b',
      borderRadius: 10
    },
    {
      name: '经典红色',
      primaryColor: '#ef4444',
      borderRadius: 8
    }
  ]

  const applyPresetTheme = (preset: typeof presetThemes[0]) => {
    setPrimaryColor(preset.primaryColor)
    setBorderRadius(preset.borderRadius)
  }

  return {
    // 状态
    mode: readonly(mode),
    isDark,
    primaryColor: readonly(primaryColor),
    borderRadius: readonly(borderRadius),
    fontSize: readonly(fontSize),
    fontFamily: readonly(fontFamily),
    state,

    // 动作
    setMode,
    toggleMode,
    setPrimaryColor,
    setBorderRadius,
    setFontSize,
    setFontFamily,
    resetTheme,
    initTheme,
    getThemeConfig,
    setThemeConfig,
    applyPresetTheme,

    // 预设
    presetThemes
  }
})
