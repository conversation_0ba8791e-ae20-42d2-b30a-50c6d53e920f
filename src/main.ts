import { createApp } from 'vue'
import { createPinia } from 'pinia'
import { createI18n } from 'vue-i18n'
import router from './router'
import App from './App.vue'

// 导入样式
import './styles/index.css'

// 导入国际化配置
import { messages, defaultLocale } from './locales'

// 导入主题配置
import { setupTheme } from './core/theme'

// 导入Tauri插件
import { setupTauri } from './core/tauri'

// 创建Vue应用实例
const app = createApp(App)

// 创建Pinia状态管理
const pinia = createPinia()

// 创建国际化实例
const i18n = createI18n({
  legacy: false,
  locale: defaultLocale,
  fallbackLocale: 'zh-CN',
  messages,
  globalInjection: true
})

// 注册插件
app.use(pinia)
app.use(router)
app.use(i18n)

// 初始化主题系统
setupTheme()

// 初始化Tauri配置
setupTauri()

// 挂载应用
app.mount('#app')

// 移除加载动画
const loadingContainer = document.querySelector('.loading-container')
if (loadingContainer) {
  setTimeout(() => {
    loadingContainer.remove()
  }, 1000)
}

// 全局错误处理
app.config.errorHandler = (err, vm, info) => {
  console.error('全局错误:', err, info)
  // 这里可以添加错误上报逻辑
}

// 全局警告处理
app.config.warnHandler = (msg, vm, trace) => {
  console.warn('全局警告:', msg, trace)
}
