import { invoke } from '@tauri-apps/api/core'
import type { ApiResponse } from '@/types'

// 基础API调用封装
export class TauriAPI {
  // 调用Tauri命令
  static async invoke<T = any>(command: string, args?: any): Promise<T> {
    try {
      const result = await invoke<ApiResponse<T>>(command, args)
      
      if (result.success) {
        return result.data as T
      } else {
        throw new Error(result.message || '操作失败')
      }
    } catch (error) {
      console.error(`调用命令 ${command} 失败:`, error)
      throw error
    }
  }

  // 调用返回原始响应的命令
  static async invokeRaw<T = any>(command: string, args?: any): Promise<ApiResponse<T>> {
    try {
      return await invoke<ApiResponse<T>>(command, args)
    } catch (error) {
      console.error(`调用命令 ${command} 失败:`, error)
      throw error
    }
  }

  // 批量调用命令
  static async invokeBatch<T = any>(commands: Array<{ command: string; args?: any }>): Promise<T[]> {
    try {
      const promises = commands.map(({ command, args }) => this.invoke<T>(command, args))
      return await Promise.all(promises)
    } catch (error) {
      console.error('批量调用命令失败:', error)
      throw error
    }
  }

  // 带重试的命令调用
  static async invokeWithRetry<T = any>(
    command: string, 
    args?: any, 
    maxRetries: number = 3,
    delay: number = 1000
  ): Promise<T> {
    let lastError: Error
    
    for (let i = 0; i <= maxRetries; i++) {
      try {
        return await this.invoke<T>(command, args)
      } catch (error) {
        lastError = error as Error
        
        if (i < maxRetries) {
          console.warn(`命令 ${command} 第 ${i + 1} 次调用失败，${delay}ms 后重试:`, error)
          await new Promise(resolve => setTimeout(resolve, delay))
          delay *= 2 // 指数退避
        }
      }
    }
    
    throw lastError!
  }

  // 带超时的命令调用
  static async invokeWithTimeout<T = any>(
    command: string, 
    args?: any, 
    timeout: number = 30000
  ): Promise<T> {
    return new Promise((resolve, reject) => {
      const timer = setTimeout(() => {
        reject(new Error(`命令 ${command} 调用超时 (${timeout}ms)`))
      }, timeout)

      this.invoke<T>(command, args)
        .then(result => {
          clearTimeout(timer)
          resolve(result)
        })
        .catch(error => {
          clearTimeout(timer)
          reject(error)
        })
    })
  }
}

// 错误处理工具
export class APIError extends Error {
  constructor(
    message: string,
    public code?: string,
    public details?: any
  ) {
    super(message)
    this.name = 'APIError'
  }
}

// 响应处理工具
export class ResponseHandler {
  // 处理成功响应
  static success<T>(data: T, message?: string): ApiResponse<T> {
    return {
      success: true,
      data,
      message,
      timestamp: Date.now()
    }
  }

  // 处理错误响应
  static error(message: string, code?: number, details?: any): ApiResponse {
    return {
      success: false,
      message,
      code,
      timestamp: Date.now()
    }
  }

  // 检查响应是否成功
  static isSuccess<T>(response: ApiResponse<T>): response is ApiResponse<T> & { success: true } {
    return response.success === true
  }

  // 提取响应数据
  static extractData<T>(response: ApiResponse<T>): T {
    if (this.isSuccess(response)) {
      return response.data!
    } else {
      throw new APIError(response.message || '操作失败', response.code?.toString())
    }
  }
}

// 请求参数验证
export class RequestValidator {
  // 验证必需参数
  static validateRequired(params: Record<string, any>, requiredFields: string[]) {
    const missing = requiredFields.filter(field => 
      params[field] === undefined || params[field] === null || params[field] === ''
    )
    
    if (missing.length > 0) {
      throw new APIError(`缺少必需参数: ${missing.join(', ')}`)
    }
  }

  // 验证参数类型
  static validateTypes(params: Record<string, any>, typeMap: Record<string, string>) {
    for (const [field, expectedType] of Object.entries(typeMap)) {
      if (params[field] !== undefined) {
        const actualType = typeof params[field]
        if (actualType !== expectedType) {
          throw new APIError(`参数 ${field} 类型错误，期望 ${expectedType}，实际 ${actualType}`)
        }
      }
    }
  }

  // 验证字符串长度
  static validateStringLength(
    value: string, 
    field: string, 
    minLength?: number, 
    maxLength?: number
  ) {
    if (minLength !== undefined && value.length < minLength) {
      throw new APIError(`${field} 长度不能少于 ${minLength} 个字符`)
    }
    
    if (maxLength !== undefined && value.length > maxLength) {
      throw new APIError(`${field} 长度不能超过 ${maxLength} 个字符`)
    }
  }

  // 验证数值范围
  static validateNumberRange(
    value: number, 
    field: string, 
    min?: number, 
    max?: number
  ) {
    if (min !== undefined && value < min) {
      throw new APIError(`${field} 不能小于 ${min}`)
    }
    
    if (max !== undefined && value > max) {
      throw new APIError(`${field} 不能大于 ${max}`)
    }
  }

  // 验证枚举值
  static validateEnum(value: any, field: string, allowedValues: any[]) {
    if (!allowedValues.includes(value)) {
      throw new APIError(`${field} 值无效，允许的值: ${allowedValues.join(', ')}`)
    }
  }
}

// 缓存管理
export class APICache {
  private static cache = new Map<string, { data: any; timestamp: number; ttl: number }>()

  // 设置缓存
  static set(key: string, data: any, ttl: number = 300000) { // 默认5分钟
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl
    })
  }

  // 获取缓存
  static get<T = any>(key: string): T | null {
    const cached = this.cache.get(key)
    
    if (!cached) {
      return null
    }
    
    // 检查是否过期
    if (Date.now() - cached.timestamp > cached.ttl) {
      this.cache.delete(key)
      return null
    }
    
    return cached.data as T
  }

  // 删除缓存
  static delete(key: string) {
    this.cache.delete(key)
  }

  // 清空缓存
  static clear() {
    this.cache.clear()
  }

  // 生成缓存键
  static generateKey(command: string, args?: any): string {
    if (!args) {
      return command
    }
    
    const argsStr = JSON.stringify(args, Object.keys(args).sort())
    return `${command}:${argsStr}`
  }
}

// 带缓存的API调用
export async function invokeWithCache<T = any>(
  command: string,
  args?: any,
  ttl?: number
): Promise<T> {
  const cacheKey = APICache.generateKey(command, args)
  
  // 尝试从缓存获取
  const cached = APICache.get<T>(cacheKey)
  if (cached !== null) {
    return cached
  }
  
  // 调用API
  const result = await TauriAPI.invoke<T>(command, args)
  
  // 缓存结果
  if (ttl !== undefined) {
    APICache.set(cacheKey, result, ttl)
  }
  
  return result
}
