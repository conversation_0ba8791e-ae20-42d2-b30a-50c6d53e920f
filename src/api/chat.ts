import { Tau<PERSON><PERSON><PERSON>, RequestValidator } from './base'
import type { 
  ChatSession, 
  ChatMessage, 
  ChatInput, 
  SessionConfig,
  MessageSearchParams,
  ExportOptions,
  ChatStats
} from '@/types'

// 聊天API接口
export class ChatAPI {
  // 获取会话列表
  static async getSessions(): Promise<ChatSession[]> {
    return TauriAPI.invoke('get_chat_sessions')
  }

  // 创建新会话
  static async createSession(config?: Partial<SessionConfig>): Promise<ChatSession> {
    return TauriAPI.invoke('create_chat_session', { config })
  }

  // 获取会话详情
  static async getSession(sessionId: string): Promise<ChatSession> {
    RequestValidator.validateRequired({ sessionId }, ['sessionId'])
    return TauriAPI.invoke('get_chat_session', { sessionId })
  }

  // 更新会话
  static async updateSession(sessionId: string, updates: Partial<ChatSession>): Promise<ChatSession> {
    RequestValidator.validateRequired({ sessionId }, ['sessionId'])
    return TauriAPI.invoke('update_chat_session', { sessionId, updates })
  }

  // 删除会话
  static async deleteSession(sessionId: string): Promise<void> {
    RequestValidator.validateRequired({ sessionId }, ['sessionId'])
    return TauriAPI.invoke('delete_chat_session', { sessionId })
  }

  // 归档会话
  static async archiveSession(sessionId: string): Promise<void> {
    RequestValidator.validateRequired({ sessionId }, ['sessionId'])
    return TauriAPI.invoke('archive_chat_session', { sessionId })
  }

  // 取消归档会话
  static async unarchiveSession(sessionId: string): Promise<void> {
    RequestValidator.validateRequired({ sessionId }, ['sessionId'])
    return TauriAPI.invoke('unarchive_chat_session', { sessionId })
  }

  // 获取会话消息
  static async getMessages(sessionId: string, limit?: number, offset?: number): Promise<ChatMessage[]> {
    RequestValidator.validateRequired({ sessionId }, ['sessionId'])
    return TauriAPI.invoke('get_chat_messages', { sessionId, limit, offset })
  }

  // 发送消息
  static async sendMessage(sessionId: string, input: ChatInput): Promise<ChatMessage> {
    RequestValidator.validateRequired({ sessionId, input }, ['sessionId', 'input'])
    RequestValidator.validateRequired({ content: input.content }, ['content'])
    
    return TauriAPI.invoke('send_chat_message', { sessionId, input })
  }

  // 开始流式对话
  static async startStreamChat(sessionId: string, input: ChatInput): Promise<string> {
    RequestValidator.validateRequired({ sessionId, input }, ['sessionId', 'input'])
    RequestValidator.validateRequired({ content: input.content }, ['content'])
    
    return TauriAPI.invoke('start_stream_chat', { sessionId, input })
  }

  // 停止流式对话
  static async stopStreamChat(sessionId: string): Promise<void> {
    RequestValidator.validateRequired({ sessionId }, ['sessionId'])
    return TauriAPI.invoke('stop_stream_chat', { sessionId })
  }

  // 重新生成消息
  static async regenerateMessage(messageId: string): Promise<ChatMessage> {
    RequestValidator.validateRequired({ messageId }, ['messageId'])
    return TauriAPI.invoke('regenerate_chat_message', { messageId })
  }

  // 删除消息
  static async deleteMessage(messageId: string): Promise<void> {
    RequestValidator.validateRequired({ messageId }, ['messageId'])
    return TauriAPI.invoke('delete_chat_message', { messageId })
  }

  // 编辑消息
  static async editMessage(messageId: string, content: string): Promise<ChatMessage> {
    RequestValidator.validateRequired({ messageId, content }, ['messageId', 'content'])
    return TauriAPI.invoke('edit_chat_message', { messageId, content })
  }

  // 搜索消息
  static async searchMessages(params: MessageSearchParams): Promise<ChatMessage[]> {
    return TauriAPI.invoke('search_chat_messages', { params })
  }

  // 获取会话配置
  static async getSessionConfig(sessionId: string): Promise<SessionConfig> {
    RequestValidator.validateRequired({ sessionId }, ['sessionId'])
    return TauriAPI.invoke('get_session_config', { sessionId })
  }

  // 更新会话配置
  static async updateSessionConfig(sessionId: string, config: Partial<SessionConfig>): Promise<SessionConfig> {
    RequestValidator.validateRequired({ sessionId }, ['sessionId'])
    return TauriAPI.invoke('update_session_config', { sessionId, config })
  }

  // 导出会话
  static async exportSession(sessionId: string, options: ExportOptions): Promise<string> {
    RequestValidator.validateRequired({ sessionId, options }, ['sessionId', 'options'])
    return TauriAPI.invoke('export_chat_session', { sessionId, options })
  }

  // 导入会话
  static async importSession(filePath: string): Promise<ChatSession> {
    RequestValidator.validateRequired({ filePath }, ['filePath'])
    return TauriAPI.invoke('import_chat_session', { filePath })
  }

  // 清空会话消息
  static async clearSessionMessages(sessionId: string): Promise<void> {
    RequestValidator.validateRequired({ sessionId }, ['sessionId'])
    return TauriAPI.invoke('clear_session_messages', { sessionId })
  }

  // 获取聊天统计
  static async getChatStats(): Promise<ChatStats> {
    return TauriAPI.invoke('get_chat_stats')
  }

  // 获取会话统计
  static async getSessionStats(sessionId: string): Promise<any> {
    RequestValidator.validateRequired({ sessionId }, ['sessionId'])
    return TauriAPI.invoke('get_session_stats', { sessionId })
  }

  // 复制消息
  static async copyMessage(messageId: string): Promise<void> {
    RequestValidator.validateRequired({ messageId }, ['messageId'])
    return TauriAPI.invoke('copy_chat_message', { messageId })
  }

  // 分享消息
  static async shareMessage(messageId: string): Promise<string> {
    RequestValidator.validateRequired({ messageId }, ['messageId'])
    return TauriAPI.invoke('share_chat_message', { messageId })
  }

  // 获取消息详情
  static async getMessageDetails(messageId: string): Promise<ChatMessage> {
    RequestValidator.validateRequired({ messageId }, ['messageId'])
    return TauriAPI.invoke('get_message_details', { messageId })
  }

  // 标记消息为重要
  static async markMessageImportant(messageId: string, important: boolean = true): Promise<void> {
    RequestValidator.validateRequired({ messageId }, ['messageId'])
    return TauriAPI.invoke('mark_message_important', { messageId, important })
  }

  // 添加消息标签
  static async addMessageTag(messageId: string, tag: string): Promise<void> {
    RequestValidator.validateRequired({ messageId, tag }, ['messageId', 'tag'])
    return TauriAPI.invoke('add_message_tag', { messageId, tag })
  }

  // 移除消息标签
  static async removeMessageTag(messageId: string, tag: string): Promise<void> {
    RequestValidator.validateRequired({ messageId, tag }, ['messageId', 'tag'])
    return TauriAPI.invoke('remove_message_tag', { messageId, tag })
  }

  // 获取可用模型列表
  static async getAvailableModels(): Promise<string[]> {
    return TauriAPI.invoke('get_available_chat_models')
  }

  // 获取模型配置
  static async getModelConfig(modelId: string): Promise<any> {
    RequestValidator.validateRequired({ modelId }, ['modelId'])
    return TauriAPI.invoke('get_chat_model_config', { modelId })
  }

  // 验证模型可用性
  static async validateModel(modelId: string): Promise<boolean> {
    RequestValidator.validateRequired({ modelId }, ['modelId'])
    return TauriAPI.invoke('validate_chat_model', { modelId })
  }

  // 获取会话模板
  static async getSessionTemplates(): Promise<any[]> {
    return TauriAPI.invoke('get_session_templates')
  }

  // 从模板创建会话
  static async createSessionFromTemplate(templateId: string): Promise<ChatSession> {
    RequestValidator.validateRequired({ templateId }, ['templateId'])
    return TauriAPI.invoke('create_session_from_template', { templateId })
  }

  // 保存会话为模板
  static async saveSessionAsTemplate(sessionId: string, templateName: string): Promise<void> {
    RequestValidator.validateRequired({ sessionId, templateName }, ['sessionId', 'templateName'])
    return TauriAPI.invoke('save_session_as_template', { sessionId, templateName })
  }
}
