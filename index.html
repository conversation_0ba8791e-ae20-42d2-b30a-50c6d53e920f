<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>AI Studio - 智能AI助手</title>
    <meta name="description" content="AI Studio - 基于 Vue3 + TypeScript + Vite + Tauri 2.x 的中文AI助手桌面应用" />
    
    <!-- 预加载字体 -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- 防止页面闪烁 -->
    <style>
      html {
        background-color: #ffffff;
      }
      html.dark {
        background-color: #0f172a;
      }
      
      /* 加载动画 */
      .loading-container {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 9999;
      }
      
      .loading-spinner {
        width: 50px;
        height: 50px;
        border: 3px solid rgba(255, 255, 255, 0.3);
        border-radius: 50%;
        border-top-color: #fff;
        animation: spin 1s ease-in-out infinite;
      }
      
      @keyframes spin {
        to { transform: rotate(360deg); }
      }
      
      .loading-text {
        color: white;
        font-size: 18px;
        font-weight: 500;
        margin-top: 20px;
        font-family: 'Inter', sans-serif;
      }
    </style>
  </head>
  <body>
    <div id="app">
      <!-- 应用加载时显示的加载动画 -->
      <div class="loading-container">
        <div class="text-center">
          <div class="loading-spinner"></div>
          <div class="loading-text">AI Studio 正在启动...</div>
        </div>
      </div>
    </div>
    <script type="module" src="/src/main.ts"></script>
  </body>
</html>
